module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  moduleFileExtensions: ['js', 'ts', 'json', 'vue'],
  transform: {
    '^.+\\.ts$': 'ts-jest',
    '^.+\\.vue$': '@vue/vue3-jest'
  },
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/client/src/$1',
    '^@server/(.*)$': '<rootDir>/server/$1'
  },
  testMatch: [
    '<rootDir>/client/src/**/*.spec.ts',
    '<rootDir>/client/src/**/*.test.ts',
    '<rootDir>/server/**/*.spec.ts',
    '<rootDir>/server/**/*.test.ts'
  ],
  collectCoverage: true,
  collectCoverageFrom: ['client/src/**/*.{ts,vue}', 'server/**/*.ts', '!**/node_modules/**']
}

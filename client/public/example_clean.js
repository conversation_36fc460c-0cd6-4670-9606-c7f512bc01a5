/**
 * 清洗算法 - 默认
 * @param {Array} data - 输入数据数组 [{"collectionAt": "2025-04-30T09:31:39.000Z","deviceSn": "PDF-2502-20001","heart": 244, "mode": 0, "strkPct": 20, "18b03": 95, "wrmCd": 0, "dpth": 44360, "rtnTq": 44360, "frcstKn": 44360,"wtrPrsH": 72, "rtnSpd": 2490, "advncSpd": 29, "hydrPrs": 1830,"wtrPrsL": 0, "hghWrk": 0, "lwWrk": 44359, "rtnPrs": 140, "frcstPrs": 710, "clctType": 1, "clctSts": 1, "tunnelDpth": 24, "hlNum": 23, "hlAng": null, "rc0": 127, "rc1": 0, "rc2": 0, "rc3": 0, "rc4": 0, "rc5": null, "rc6": null "led_08": 8, "ledAf": 16, "tunnelName": "abc", "diameter": 90}]
 * @returns {Array} 清洗后的数据
 */
function clean(data) {
  if (!Array.isArray(data)) {
    return data
  }

  return data.filter(item => {
    // 检查clctSts字段是否存在且值为1
    return item && item.clctSts === 1
  })
}

/**
 * 清洗算法 - 数据滤波处理
 * @param {Array} data - 数据数组
 * @returns {Array} 清洗后的数据
 */
function filteringClean(data) {
  try {
    if (!Array.isArray(data) || data.length === 0) {
      console.warn('数据清洗: 输入数据为空或格式错误')
      return data
    }

    console.log(`数据清洗开始: 输入数据量 ${data.length} 条`)

    // 需要进行滤波的字段
    const filterFields = ['advncSpd', 'rtnSpd', 'rtnTq', 'wtrPrsH', 'frcstKn']

    // 深拷贝数据，避免修改原始数据
    let cleanedData = JSON.parse(JSON.stringify(data))

    // 对每个字段进行滤波处理
    filterFields.forEach(field => {
      cleanedData = applyDataFilter(cleanedData, field)
    })

    console.log(`数据清洗完成: 输出数据量 ${cleanedData.length} 条`)
    return cleanedData
  } catch (error) {
    console.error('数据清洗过程中发生错误:', error)
    return data // 出错时返回原始数据
  }
}

/**
 * 对指定字段应用数据滤波
 * @param {Array} data - 数据数组
 * @param {string} field - 字段名
 * @returns {Array} 滤波后的数据
 */
function applyDataFilter(data, field) {
  try {
    // 1. 异常值检测和处理
    data = removeOutliers(data, field)

    // 2. 移动平均滤波
    data = movingAverageFilter(data, field, 5) // 5点移动平均

    // 3. 中值滤波
    data = medianFilter(data, field, 3) // 3点中值滤波

    return data
  } catch (error) {
    console.error(`字段 ${field} 滤波处理失败:`, error)
    return data
  }
}

/**
 * 异常值检测和处理 - 使用四分位数方法(IQR)
 * @param {Array} data - 数据数组
 * @param {string} field - 字段名
 * @returns {Array} 处理后的数据
 */
function removeOutliers(data, field) {
  try {
    // 提取有效数值
    const values = data.map(item => Number(item[field])).filter(val => !isNaN(val) && isFinite(val))

    if (values.length < 4) {
      return data // 数据量太少，不进行异常值处理
    }

    // 计算四分位数
    const sortedValues = [...values].sort((a, b) => a - b)
    const q1Index = Math.floor(sortedValues.length * 0.25)
    const q3Index = Math.floor(sortedValues.length * 0.75)
    const q1 = sortedValues[q1Index]
    const q3 = sortedValues[q3Index]
    const iqr = q3 - q1

    // 计算异常值边界
    const lowerBound = q1 - 1.5 * iqr
    const upperBound = q3 + 1.5 * iqr

    console.log(`字段 ${field} 异常值边界: [${lowerBound.toFixed(2)}, ${upperBound.toFixed(2)}]`)

    // 处理异常值
    let outlierCount = 0
    const processedData = data.map((item, index) => {
      const value = Number(item[field])

      if (isNaN(value) || !isFinite(value)) {
        return item // 保持非数值数据不变
      }

      // 检测异常值
      if (value < lowerBound || value > upperBound) {
        outlierCount++

        // 使用邻近值的平均值替换异常值
        const neighborValues = getNeighborValues(data, index, field, 2)
        const replacementValue =
          neighborValues.length > 0
            ? neighborValues.reduce((sum, val) => sum + val, 0) / neighborValues.length
            : q1 + (q3 - q1) / 2 // 如果没有邻近值，使用中位数

        return {
          ...item,
          [field]: Math.round(replacementValue * 100) / 100 // 保留2位小数
        }
      }

      return item
    })

    if (outlierCount > 0) {
      console.log(`字段 ${field} 检测到 ${outlierCount} 个异常值并已处理`)
    }

    return processedData
  } catch (error) {
    console.error(`字段 ${field} 异常值处理失败:`, error)
    return data
  }
}

/**
 * 获取指定索引周围的邻近有效值
 * @param {Array} data - 数据数组
 * @param {number} index - 当前索引
 * @param {string} field - 字段名
 * @param {number} range - 搜索范围
 * @returns {Array} 邻近有效值数组
 */
function getNeighborValues(data, index, field, range = 2) {
  const neighbors = []

  // 向前搜索
  for (let i = Math.max(0, index - range); i < index; i++) {
    const value = Number(data[i][field])
    if (!isNaN(value) && isFinite(value)) {
      neighbors.push(value)
    }
  }

  // 向后搜索
  for (let i = index + 1; i <= Math.min(data.length - 1, index + range); i++) {
    const value = Number(data[i][field])
    if (!isNaN(value) && isFinite(value)) {
      neighbors.push(value)
    }
  }

  return neighbors
}

/**
 * 移动平均滤波
 * @param {Array} data - 数据数组
 * @param {string} field - 字段名
 * @param {number} windowSize - 窗口大小
 * @returns {Array} 滤波后的数据
 */
function movingAverageFilter(data, field, windowSize = 5) {
  try {
    if (data.length < windowSize) {
      return data // 数据量不足，不进行滤波
    }

    const filteredData = [...data]
    const halfWindow = Math.floor(windowSize / 2)

    for (let i = halfWindow; i < data.length - halfWindow; i++) {
      const windowValues = []

      // 收集窗口内的有效值
      for (let j = i - halfWindow; j <= i + halfWindow; j++) {
        const value = Number(data[j][field])
        if (!isNaN(value) && isFinite(value)) {
          windowValues.push(value)
        }
      }

      // 计算平均值
      if (windowValues.length > 0) {
        const average = windowValues.reduce((sum, val) => sum + val, 0) / windowValues.length
        filteredData[i] = {
          ...filteredData[i],
          [field]: Math.round(average * 100) / 100 // 保留2位小数
        }
      }
    }

    console.log(`字段 ${field} 移动平均滤波完成 (窗口大小: ${windowSize})`)
    return filteredData
  } catch (error) {
    console.error(`字段 ${field} 移动平均滤波失败:`, error)
    return data
  }
}

/**
 * 中值滤波
 * @param {Array} data - 数据数组
 * @param {string} field - 字段名
 * @param {number} windowSize - 窗口大小
 * @returns {Array} 滤波后的数据
 */
function medianFilter(data, field, windowSize = 3) {
  try {
    if (data.length < windowSize) {
      return data // 数据量不足，不进行滤波
    }

    const filteredData = [...data]
    const halfWindow = Math.floor(windowSize / 2)

    for (let i = halfWindow; i < data.length - halfWindow; i++) {
      const windowValues = []

      // 收集窗口内的有效值
      for (let j = i - halfWindow; j <= i + halfWindow; j++) {
        const value = Number(data[j][field])
        if (!isNaN(value) && isFinite(value)) {
          windowValues.push(value)
        }
      }

      // 计算中值
      if (windowValues.length > 0) {
        windowValues.sort((a, b) => a - b)
        const median =
          windowValues.length % 2 === 0
            ? (windowValues[windowValues.length / 2 - 1] + windowValues[windowValues.length / 2]) /
              2
            : windowValues[Math.floor(windowValues.length / 2)]

        filteredData[i] = {
          ...filteredData[i],
          [field]: Math.round(median * 100) / 100 // 保留2位小数
        }
      }
    }

    console.log(`字段 ${field} 中值滤波完成 (窗口大小: ${windowSize})`)
    return filteredData
  } catch (error) {
    console.error(`字段 ${field} 中值滤波失败:`, error)
    return data
  }
}

/**
 * 高级数据清洗函数 - 提供更多配置选项
 * @param {Array} data - 输入数据数组
 * @param {Object} options - 配置选项
 * @returns {Array} 清洗后的数据
 */
function advancedClean(data, options = {}) {
  try {
    const defaultOptions = {
      // 需要滤波的字段
      filterFields: ['advncSpd', 'rtnSpd', 'rtnTq', 'wtrPrsH', 'frcstKn'],
      // 异常值检测配置
      outlierDetection: {
        enabled: true,
        method: 'iqr', // 'iqr' 或 'zscore'
        threshold: 1.5 // IQR倍数或Z-score阈值
      },
      // 移动平均滤波配置
      movingAverage: {
        enabled: true,
        windowSize: 5
      },
      // 中值滤波配置
      medianFilter: {
        enabled: true,
        windowSize: 3
      },
      // 卡尔曼滤波配置
      kalmanFilter: {
        enabled: false,
        processNoise: 0.1,
        measurementNoise: 0.1
      }
    }

    const config = { ...defaultOptions, ...options }

    if (!Array.isArray(data) || data.length === 0) {
      console.warn('高级数据清洗: 输入数据为空或格式错误')
      return data
    }

    console.log(`高级数据清洗开始: 输入数据量 ${data.length} 条`)
    console.log('清洗配置:', config)

    let cleanedData = JSON.parse(JSON.stringify(data))

    // 对每个字段进行滤波处理
    config.filterFields.forEach(field => {
      console.log(`开始处理字段: ${field}`)

      // 1. 异常值检测和处理
      if (config.outlierDetection.enabled) {
        if (config.outlierDetection.method === 'zscore') {
          cleanedData = removeOutliersZScore(cleanedData, field, config.outlierDetection.threshold)
        } else {
          cleanedData = removeOutliers(cleanedData, field)
        }
      }

      // 2. 移动平均滤波
      if (config.movingAverage.enabled) {
        cleanedData = movingAverageFilter(cleanedData, field, config.movingAverage.windowSize)
      }

      // 3. 中值滤波
      if (config.medianFilter.enabled) {
        cleanedData = medianFilter(cleanedData, field, config.medianFilter.windowSize)
      }

      // 4. 卡尔曼滤波 (可选)
      if (config.kalmanFilter.enabled) {
        cleanedData = kalmanFilter(cleanedData, field, config.kalmanFilter)
      }
    })

    console.log(`高级数据清洗完成: 输出数据量 ${cleanedData.length} 条`)
    return cleanedData
  } catch (error) {
    console.error('高级数据清洗过程中发生错误:', error)
    return data
  }
}

/**
 * 基于Z-score的异常值检测和处理
 * @param {Array} data - 数据数组
 * @param {string} field - 字段名
 * @param {number} threshold - Z-score阈值
 * @returns {Array} 处理后的数据
 */
function removeOutliersZScore(data, field, threshold = 2.5) {
  try {
    // 提取有效数值
    const values = data.map(item => Number(item[field])).filter(val => !isNaN(val) && isFinite(val))

    if (values.length < 3) {
      return data
    }

    // 计算均值和标准差
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length
    const stdDev = Math.sqrt(variance)

    console.log(`字段 ${field} Z-score统计: 均值=${mean.toFixed(2)}, 标准差=${stdDev.toFixed(2)}`)

    // 处理异常值
    let outlierCount = 0
    const processedData = data.map((item, index) => {
      const value = Number(item[field])

      if (isNaN(value) || !isFinite(value)) {
        return item
      }

      // 计算Z-score
      const zScore = Math.abs((value - mean) / stdDev)

      if (zScore > threshold) {
        outlierCount++

        // 使用邻近值的平均值替换异常值
        const neighborValues = getNeighborValues(data, index, field, 2)
        const replacementValue =
          neighborValues.length > 0
            ? neighborValues.reduce((sum, val) => sum + val, 0) / neighborValues.length
            : mean

        return {
          ...item,
          [field]: Math.round(replacementValue * 100) / 100
        }
      }

      return item
    })

    if (outlierCount > 0) {
      console.log(`字段 ${field} Z-score方法检测到 ${outlierCount} 个异常值并已处理`)
    }

    return processedData
  } catch (error) {
    console.error(`字段 ${field} Z-score异常值处理失败:`, error)
    return data
  }
}

/**
 * 简单卡尔曼滤波实现
 * @param {Array} data - 数据数组
 * @param {string} field - 字段名
 * @param {Object} config - 卡尔曼滤波配置
 * @returns {Array} 滤波后的数据
 */
function kalmanFilter(data, field, config = {}) {
  try {
    const { processNoise = 0.1, measurementNoise = 0.1 } = config

    const filteredData = [...data]
    let estimate = Number(data[0][field]) || 0
    let errorCovariance = 1.0

    for (let i = 0; i < data.length; i++) {
      const measurement = Number(data[i][field])

      if (isNaN(measurement) || !isFinite(measurement)) {
        continue
      }

      // 预测步骤
      const predictedEstimate = estimate
      const predictedErrorCovariance = errorCovariance + processNoise

      // 更新步骤
      const kalmanGain = predictedErrorCovariance / (predictedErrorCovariance + measurementNoise)
      estimate = predictedEstimate + kalmanGain * (measurement - predictedEstimate)
      errorCovariance = (1 - kalmanGain) * predictedErrorCovariance

      filteredData[i] = {
        ...filteredData[i],
        [field]: Math.round(estimate * 100) / 100
      }
    }

    console.log(`字段 ${field} 卡尔曼滤波完成`)
    return filteredData
  } catch (error) {
    console.error(`字段 ${field} 卡尔曼滤波失败:`, error)
    return data
  }
}

/**
 * 数据质量评估
 * @param {Array} originalData - 原始数据
 * @param {Array} cleanedData - 清洗后数据
 * @param {Array} fields - 评估字段
 * @returns {Object} 质量评估报告
 */
function assessDataQuality(
  originalData,
  cleanedData,
  fields = ['advncSpd', 'rtnSpd', 'rtnTq', 'wtrPrsH', 'frcstKn']
) {
  try {
    const report = {
      totalRecords: originalData.length,
      processedRecords: cleanedData.length,
      fieldReports: {}
    }

    fields.forEach(field => {
      const originalValues = originalData
        .map(item => Number(item[field]))
        .filter(val => !isNaN(val) && isFinite(val))

      const cleanedValues = cleanedData
        .map(item => Number(item[field]))
        .filter(val => !isNaN(val) && isFinite(val))

      if (originalValues.length === 0) {
        report.fieldReports[field] = { error: '无有效数据' }
        return
      }

      // 计算统计指标
      const originalMean = originalValues.reduce((sum, val) => sum + val, 0) / originalValues.length
      const cleanedMean = cleanedValues.reduce((sum, val) => sum + val, 0) / cleanedValues.length

      const originalStd = Math.sqrt(
        originalValues.reduce((sum, val) => sum + Math.pow(val - originalMean, 2), 0) /
          originalValues.length
      )
      const cleanedStd = Math.sqrt(
        cleanedValues.reduce((sum, val) => sum + Math.pow(val - cleanedMean, 2), 0) /
          cleanedValues.length
      )

      report.fieldReports[field] = {
        originalMean: Math.round(originalMean * 100) / 100,
        cleanedMean: Math.round(cleanedMean * 100) / 100,
        originalStd: Math.round(originalStd * 100) / 100,
        cleanedStd: Math.round(cleanedStd * 100) / 100,
        noiseReduction: Math.round(((originalStd - cleanedStd) / originalStd) * 100 * 100) / 100,
        dataIntegrity: Math.round((cleanedValues.length / originalValues.length) * 100 * 100) / 100
      }
    })

    return report
  } catch (error) {
    console.error('数据质量评估失败:', error)
    return { error: error.message }
  }
}

/**
 * 导出清洗配置预设
 */
const FILTER_PRESETS = {
  // 轻度清洗 - 适用于数据质量较好的场景
  light: {
    filterFields: ['advncSpd', 'rtnSpd', 'rtnTq', 'wtrPrsH', 'frcstKn'],
    outlierDetection: { enabled: true, method: 'iqr', threshold: 2.0 },
    movingAverage: { enabled: false },
    medianFilter: { enabled: true, windowSize: 3 },
    kalmanFilter: { enabled: false }
  },

  // 标准清洗 - 默认推荐配置
  standard: {
    filterFields: ['advncSpd', 'rtnSpd', 'rtnTq', 'wtrPrsH', 'frcstKn'],
    outlierDetection: { enabled: true, method: 'iqr', threshold: 1.5 },
    movingAverage: { enabled: true, windowSize: 5 },
    medianFilter: { enabled: true, windowSize: 3 },
    kalmanFilter: { enabled: false }
  },

  // 强力清洗 - 适用于噪声较大的数据
  aggressive: {
    filterFields: ['advncSpd', 'rtnSpd', 'rtnTq', 'wtrPrsH', 'frcstKn'],
    outlierDetection: { enabled: true, method: 'zscore', threshold: 2.0 },
    movingAverage: { enabled: true, windowSize: 7 },
    medianFilter: { enabled: true, windowSize: 5 },
    kalmanFilter: { enabled: true, processNoise: 0.1, measurementNoise: 0.1 }
  }
}

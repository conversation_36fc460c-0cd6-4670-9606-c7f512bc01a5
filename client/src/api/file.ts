/**
 * 文件相关API接口
 * 包含文件管理、上传下载、数据查询等功能
 */

import request from '@/utils/request'
import type { FileListResponse, FileDetailResponse, StatsResponse, ApiResponse, PaginationParams } from '@/types/api'

// 文件列表查询参数
export interface FileListParams extends PaginationParams {
  deviceSn?: string
  fileName?: string
  startDate?: string
  endDate?: string
  fileType?: string
}

// 设备文件项
export interface DeviceFileItem {
  value: string
  label: string
  fileSize: number
  filePath: string
  createdAt: string
}

// 原始数据查询参数
export interface RawDataQueryParams {
  deviceId: string
  startTime?: string
  endTime?: string
  holeNo?: string
  page?: number
  pageSize?: number
}

// 文件上传参数
export interface FileUploadParams {
  file: File
  deviceId?: string
  description?: string
}

// 文件更新参数
export interface FileUpdateParams {
  fileName?: string
  description?: string
  tags?: string[]
}

/**
 * 获取文件和设备统计数据
 * @returns 统计数据响应
 */
export const getStats = (): Promise<StatsResponse> => {
  return request({
    url: '/files/stats',
    method: 'get'
  })
}

/**
 * 获取文件列表
 * @param params 查询参数
 * @returns 文件列表响应
 */
export const getFileList = (params: FileListParams = {}): Promise<FileListResponse> => {
  return request({
    url: '/files',
    method: 'get',
    params
  })
}

/**
 * 获取设备文件列表
 * @param deviceId 设备ID
 * @returns 设备文件列表响应
 */
export const getDeviceFiles = (
  deviceId: string
): Promise<ApiResponse<DeviceFileItem[]>> => {
  return request({
    url: `/files/device/${deviceId}`,
    method: 'get'
  })
}

/**
 * 获取文件详情
 * @param id 文件ID
 * @returns 文件详情响应
 */
export const getFileDetail = (id: string | number): Promise<FileDetailResponse> => {
  return request({
    url: `/files/${id}`,
    method: 'get'
  })
}

/**
 * 上传文件
 * @param data 文件上传数据
 * @returns 上传结果响应
 */
export const uploadFile = (data: FileUploadParams | FormData): Promise<ApiResponse<unknown>> => {
  return request({
    url: '/files',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 更新文件信息
 * @param id 文件ID
 * @param data 更新数据
 * @returns 更新结果响应
 */
export const updateFile = (
  id: string | number,
  data: FileUpdateParams
): Promise<ApiResponse<unknown>> => {
  return request({
    url: `/files/${id}`,
    method: 'put',
    data
  })
}

/**
 * 获取文件原始数据
 * @param fileId 文件ID
 * @returns 原始数据响应
 */
export const getFileOriginalData = (fileId: string | number): Promise<ApiResponse<unknown>> => {
  if (!fileId) {
    const error = new Error('缺少文件ID参数')
    console.error('getFileOriginalData:', error.message)
    return Promise.reject(error)
  }

  console.log(`准备请求文件原始数据，fileId: ${fileId}`)
  return request({
    url: `/files/${fileId}/original-data`,
    method: 'get',
    timeout: 60000 // 设置更长的超时时间: 60秒
  }).catch(error => {
    console.error(`获取文件原始数据失败，fileId: ${fileId}`, error)
    throw error
  })
}

/**
 * 根据条件查询原始数据
 * @param params 查询参数，包含deviceId（必选）、startTime、endTime、holeNo（可选）
 * @returns 原始数据响应
 */
export const getRawData = (params: RawDataQueryParams): Promise<ApiResponse<unknown>> => {
  return request({
    url: '/data/raw-query',
    method: 'get',
    params,
    timeout: 60000 // 设置更长的超时时间: 60秒
  })
}

/**
 * 删除文件
 * @param id 文件ID
 * @returns 删除结果响应
 */
export const deleteFile = (id: string | number): Promise<ApiResponse<unknown>> => {
  return request({
    url: `/files/${id}`,
    method: 'delete'
  })
}

// 导出文件API对象（保持向后兼容）
export const fileApi = {
  getStats,
  getFileList,
  getDeviceFiles,
  getFileDetail,
  uploadFile,
  updateFile,
  deleteFile,
  getFileOriginalData,
  getRawData
}

// 默认导出（推荐使用命名导出）
export default fileApi

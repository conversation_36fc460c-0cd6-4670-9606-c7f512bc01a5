/**
 * 认证相关API接口
 * 包含登录、登出、钉钉登录等认证功能
 */

import request from '@/utils/request'
import type { LoginRequest, LoginResponse, LogoutResponse } from '@/types/api'

// 钉钉登录请求参数
export interface DingTalkLoginRequest {
  code: string
  state?: string
}

// 用户信息验证响应
export interface UserVerifyResponse {
  valid: boolean
  user?: {
    id: string
    username: string
    name: string
    role: string
  }
}

/**
 * 用户登录
 * @param data 登录请求参数
 * @returns 登录响应数据
 */
export const login = (data: LoginRequest): Promise<LoginResponse> => {
  return request({
    url: '/users/login',
    method: 'post',
    data
  })
}

/**
 * 用户登出
 * @returns 登出响应数据
 */
export const logout = (): Promise<LogoutResponse> => {
  return request({
    url: '/users/logout',
    method: 'post'
  })
}

/**
 * 钉钉登录
 * @param data 钉钉登录请求参数
 * @returns 登录响应数据
 */
export const dingTalkLogin = (data: DingTalkLoginRequest | string): Promise<LoginResponse> => {
  // 兼容旧的字符串参数格式
  const requestData = typeof data === 'string' ? { code: data } : data

  return request({
    url: '/users/dingtalk-login',
    method: 'post',
    data: requestData
  })
}

/**
 * 验证用户登录状态
 * @returns 用户验证响应
 */
export const verifyUser = (): Promise<UserVerifyResponse> => {
  return request({
    url: '/users/verify',
    method: 'get'
  })
}

/**
 * 刷新用户信息
 * @returns 用户信息
 */
export const refreshUserInfo = (): Promise<LoginResponse> => {
  return request({
    url: '/users/info',
    method: 'get'
  })
}

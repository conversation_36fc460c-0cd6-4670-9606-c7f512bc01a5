import request from '@/utils/request'
import type { AxiosRequestHeaders } from 'axios'

// 定义基础接口
interface ChartComponent {
  id: number;
  name: string;
  type: string;
  component: string;
  description?: string;
  config?: string;
  status: number;
}

interface DeviceChartConfig {
  id: number;
  deviceId?: number;
  chartComponentId?: number;
  configId?: number;
  enabled?: boolean;
  functionNames?: string[]; // 新增：所有函数名数组
  [key: string]: any;
}

// 参数接口定义
interface ChartConfigParams {
  [key: string]: any;
}

interface ChartConfigData {
  deviceId: number;
  chartComponentId: number;
  [key: string]: any;
}

// 请求返回值接口
interface RequestResponse<T> {
  success: boolean;
  message?: string;
  data: T;
}

// 获取所有可用图表组件
export const getAllCharts = (params: ChartConfigParams = {}): Promise<RequestResponse<{ list: ChartComponent[]; total: number }>> => {
  return request({
    url: '/chart-components',
    method: 'get',
    params,
    headers: {} as AxiosRequestHeaders
  })
}

// 获取图表组件详情
export function getChartDetail(id: number): Promise<RequestResponse<ChartComponent>> {
  return request({
    url: `/chart-components/${id}`,
    method: 'get',
    headers: {} as AxiosRequestHeaders
  })
}

// 获取某个设备的图表组件配置
export const getDeviceChartConfigs = (deviceId: number | string): Promise<RequestResponse<DeviceChartConfig[]>> => {
  return request({
    url: `/device-chart-configs/device/${deviceId}`,
    method: 'get',
    headers: {} as AxiosRequestHeaders
  })
}

// 获取设备的图表组件配置状态（带启用状态的所有组件）
export const getDeviceChartConfigStatus = (deviceId: number | string): Promise<RequestResponse<{ list: DeviceChartConfig[]; total: number }>> => {
  return request({
    url: `/device-chart-configs/device/${deviceId}/status`,
    method: 'get',
    headers: {} as AxiosRequestHeaders
  })
}

// 获取设备已启用的图表组件（专用于设备详情页）
export const getDeviceEnabledCharts = (deviceId: number | string): Promise<RequestResponse<{ list: DeviceChartConfig[]; total: number }>> => {
  return request({
    url: `/device-chart-configs/device/${deviceId}/enabled`,
    method: 'get',
    headers: {} as AxiosRequestHeaders
  })
}

// 创建设备图表组件配置
export const createDeviceChartConfig = (data: ChartConfigData): Promise<RequestResponse<DeviceChartConfig>> => {
  return request({
    url: '/device-chart-configs',
    method: 'post',
    data,
    headers: {} as AxiosRequestHeaders
  })
}

// 更新设备图表组件配置
export function updateDeviceChartConfig(id: number, data: Partial<ChartConfigData>): Promise<RequestResponse<DeviceChartConfig>> {
  return request({
    url: `/device-chart-configs/${id}`,
    method: 'put',
    data,
    headers: {} as AxiosRequestHeaders
  })
}

// 删除设备图表组件配置
export const deleteDeviceChartConfig = (configId: number): Promise<RequestResponse<any>> => {
  return request({
    url: `/device-chart-configs/${configId}`,
    method: 'delete',
    headers: {} as AxiosRequestHeaders
  })
}

/* 
// 批量更新设备图表配置 - 后端尚未实现
export const batchUpdateDeviceChartConfigs = (deviceId: number, data: any): Promise<RequestResponse<any>> => {
  return request({
    url: `/devices/${deviceId}/chart-configs/batch`,
    method: 'post',
    data,
    headers: {} as AxiosRequestHeaders
  })
}
*/ 
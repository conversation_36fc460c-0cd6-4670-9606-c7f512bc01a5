import request from '@/utils/request'
import type { AxiosRequestHeaders } from 'axios'

/**
 * 模板相关的API接口
 */

// 模板数据类型定义
export interface Template {
  id: number
  name: string
  description?: string
  config: string
  status: number
  createdAt: string
  updatedAt: string
}

export interface TemplateListParams {
  page?: number
  pageSize?: number
  name?: string
  status?: number
}

export interface TemplateListResponse {
  success: boolean
  data: {
    total: number
    list: Template[]
  }
  message?: string
}

export interface TemplateDetailResponse {
  success: boolean
  data: Template
  message?: string
}

export interface CreateTemplateRequest {
  name: string
  description?: string
  config: string
  status: number
}

export interface UpdateTemplateRequest {
  name?: string
  description?: string
  config?: string
  status?: number
}

export interface TemplateResponse {
  success: boolean
  data?: Template
  message?: string
}

/**
 * 获取模板列表
 */
export const getTemplateList = (params: TemplateListParams): Promise<TemplateListResponse> => {
  return request({
    url: '/templates',
    method: 'get',
    params,
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 获取模板详情
 */
export const getTemplateDetail = (id: string | number): Promise<TemplateDetailResponse> => {
  return request({
    url: `/templates/${id}`,
    method: 'get',
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 创建模板
 */
export const createTemplate = (data: CreateTemplateRequest): Promise<TemplateResponse> => {
  return request({
    url: '/templates',
    method: 'post',
    data,
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 更新模板
 */
export const updateTemplate = (id: string | number, data: UpdateTemplateRequest): Promise<TemplateResponse> => {
  return request({
    url: `/templates/${id}`,
    method: 'put',
    data,
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 删除模板
 */
export const deleteTemplate = (id: string | number): Promise<TemplateResponse> => {
  return request({
    url: `/templates/${id}`,
    method: 'delete',
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 批量删除模板
 */
export const batchDeleteTemplates = (ids: number[]): Promise<TemplateResponse> => {
  return request({
    url: '/templates/batch-delete',
    method: 'post',
    data: { ids },
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 更新模板状态
 */
export const updateTemplateStatus = (id: string | number, status: number): Promise<TemplateResponse> => {
  return request({
    url: `/templates/${id}/status`,
    method: 'patch',
    data: { status },
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 复制模板
 */
export const copyTemplate = (id: string | number, name: string): Promise<TemplateResponse> => {
  return request({
    url: `/templates/${id}/copy`,
    method: 'post',
    data: { name },
    headers: {} as AxiosRequestHeaders
  })
}

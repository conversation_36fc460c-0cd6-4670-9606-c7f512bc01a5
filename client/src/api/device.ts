/**
 * 设备相关API接口
 * 包含设备管理、算法关联、数字岩芯等功能
 */

import request from '@/utils/request'
import type { Device, PaginatedResponse, ApiResponse } from '@/types/api'

// 设备列表请求参数
export interface DeviceListParams {
  page?: number
  pageSize?: number
  type?: string
  name?: string
  serialNumber?: string
  keyword?: string
  status?: string
}

// 设备算法关联参数
export interface DeviceArithmeticParams {
  deviceIds: number[]
  arithmeticId: number
  arithmeticType: number
}

// 设备更新参数
export interface DeviceUpdateParams {
  deviceName?: string
  serialNumber?: string
  macAddress?: string
  deviceSecret?: string
}

// 数字岩芯数据项
export interface DigitalCoreItem {
  fileId: number
  fileName: string
  imageName: string
  fileSize: number
  holeNo: string
  startTime: string
  endTime: string
  createdAt: string
  dataCount: number
  startDepth: number | null
  endDepth: number | null
}

/**
 * 获取设备列表
 * @param params 查询参数
 * @returns 设备列表响应
 */
export const getDeviceList = (params: DeviceListParams = {}): Promise<ApiResponse<PaginatedResponse<Device>>> => {
  return request({
    url: '/devices',
    method: 'get',
    params
  })
}

/**
 * 获取设备详情
 * @param id 设备ID
 * @returns 设备详情响应
 */
export const getDeviceDetail = (id: string): Promise<ApiResponse<Device>> => {
  return request({
    url: `/devices/${id}`,
    method: 'get'
  })
}

/**
 * 获取设备关联的算法
 * @param deviceId 设备ID
 * @returns 设备算法关联信息
 */
export const getDeviceArithmetic = (deviceId: string): Promise<ApiResponse<unknown>> => {
  return request({
    url: `/device-arithmetics/${deviceId}`,
    method: 'get'
  })
}

/**
 * 设置设备关联算法
 * @param deviceId 设备ID
 * @param data 算法关联数据
 * @returns 设置结果
 */
export const setDeviceArithmetic = (
  deviceId: string,
  data: DeviceArithmeticParams
): Promise<ApiResponse<unknown>> => {
  return request({
    url: `/device-arithmetics/${deviceId}`,
    method: 'post',
    data
  })
}

/**
 * 删除设备关联算法
 * @param deviceId 设备ID
 * @param arithmeticType 算法类型
 * @returns 删除结果
 */
export const deleteDeviceArithmetic = (
  deviceId: string,
  arithmeticType: number
): Promise<ApiResponse<unknown>> => {
  return request({
    url: `/device-arithmetics/${deviceId}/${arithmeticType}`,
    method: 'delete'
  })
}

/**
 * 批量设置设备关联算法
 * @param data 批量设置参数
 * @returns 设置结果
 */
export const batchSetDeviceArithmetic = (
  data: DeviceArithmeticParams
): Promise<ApiResponse<unknown>> => {
  return request({
    url: `/device-arithmetics/batch`,
    method: 'post',
    data
  })
}

/**
 * 批量取消设备算法应用
 * @param data 批量取消参数
 * @returns 取消结果
 */
export const batchDeleteDeviceArithmetic = (
  data: DeviceArithmeticParams
): Promise<ApiResponse<unknown>> => {
  return request({
    url: `/device-arithmetics/batch`,
    method: 'delete',
    data
  })
}

/**
 * 获取设备算法设置历史记录
 */
export function getDeviceArithmeticRecords(
  deviceId: string,
  params?: { startDate?: string; endDate?: string; page?: number; pageSize?: number }
) {
  return request({
    url: `/device-arithmetics/records/${deviceId}`,
    method: 'get',
    params,
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 获取设备列表（包含算法信息）
 */
export function getDeviceListWithAlgorithm(params: {
  page?: number
  pageSize?: number
  type?: string
  name?: string
  serialNumber?: string
  algorithmType?: number
  arithmeticId?: number
}): Promise<{
  success: boolean
  data: {
    total: number
    list: Device[]
  }
}> {
  return request({
    url: '/devices/with-algorithm',
    method: 'get',
    params,
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 更新设备信息
 */
export const updateDevice = (
  id: string,
  data: {
    deviceName?: string
    serialNumber?: string
    macAddress?: string
    deviceSecret?: string
  }
): Promise<{
  success: boolean
  data: Device
  message?: string
}> => {
  return request({
    url: `/devices/${id}`,
    method: 'put',
    data,
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 获取设备数字岩芯数据
 */
export const getDeviceDigitalCore = (
  deviceId: string,
  params: {
    page?: number
    pageSize?: number
  } = {}
): Promise<{
  success: boolean
  data: {
    total: number
    list: Array<{
      fileId: number
      fileName: string
      imageName: string
      fileSize: number
      holeNo: string
      startTime: string
      endTime: string
      createdAt: string
      dataCount: number
      startDepth: number | null
      endDepth: number | null
    }>
  }
}> => {
  return request({
    url: `/devices/${deviceId}/digital-core`,
    method: 'get',
    params,
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 获取设备数字岩芯详情
 */
export const getDeviceDigitalCoreDetail = (
  deviceId: string,
  fileId: string
): Promise<{
  success: boolean
  data: {
    fileId: number
    fileName: string
    imageName: string
    fileSize: number
    holeNo: string
    startTime: string
    endTime: string
    createdAt: string
    dataCount: number
    startDepth: number | null
    endDepth: number | null
  }
}> => {
  return request({
    url: `/devices/${deviceId}/digital-core/${fileId}`,
    method: 'get',
    headers: {} as AxiosRequestHeaders
  })
}

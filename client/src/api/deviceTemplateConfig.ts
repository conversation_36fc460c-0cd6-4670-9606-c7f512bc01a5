import request from '@/utils/request'
import type { AxiosRequestHeaders } from 'axios'

/**
 * 设备模板配置相关的API接口
 */

// 设备模板配置数据类型定义
export interface DeviceTemplateConfig {
  id: number
  device_id: number
  template_id: number
  created_at: string
  modified_at?: string
  template?: {
    id: number
    name: string
    description?: string
    config?: string
    status: number
  }
}

export interface DeviceTemplateConfigListResponse {
  success: boolean
  data: {
    total: number
    list: DeviceTemplateConfig[]
  }
  message?: string
}

export interface DeviceTemplateConfigResponse {
  success: boolean
  data?: DeviceTemplateConfig
  message?: string
}

/**
 * 获取设备的模板配置列表
 */
export const getDeviceTemplateConfigs = (deviceId: number): Promise<DeviceTemplateConfigListResponse> => {
  return request({
    url: `/device-template-configs/device/${deviceId}`,
    method: 'get',
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 获取设备启用的模板列表
 */
export const getDeviceEnabledTemplates = (deviceId: number): Promise<DeviceTemplateConfigListResponse> => {
  return request({
    url: `/device-template-configs/device/${deviceId}/enabled`,
    method: 'get',
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 获取模板绑定的设备数量
 */
export const getTemplateDeviceCount = (templateId: number): Promise<{ success: boolean; data: { count: number } }> => {
  return request({
    url: `/device-template-configs/template/${templateId}/count`,
    method: 'get',
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 添加设备模板配置
 */
export const addDeviceTemplateConfig = (deviceId: number, templateId: number): Promise<DeviceTemplateConfigResponse> => {
  return request({
    url: `/device-template-configs/device/${deviceId}`,
    method: 'post',
    data: { templateId: templateId },
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 删除设备模板配置
 */
export const deleteDeviceTemplateConfig = (configId: number): Promise<DeviceTemplateConfigResponse> => {
  return request({
    url: `/device-template-configs/${configId}`,
    method: 'delete',
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 解除设备与模板的绑定
 */
export const unbindDeviceTemplate = (deviceId: number, templateId: number): Promise<DeviceTemplateConfigResponse> => {
  return request({
    url: `/device-template-configs/device/${deviceId}/template/${templateId}`,
    method: 'delete',
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 批量配置设备模板
 */
export const batchConfigDeviceTemplates = (
  deviceId: number,
  templateIds: number[]
): Promise<DeviceTemplateConfigResponse> => {
  return request({
    url: `/device-template-configs/device/${deviceId}/batch`,
    method: 'post',
    data: { templateIds: templateIds },
    headers: {} as AxiosRequestHeaders
  })
}



/**
 * 批量获取多个设备的模板配置状态
 */
export const getBatchDeviceTemplateConfigs = (
  deviceIds: number[],
  templateId?: number
): Promise<{
  success: boolean
  data: Array<{
    deviceId: number
    hasTemplate: boolean
    isCurrentTemplate: boolean
    configs: any[]
  }>
}> => {
  return request({
    url: '/device-template-configs/batch-configs',
    method: 'post',
    data: { deviceIds, templateId },
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 批量绑定多个设备到模板
 */
export const batchBindDevicesToTemplate = (
  deviceIds: number[],
  templateId: number
): Promise<DeviceTemplateConfigResponse> => {
  return request({
    url: `/device-template-configs/template/${templateId}/batch-bind`,
    method: 'post',
    data: { deviceIds },
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 批量解绑多个设备与模板的关系
 */
export const batchUnbindDevicesFromTemplate = (
  deviceIds: number[],
  templateId: number
): Promise<DeviceTemplateConfigResponse> => {
  return request({
    url: `/device-template-configs/template/${templateId}/batch-unbind`,
    method: 'post',
    data: { deviceIds },
    headers: {} as AxiosRequestHeaders
  })
}

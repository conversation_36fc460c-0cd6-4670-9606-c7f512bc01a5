/**
 * 数据相关API接口
 * 包含钻进数据查询、算法处理、字段映射等功能
 */

import request from '@/utils/request'
import type { ApiResponse } from '@/types/api'

// 钻进数据查询参数
export interface DrillingDataQueryParams {
  deviceId: string
  fileId?: string
  startTime?: string
  endTime?: string
  holeNo?: string
  limit?: number
  offset?: number
}

// 算法数据查询参数
export interface AlgorithmDataQueryParams extends DrillingDataQueryParams {
  functions: string // 算法方法名称，多个用逗号分隔
}

// 深度范围查询参数
export interface DepthRangeQueryParams {
  deviceId: string
  holeNo: string
  startDepth: number
  endDepth: number
}

// 钻进数据项
export interface DrillingDataItem {
  id?: number
  deviceId: string
  collectionAt: string
  dpth?: number
  advncSpd?: number
  rtnTq?: number
  wtrPrsH?: number
  frcstKn?: number
  rtnSpd?: number
  [key: string]: unknown
}

// 字段映射项
export interface FieldMapping {
  field: string
  description: string
  unit?: string
  type?: string
}

/**
 * 统一查询钻进数据
 * @param params 查询参数，包含设备ID，可选的文件ID/时间范围/孔号
 * @returns 钻进数据响应
 */
export const queryDrillingData = (
  params: DrillingDataQueryParams
): Promise<ApiResponse<DrillingDataItem[]>> => {
  return request({
    url: '/data/query',
    method: 'post',
    data: params,
    timeout: 300000, // 5分钟超时时间
    maxContentLength: Infinity // 确保没有响应大小限制
  })
}

/**
 * 查询原始数据并自动调用指定算法
 * @param params 查询参数，包含设备ID、算法方法名称，可选的文件ID/时间范围/孔号
 * @returns 算法处理后的数据响应
 */
export const queryDataWithAlgorithms = (
  params: AlgorithmDataQueryParams
): Promise<ApiResponse<unknown>> => {
  return request({
    url: '/data/query-with-algorithms',
    method: 'post',
    data: params,
    timeout: 300000, // 5分钟超时时间
    maxContentLength: Infinity // 确保没有响应大小限制
  })
}

/**
 * 根据深度范围查询钻进数据
 * @param params 查询参数，包含设备ID、孔号、开始深度、结束深度
 * @returns 深度范围内的钻进数据响应
 */
export const queryDrillingDataByDepth = (
  params: DepthRangeQueryParams
): Promise<ApiResponse<DrillingDataItem[]>> => {
  return request({
    url: '/data/query-by-depth',
    method: 'post',
    data: params,
    timeout: 300000, // 5分钟超时时间
    maxContentLength: Infinity // 确保没有响应大小限制
  })
}

/**
 * 获取设备最新一条数据，用于判断设备在线状态
 * @param deviceId 设备ID
 * @returns 最新数据响应
 */
export const getLatestDeviceData = (deviceId: string): Promise<ApiResponse<DrillingDataItem | null>> => {
  return request({
    url: '/data/latest',
    method: 'get',
    params: { deviceId }
  })
}

/**
 * 获取字段映射
 * @returns 字段映射响应
 */
export const getFieldMappings = (): Promise<ApiResponse<FieldMapping[]>> => {
  return request({
    url: '/data/field-mappings',
    method: 'get'
  })
}

/**
 * 获取设备数据统计信息
 * @param deviceId 设备ID
 * @param params 统计参数
 * @returns 数据统计响应
 */
export const getDeviceDataStats = (
  deviceId: string,
  params?: {
    startTime?: string
    endTime?: string
    holeNo?: string
  }
): Promise<ApiResponse<{
  totalCount: number
  dateRange: {
    start: string
    end: string
  }
  depthRange?: {
    min: number
    max: number
  }
}>> => {
  return request({
    url: `/data/stats/${deviceId}`,
    method: 'get',
    params
  })
}

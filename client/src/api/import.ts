import request from '@/utils/request'
import type { AxiosRequestHeaders } from 'axios'

/**
 * 获取导入数据列表
 * @param {Object} params 查询参数
 * @returns 响应数据
 */
export const getImportList = (params?: {
  file_name?: string
  page?: number
  pageSize?: number
  start_date?: string
  end_date?: string
}) => {
  return request({
    url: '/imports',
    method: 'get',
    params,
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 获取导入数据详情
 * @param {string|number} id 导入数据ID
 * @returns 响应数据
 */
export const getImportDetail = (id: string | number) => {
  return request({
    url: `/imports/${id}`,
    method: 'get',
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 创建导入数据
 * @param {Object} data 导入数据
 * @returns 响应数据
 */
export const createImport = (data: {
  file_name: string
  content?: string
  import_rows?: number
}) => {
  return request({
    url: '/imports',
    method: 'post',
    data,
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 更新导入数据
 * @param {string|number} id 导入数据ID
 * @param {Object} data 更新的数据
 * @returns 响应数据
 */
export const updateImport = (
  id: string | number,
  data: {
    file_name?: string
    content?: string
    import_rows?: number
  }
) => {
  return request({
    url: `/imports/${id}`,
    method: 'put',
    data,
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 删除导入数据
 * @param {string|number} id 导入数据ID
 * @returns 响应数据
 */
export const deleteImport = (id: string | number) => {
  return request({
    url: `/imports/${id}`,
    method: 'delete',
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 检查文件名是否已存在
 * @param {string} fileName 文件名
 * @returns 响应数据
 */
export const checkFileNameExists = (fileName: string) => {
  return request({
    url: `/imports/check/${encodeURIComponent(fileName)}`,
    method: 'get',
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 批量检查文件名是否已存在
 * @param {string[]} fileNames 文件名列表
 * @returns 响应数据
 */
export const batchCheckFileNames = (fileNames: string[]) => {
  return request({
    url: '/imports/batch-check',
    method: 'post',
    data: { fileNames },
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 获取导入关联的设备列表
 * @param {string|number} importId 导入数据ID
 * @param {Object} params 查询参数
 * @returns 响应数据
 */
export const getImportDevices = (
  importId: string | number,
  params?: {
    page?: number
    pageSize?: number
  }
) => {
  return request({
    url: `/imports/${importId}/devices`,
    method: 'get',
    params,
    headers: {} as AxiosRequestHeaders
  })
}

import { createRouter, createWebHistory, type RouteRecordRaw, type Router } from 'vue-router'
import Layout from '@/components/layout/Layout.vue'

// 扩展路由元信息类型
declare module 'vue-router' {
  interface RouteMeta {
    title?: string
    requiresAuth?: boolean
    activeMenu?: string
    icon?: string
    hidden?: boolean
  }
}

const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/index.vue'),
    meta: { title: '登录', requiresAuth: false }
  },
  {
    path: '/',
    component: Layout,
    redirect: '/device',
    children: [
      {
        path: 'device',
        name: 'Device',
        component: () => import('@/views/device/index.vue'),
        meta: { title: '设备管理', requiresAuth: true }
      },
      {
        path: 'device/detail/:id',
        name: 'DeviceDetail',
        component: () => import('@/views/device/detail.vue'),
        meta: {
          title: '设备详情',
          requiresAuth: true,
          activeMenu: '/device'
        }
      },
      {
        path: 'device/chart-config/:id',
        name: 'DeviceChartConfig',
        component: () => import('@/views/device/chart-config.vue'),
        meta: {
          title: '设备图表配置',
          requiresAuth: true,
          activeMenu: '/device'
        }
      },
      {
        path: 'device/realtime/:id',
        name: 'DeviceRealtime',
        component: () => import('@/views/device/realtime.vue'),
        meta: {
          title: '设备实时数据',
          requiresAuth: true,
          activeMenu: '/device'
        }
      },
      {
        path: 'device/digitalcore/:id',
        name: 'DigitalCore',
        component: () => import('@/views/device/digitalcore.vue'),
        meta: {
          title: '数字岩芯',
          requiresAuth: true,
          activeMenu: '/device'
        }
      },
      {
        path: 'device/digitalcore/:deviceId/detail/:fileId',
        name: 'DigitalCoreDetail',
        component: () => import('@/views/device/digitalcore-detail.vue'),
        meta: {
          title: '数字岩芯详情',
          requiresAuth: true,
          activeMenu: '/device'
        }
      },
      {
        path: 'algorithm/cleaning',
        name: 'AlgorithmCleaning',
        component: () => import('@/views/algorithm/cleaning.vue'),
        meta: { title: '清洗算法', requiresAuth: true }
      },
      {
        path: 'algorithm/analysis',
        name: 'AlgorithmAnalysis',
        component: () => import('@/views/algorithm/analysis.vue'),
        meta: { title: '分析算法', requiresAuth: true }
      },
      {
        path: 'overview',
        name: 'Overview',
        component: () => import('@/views/overview/index.vue'),
        meta: { title: '文件总览', requiresAuth: true }
      },
      {
        path: 'import',
        name: 'Import',
        component: () => import('@/views/import/index.vue'),
        meta: { title: '导入管理', requiresAuth: true }
      },
      {
        path: 'import/detail/:id',
        name: 'ImportDetail',
        component: () => import('@/views/import/detail.vue'),
        meta: {
          title: '导入详情',
          requiresAuth: true,
          activeMenu: '/import'
        }
      },
      {
        path: 'template',
        name: 'Template',
        component: () => import('@/views/template/index.vue'),
        meta: { title: '模板管理', requiresAuth: true }
      },
      {
        path: 'template/create',
        name: 'TemplateCreate',
        component: () => import('@/views/template/TemplateForm.vue'),
        meta: { 
          title: '创建模板', 
          requiresAuth: true,
          activeMenu: '/template'
        }
      },
      {
        path: 'template/edit/:id',
        name: 'TemplateEdit',
        component: () => import('@/views/template/TemplateForm.vue'),
        meta: { 
          title: '编辑模板', 
          requiresAuth: true,
          activeMenu: '/template'
        }
      },
      {
        path: 'algorithm/edit/:id',
        name: 'AlgorithmEdit',
        component: () => import('@/views/algorithm/edit.vue'),
        meta: {
          requiresAuth: true,
          title: '算法编辑'
        }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/overview'
  }
]

const router: Router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  // 路由配置选项
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  const title = to.meta?.title
  document.title = title ? `${title} - 海聚科技钻井数据监控系统` : '海聚科技钻井数据监控系统'

  // 检查是否需要登录
  if (to.meta?.requiresAuth !== false) {
    const userInfo = localStorage.getItem('userInfo')

    // 如果userInfo不存在，清除数据并跳转到登录页
    // token验证由后端通过HttpOnly Cookie处理
    if (!userInfo) {
      localStorage.removeItem('userInfo')
      next({ path: '/login' })
      return
    } else {
      next()
      return
    }
  } else {
    next()
  }
})

export default router

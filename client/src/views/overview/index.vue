<template>
  <div class="overview-container">
    <!-- 搜索和列表整合在一个卡片中 -->
    <el-card class="data-list-card">
      <!-- 筛选区域 -->
      <div class="filter-section">
        <el-row :gutter="24">
          <el-col :span="4">
            <el-input
              v-model.trim="filterForm.deviceSn"
              placeholder="设备序列号"
              clearable
              maxlength="32"
              @keyup.enter="debouncedFetchFileList"
            />
          </el-col>
          <el-col :span="4">
            <el-input
              v-model.trim="filterForm.fileName"
              placeholder="文件名称"
              clearable
              maxlength="64"
              @keyup.enter="debouncedFetchFileList"
            />
          </el-col>
          <el-col :span="8">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleDateChange"
            />
          </el-col>
          <el-col
            :span="8"
            class="filter-buttons"
          >
            <el-button
              type="primary"
              @click="debouncedFetchFileList"
            >
              搜索
            </el-button>
            <el-button @click="resetForm">
              清空
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="fileList"
        style="width: 100%"
        stripe
      >
        <!-- 新增的序号列 -->
        <el-table-column
          type="index"
          label="序号"
          width="60"
          align="center"
        />
        <el-table-column
          align="center"
          prop="device_sn"
          label="设备序列号"
        />
        <el-table-column
          align="center"
          prop="file_name"
          label="文件名称"
          show-overflow-tooltip
        />
        <el-table-column
          label="最后上传时间"
          align="center"
        >
          <template #default="scope">
            {{ formatDateTime(scope.row.modified_at) }}
          </template>
        </el-table-column>
        <el-table-column
          label="文件大小"
          align="center"
        >
          <template #default="scope">
            {{ formatFileSize(scope.row.file_size) }}
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          prop="data_rows"
          label="数据行数"
        />
        <el-table-column
          label="数据采集时间"
          align="center"
          width="350"
        >
          <template #default="scope">
            {{ formatDateTime(scope.row.first_data_time) + ' ~ ' + formatDateTime(scope.row.last_data_time) }}
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          width="120"
        >
          <template #default="scope">
            <el-button
              v-if="scope.row.oss_path"
              type="primary"
              size="small"
              @click="downloadFile(scope.row)"
            >
              下载
            </el-button>
            <span
              v-else
              class="no-download-text"
            >
              暂无文件
            </span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页部分 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next"
          :total="total"
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 40, 50, 60, 80, 100]"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { fileApi } from '@/api'
import { formatDateTime, formatFileSize } from '@/utils/utils'
import { debouncedApiCall, debouncedSearch } from '@/utils/debounce'
import { useFileListStore } from '@/store/fileList'
import { storeToRefs } from 'pinia'

// OSS域名配置
const OSS_DOMAIN = 'https://mhp-data-hub.oss-cn-hangzhou.aliyuncs.com/'

// 使用文件列表Store
const fileListStore = useFileListStore()
const { filterForm, currentPage, pageSize, total, dateRange } = storeToRefs(fileListStore)

const loading = ref(false)

// 文件列表数据
const fileList = ref([])

// 获取文件列表
const fetchFileList = async () => {
  try {
    loading.value = true
    const params = {
      deviceSn: filterForm.value.deviceSn,
      fileName: filterForm.value.fileName,
      startDate: filterForm.value.startDate,
      endDate: filterForm.value.endDate,
      page: currentPage.value,
      pageSize: pageSize.value
    }

    const res = await fileApi.getFileList(params)
    if (res.success) {
      fileList.value = res.data.list
      total.value = res.data.total
    } else {
      ElMessage.error('获取文件列表失败')
    }
  } catch (error) {
    console.error('获取文件列表失败:', error)
    ElMessage.error('获取文件列表失败: ' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}



// 日期范围变化处理
const handleDateChange = val => {
  fileListStore.updateDateFromRange(val || [])
}

// 创建防抖的搜索函数
const debouncedFetchFileList = debouncedApiCall(() => {
  currentPage.value = 1
  fetchFileList()
})

// 创建防抖的输入搜索函数
const debouncedInputSearch = debouncedSearch(() => {
  currentPage.value = 1
  fetchFileList()
})

// 重置表单
const resetForm = () => {
  fileListStore.resetState()
  fetchFileList()
}

// 页面切换
const handlePageChange = page => {
  currentPage.value = page
  fetchFileList()
}

// 页面大小变化
const handleSizeChange = val => {
  pageSize.value = val
  currentPage.value = 1 // 重置到第一页
  fetchFileList()
}

// 下载文件
const downloadFile = (row) => {
  try {
    if (!row.oss_path) {
      ElMessage.warning('该文件暂无下载链接')
      return
    }

    // 拼接完整的下载链接
    const downloadUrl = OSS_DOMAIN + row.oss_path

    // 创建一个临时的a标签进行下载
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = row.file_name || '文件下载'
    link.target = '_blank'

    // 触发下载
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    ElMessage.success('开始下载文件')
  } catch (error) {
    console.error('下载文件失败:', error)
    ElMessage.error('下载文件失败: ' + (error.message || '未知错误'))
  }
}

// 监听输入框变化，实现实时搜索
watch(
  () => [filterForm.deviceSn, filterForm.fileName],
  () => {
    debouncedInputSearch()
  },
  { deep: true }
)

// 监听状态变化，自动保存状态
watch([() => filterForm.value.deviceSn, () => filterForm.value.fileName,
       () => filterForm.value.startDate, () => filterForm.value.endDate,
       () => currentPage.value, () => pageSize.value],
  () => {
    fileListStore.saveState()
  },
  { deep: true }
)

onMounted(() => {
  // 恢复保存的搜索状态
  const restored = fileListStore.restoreState()
  if (restored) {
    console.log('已恢复文件列表搜索状态')
  }

  // 初始加载文件列表
  fetchFileList()
})

// 组件卸载时清理防抖函数
onUnmounted(() => {
  if (debouncedFetchFileList && typeof debouncedFetchFileList.cancel === 'function') {
    debouncedFetchFileList.cancel()
  }
  if (debouncedInputSearch && typeof debouncedInputSearch.cancel === 'function') {
    debouncedInputSearch.cancel()
  }
})
</script>

<style scoped>
.overview-container {
  padding: 24px;
  background-color: var(--apple-background);
}

/* 整合搜索和列表的卡片 */
.data-list-card {
  margin-bottom: 24px;
}

/* 筛选区域样式 */
.filter-section {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.filter-buttons {
  text-align: right;
}

/* 分页容器样式 */
.pagination-container {
  margin-top: 20px;
  text-align: center;
}

/* 操作列样式 */
.no-download-text {
  color: var(--apple-text-secondary, #999);
  font-size: 12px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .filter-buttons {
    text-align: left;
    margin-top: 16px;
  }
}
</style>

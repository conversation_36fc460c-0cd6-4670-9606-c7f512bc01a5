<!--
  模板列表页面 - 包含 transform: scale() 居中解决方案

  核心功能：
  1. 4列瀑布流布局展示模板卡片
  2. 动态高度的模板预览，支持缩放显示
  3. 完美解决 transform: scale() 元素的居中问题

  居中方案核心原理：
  transform: scale() 只改变元素的视觉效果，不改变布局空间，导致 flexbox 无法正确居中

  解决方案：三层结构
  1. template-preview: 外层容器，使用 flexbox 居中
  2. preview-wrapper: 包装器，提供缩放后的正确占用空间
  3. preview-canvas: 画板，应用 transform: scale() 缩放

  关键函数：
  - getWrapperStyle(): 计算缩放后的实际显示尺寸
  - getCanvasStyle(): 应用缩放变换，保持原始尺寸

  适用场景：
  - 任何需要缩放显示且要求居中的内容
  - 动态尺寸的预览组件
  - 响应式布局中的缩放元素
-->
<template>
  <div class="template-container">
    <!-- 搜索和筛选区域 -->
    <el-card class="filter-card">
      <div class="filter-section">
        <el-row :gutter="24">
          <el-col :span="3">
            <el-input
              v-model.trim="searchKeyword"
              placeholder="模板名称"
              clearable
              maxlength="32"
              @keyup.enter="debouncedHandleSearch"
            />
          </el-col>
          <el-col :span="3">
            <el-select
              v-model="statusFilter"
              placeholder="状态筛选"
            >
              <el-option
                label="全部"
                value="全部"
              />
              <el-option
                label="启用"
                value="1"
              />
              <el-option
                label="禁用"
                value="0"
              />
            </el-select>
          </el-col>
          <el-col
            :span="3"
            class="filter-buttons"
          >
            <el-button
              type="primary"
              @click="debouncedHandleSearch"
            >
              搜索
            </el-button>
            <el-button @click="resetForm">
              清空
            </el-button>
          </el-col>
          <el-col :span="10">
            <!-- 分页信息和页码导航 -->
            <div class="pagination-info">
              <el-pagination
                background
                layout="total, sizes, prev, pager, next"
                :total="total"
                :current-page="currentPage"
                :page-size="pageSize"
                :page-sizes="[10, 20, 40, 50, 60, 80, 100]"
                small
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </el-col>
          <el-col
            :span="5"
            style="text-align: right; display: flex; align-items: center; justify-content: flex-end; height: 40px;"
          >
            <el-button
              type="primary"
              @click="handleAddTemplate"
            >
              <el-icon><Plus /></el-icon>新增模板
            </el-button>
          </el-col>
        </el-row>
      </div>
    </el-card>
      
    <!-- 4列瀑布流模板卡片展示 -->
    <div
      ref="waterfallContainer"
      v-loading="loading"
      class="template-waterfall"
    >
      <div
        v-for="template in templates"
        :key="template.id"
        class="template-card"
        :class="{ 'template-card-disabled': template.status === 0 }"
      >
        <!-- 卡片头部 -->
        <div class="template-card-header">
          <h3 class="template-name">
            {{ template.name }}
          </h3>
          <el-tag
            :type="template.status === 1 ? 'success' : 'info'"
            size="small"
          >
            {{ template.status === 1 ? '启用' : '禁用' }}
          </el-tag>
        </div>

        <!-- 模板预览区域 -->
        <div class="template-preview-container">
          <!--
              三层结构解决 transform: scale() 居中问题：
              1. template-preview: 外层容器，使用 flexbox 居中
              2. preview-wrapper: 包装器，提供缩放后的正确占用空间
              3. preview-canvas: 画板，应用 transform: scale() 缩放

              核心原理：transform: scale() 不改变元素的布局空间，只改变视觉效果
              所以需要包装器来提供正确的占用空间，让 flexbox 能正确计算居中位置
            -->
          <div
            class="template-preview"
            :style="{ height: getPreviewHeight(template) + 'px' }"
          >
            <!-- 预览包装器：计算缩放后的实际显示尺寸，为 flexbox 居中提供正确的占用空间 -->
            <div
              class="preview-wrapper"
              :style="getWrapperStyle(template)"
            >
              <!-- 预览画板：应用缩放变换，保持原始尺寸但视觉上缩放 -->
              <div
                class="preview-canvas"
                :style="getCanvasStyle(template)"
              >
                <!-- 预览组件 -->
                <div
                  v-for="(component, index) in getTemplateComponents(template)"
                  :key="'preview-' + (component.id || index)"
                  class="preview-component"
                  :style="getComponentStyle(component)"
                >
                  <div class="preview-component-content">
                    <div class="preview-component-header">
                      <span class="preview-component-title">{{ component.name || component.title || '组件' }}</span>
                      <el-tag
                        size="small"
                        type="info"
                      >
                        {{ component.type || 'chart' }}
                      </el-tag>
                    </div>
                    <div class="preview-component-body">
                      <!-- 如果有缩略图就显示缩略图 -->
                      <div
                        v-if="component.thumbnail"
                        class="preview-component-chart"
                      >
                        <img
                          :src="component.thumbnail"
                          :alt="component.name"
                          @error="handleImageError"
                        >
                      </div>
                      <!-- 否则显示占位符 -->
                      <div
                        v-else
                        class="preview-component-placeholder"
                      >
                        <el-icon class="preview-icon">
                          <TrendCharts />
                        </el-icon>
                        <p>{{ component.type || 'Chart' }}</p>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 空状态 -->
                <div
                  v-if="getTemplateComponents(template).length === 0"
                  class="preview-empty"
                >
                  <el-icon class="preview-empty-icon">
                    <DocumentCopy />
                  </el-icon>
                  <p>暂无组件</p>
                  <span class="preview-empty-tip">点击编辑添加图表组件</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 卡片内容 -->
        <div
          v-if="template.description"
          class="template-card-content"
        >
          <p
            class="template-description"
            :title="template.description"
          >
            {{ template.description || '暂无描述' }}
          </p>
        </div>

        <!-- 卡片操作 -->
        <div class="template-card-footer">
          <el-button
            type="success"
            link
            :disabled="template.status === 0"
            @click="handleConfigDevice(template)"
          >
            <el-icon><Connection /></el-icon>
            绑定设备({{ template.deviceCount || 0 }})
          </el-button>
          <el-button
            type="primary"
            link
            :disabled="template.status === 0"
            @click="handleEdit(template)"
          >
            <el-icon><Edit /></el-icon>
            编辑
          </el-button>
          <el-button
            type="primary"
            link
            @click="handleView(template)"
          >
            <el-icon><View /></el-icon>
            查看
          </el-button>
          <el-dropdown
            trigger="click"
            @command="command => handleCommand(command, template)"
          >
            <el-button
              type="primary"
              link
            >
              <el-icon><More /></el-icon>
              更多
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  command="copy"
                  :disabled="template.status === 0"
                >
                  <el-icon><DocumentCopy /></el-icon>
                  复制
                </el-dropdown-item>
                <el-dropdown-item :command="template.status === 1 ? 'disable' : 'enable'">
                  <el-icon>
                    <CircleClose v-if="template.status === 1" />
                    <CircleCheck v-else />
                  </el-icon>
                  {{ template.status === 1 ? '禁用' : '启用' }}
                </el-dropdown-item>
                <el-dropdown-item
                  command="delete"
                  divided
                >
                  <el-icon><Delete /></el-icon>
                  删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
        
      <!-- 空状态展示 -->
      <el-empty
        v-if="templates.length === 0 && !loading"
        description="暂无模板数据"
        class="empty-data"
      />
    </div>

    <!-- 查看模板详情对话框 -->
    <el-dialog
      v-model="viewDialogVisible"
      title="模板详情"
      width="600px"
    >
      <div
        v-if="currentTemplate"
        class="template-detail"
      >
        <div class="detail-item">
          <span class="detail-label">名称：</span>
          <span>{{ currentTemplate.name }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">描述：</span>
          <span>{{ currentTemplate.description || '暂无描述' }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">状态：</span>
          <el-tag
            :type="currentTemplate.status === 1 ? 'success' : 'info'"
            size="small"
          >
            {{ currentTemplate.status === 1 ? '启用' : '禁用' }}
          </el-tag>
        </div>
        <div class="detail-item">
          <span class="detail-label">创建时间：</span>
          <span>{{ formatDate(currentTemplate.created_at) }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">最后修改：</span>
          <span>{{ currentTemplate.modified_at ? formatDate(currentTemplate.modified_at) : '无修改记录' }}</span>
        </div>
        <div class="detail-item config-item">
          <span class="detail-label">配置：</span>
          <pre>{{ formatConfig(currentTemplate.config) }}</pre>
        </div>
      </div>
    </el-dialog>

    <!-- 配置设备对话框 -->
    <el-dialog
      v-model="deviceConfigDialogVisible"
      title="配置设备"
      width="1050px"
      :before-close="() => { deviceConfigDialogVisible = false }"
    >
      <div
        v-if="currentConfigTemplate"
        class="device-config-content"
      >
        <el-transfer
          v-model="boundDeviceIds"
          v-loading="deviceListLoading || transferLoading"
          :data="transferDeviceList"
          :titles="['未绑定设备', '已绑定设备']"
          :button-texts="['解绑', '绑定']"
          :format="{
            noChecked: '${total}',
            hasChecked: '${checked}/${total}'
          }"
          filterable
          :filter-placeholder="'搜索设备名称或序列号'"
          :filter-method="filterDevice"
          @change="handleTransferChange"
        >
          <template #default="{ option }">
            <div class="transfer-device-item">
              <div class="device-name">
                {{ option.label }}
              </div>
              <div class="device-serial">
                序列号: {{ option.serialNumber }}
              </div>
              <div
                v-if="option.hasOtherTemplate"
                class="device-warning"
              >
                已配置其他模板
              </div>
            </div>
          </template>
        </el-transfer>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="deviceConfigDialogVisible = false">
            关闭
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 复制模板对话框 -->
    <el-dialog
      v-model="copyDialogVisible"
      title="复制模板"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form label-width="100px">
        <el-input
          v-model="copyTemplateName"
          placeholder="请输入新模板名称"
          maxlength="32"
          show-word-limit
        />
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="copyDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            :loading="copyLoading"
            :disabled="!copyTemplateName.trim()"
            @click="confirmCopyTemplate"
          >
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/**
 * 模板列表页面
 * 提供模板的瀑布流展示、预览、编辑、删除和设备绑定功能
 */

import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Edit, View, More, TrendCharts, DocumentCopy, Connection, CircleClose, CircleCheck, Delete } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { getTemplateList, deleteTemplate, updateTemplateStatus, copyTemplate } from '@/api/template'
import { getDeviceList } from '@/api/device'
import { getBatchDeviceTemplateConfigs, batchBindDevicesToTemplate, batchUnbindDevicesFromTemplate } from '@/api/deviceTemplateConfig'
import { debouncedResize, debouncedApiCall } from '@/utils/debounce'
import { useTemplateListStore } from '@/store/templateList'
import { storeToRefs } from 'pinia'

// 模板信息类型 - 与API返回数据匹配
interface Template {
  id: number
  name: string
  description: string
  config: string
  status: number  // 0-禁用，1-启用
  created_at: string
  modified_at?: string
  deviceCount?: number
}

// 设备信息类型
interface DeviceWithTemplate {
  id: number
  deviceName: string
  serialNumber: string
  type: string
  typeName: string
  lastDeviceTime?: string
  lastLifeTime?: string
  isCurrentTemplate?: boolean
  hasTemplate?: boolean
}



// 使用模版列表Store
const templateListStore = useTemplateListStore()
const { filterForm, currentPage, pageSize, total, layoutTrigger } = storeToRefs(templateListStore)

// 将Store的filterForm映射为本地变量以保持兼容性
const searchKeyword = computed({
  get: () => filterForm.value.searchKeyword,
  set: (value: string) => { filterForm.value.searchKeyword = value }
})
const statusFilter = computed({
  get: () => filterForm.value.statusFilter,
  set: (value: string) => { filterForm.value.statusFilter = value }
})

// 状态数据
const loading = ref<boolean>(false)
const templates = ref<Template[]>([])
const viewDialogVisible = ref<boolean>(false)
const currentTemplate = ref<Template | null>(null)
const router = useRouter()

// 设备配置相关状态
const deviceConfigDialogVisible = ref<boolean>(false)
const currentConfigTemplate = ref<Template | null>(null)
const deviceListLoading = ref<boolean>(false)
const transferLoading = ref<boolean>(false) // 穿梭操作加载状态
const allDevices = ref<DeviceWithTemplate[]>([])

// 穿梭框相关数据
const boundDeviceIds = ref<number[]>([]) // 已绑定的设备ID列表
const transferDeviceList = ref<TransferDevice[]>([]) // 穿梭框数据源
const originalBoundDeviceIds = ref<number[]>([]) // 原始已绑定设备ID列表，用于对比变化

// 复制模板对话框
const copyDialogVisible = ref<boolean>(false)
const copyTemplateName = ref<string>('')
const copyLoading = ref<boolean>(false)
const currentCopyTemplate = ref<Template | null>(null)

// 穿梭框设备项类型
interface TransferDevice {
  key: number
  label: string
  serialNumber: string
  hasOtherTemplate: boolean
  disabled?: boolean
}

// 瀑布流相关
const waterfallContainer = ref(null)
const isLayouting = ref(false) // 防止重复布局

// 瀑布流布局函数 - 优化版本
const initWaterfall = async () => {
  // 防止重复执行
  if (isLayouting.value) return
  isLayouting.value = true

  try {
    // 等待DOM更新完成
    await nextTick()

    // 额外等待一小段时间，确保所有内容（包括图片）都渲染完成
    await new Promise(resolve => setTimeout(resolve, 100))

    if (!waterfallContainer.value) return

    const container = waterfallContainer.value
    const cards = container.querySelectorAll('.template-card')

    if (cards.length === 0) return

    // 响应式列数设置，充分利用容器宽度
    const containerWidth = container.offsetWidth
    let columns = 4 // 默认4列

    // 响应式调整列数
    if (containerWidth < 480) {
      columns = 1
    } else if (containerWidth < 768) {
      columns = 2
    } else if (containerWidth < 1200) {
      columns = 3
    } else {
      columns = 4
    }

    const gap = 20 // 间距

    // 计算每个卡片的宽度，充分利用容器宽度
    const cardWidth = (containerWidth - (columns - 1) * gap) / columns

    // 初始化列高度数组
    const columnHeights = new Array(columns).fill(0)

    // 设置容器为相对定位
    container.style.position = 'relative'
    container.style.width = '100%'

    // 先重置所有卡片的样式，确保能获取到正确的高度
    cards.forEach((card) => {
      card.style.position = 'static'
      card.style.width = cardWidth + 'px'
      card.style.left = 'auto'
      card.style.top = 'auto'
    })

    // 等待重置后的重新渲染
    await nextTick()

    // 为每个卡片设置位置
    cards.forEach((card) => {
      // 找到最短的列
      const shortestColumnIndex = columnHeights.indexOf(Math.min(...columnHeights))

      // 计算位置
      const x = shortestColumnIndex * (cardWidth + gap)
      const y = columnHeights[shortestColumnIndex]

      // 设置卡片样式
      card.style.position = 'absolute'
      card.style.left = x + 'px'
      card.style.top = y + 'px'
      card.style.width = cardWidth + 'px'
      card.style.transition = 'all 0.3s ease'

      // 获取卡片的实际高度（包括内容）
      const cardHeight = card.offsetHeight

      // 更新列高度
      columnHeights[shortestColumnIndex] += cardHeight + gap
    })

    // 设置容器高度
    const maxHeight = Math.max(...columnHeights)
    container.style.height = maxHeight + 'px'
  } finally {
    isLayouting.value = false
  }
}

// 使用统一的防抖布局函数
const debouncedInitWaterfall = debouncedResize(() => {
  initWaterfall()
})

// 监听模板数据变化，重新布局
watch(templates, (newTemplates, oldTemplates) => {
  // 只有在模板数量或内容发生实际变化时才重新布局
  if (!oldTemplates || newTemplates.length !== oldTemplates.length) {
    nextTick(() => {
      initWaterfall()
    })
  }
}, { deep: false }) // 改为浅层监听，提高性能

// 监听状态变化，自动保存状态和触发搜索
watch([() => filterForm.value.searchKeyword, () => filterForm.value.statusFilter,
       () => currentPage.value, () => pageSize.value],
  (newValues, oldValues) => {
    // 保存状态
    templateListStore.saveState()

    // 如果搜索条件或状态筛选发生变化，重置页码并重新搜索
    if (oldValues && (
      newValues[0] !== oldValues[0] || // searchKeyword变化
      newValues[1] !== oldValues[1]    // statusFilter变化
    )) {
      currentPage.value = 1 // 重置到第一页
      fetchTemplates()
    }
  },
  { deep: true }
)

// 监听瀑布流布局触发器
watch(layoutTrigger, () => {
  initWaterfall()
})

// 初始化加载数据
onMounted(() => {
  // 恢复保存的搜索状态
  const restored = templateListStore.restoreState()
  if (restored) {
    console.log('已恢复模版列表搜索状态')
  }

  fetchTemplates()

  // 监听窗口大小变化，使用防抖
  window.addEventListener('resize', debouncedInitWaterfall)
})

// 清理事件监听器
onUnmounted(() => {
  window.removeEventListener('resize', debouncedInitWaterfall)

  // 清理防抖函数
  if (debouncedInitWaterfall && typeof debouncedInitWaterfall.cancel === 'function') {
    debouncedInitWaterfall.cancel()
  }
  if (debouncedHandleSearch && typeof debouncedHandleSearch.cancel === 'function') {
    debouncedHandleSearch.cancel()
  }
})

// 获取模板列表数据
const fetchTemplates = async () => {
  loading.value = true
  try {
    const params: any = {
      pageSize: pageSize.value,
      page: currentPage.value
    }

    // 添加搜索条件
    if (searchKeyword.value) {
      params.name = searchKeyword.value
    }

    // 添加状态筛选
    if (statusFilter.value !== '' && statusFilter.value !== '全部') {
      params.status = statusFilter.value
    }

    const response = await getTemplateList(params)
    if (response.success) {
      // 后端已经包含设备数量，直接使用
      templates.value = response.data.list
      total.value = response.data.total

      // 数据加载完成后，等待DOM更新并初始化瀑布流布局
      await nextTick()
      // 额外延迟，确保所有模板预览内容都渲染完成
      setTimeout(() => {
        initWaterfall()
      }, 200)
    } else {
      ElMessage.error(response.message || '获取模板列表失败')
    }
  } catch (error) {
    console.error('获取模板列表错误:', error)
    ElMessage.error('获取模板列表失败')
  } finally {
    loading.value = false
  }
}

// 强制重新布局（用于解决布局问题）
const forceRelayout = async () => {
  isLayouting.value = false // 重置布局状态
  await nextTick()
  setTimeout(() => {
    initWaterfall()
  }, 100)
}

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size
  fetchTemplates()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchTemplates()
}



// 使用防抖的搜索函数
const debouncedHandleSearch = debouncedApiCall(() => {
  currentPage.value = 1
  fetchTemplates()
})

// 重置表单
const resetForm = () => {
  templateListStore.resetState()
  fetchTemplates()
}

// 新增模板
const handleAddTemplate = () => {
  router.push('/template/create')
}

// 编辑模板
const handleEdit = (template: Template) => {
  // 检查模板是否已禁用
  if (template.status === 0) {
    ElMessage.warning('已禁用的模板不允许编辑')
    return
  }
  router.push(`/template/edit/${template.id}`)
}

// 查看模板详情
const handleView = (template: Template) => {
  currentTemplate.value = template
  viewDialogVisible.value = true
}

// 复制模板
const handleCopyTemplate = (template: Template) => {
  // 检查模板是否已禁用
  if (template.status === 0) {
    ElMessage.warning('已禁用的模板不允许复制')
    return
  }

  // 打开自定义复制对话框
  currentCopyTemplate.value = template
  copyTemplateName.value = ''
  copyDialogVisible.value = true
}

// 确认复制模板
const confirmCopyTemplate = async () => {
  if (!copyTemplateName.value.trim()) {
    ElMessage.warning('请输入模板名称')
    return
  }

  try {
    copyLoading.value = true

    // 调用复制API
    const response = await copyTemplate(currentCopyTemplate.value!.id, copyTemplateName.value.trim())

    if (response.success) {
      ElMessage.success('模板复制成功')
      copyDialogVisible.value = false
      // 刷新模板列表
      fetchTemplates()
    } else {
      ElMessage.error(response.message || '复制模板失败')
    }
  } catch (error) {
    console.error('复制模板错误:', error)
    ElMessage.error('复制模板失败')
  } finally {
    copyLoading.value = false
  }
}

// 更多操作处理
const handleCommand = (command: string, template: Template) => {
  switch (command) {
    case 'copy':
      handleCopyTemplate(template)
      break
    case 'enable':
      changeTemplateStatus(template, 1)
      break
    case 'disable':
      changeTemplateStatus(template, 0)
      break
    case 'delete':
      confirmDeleteTemplate(template)
      break
    default:
      break
  }
}

// 修改模板状态
const changeTemplateStatus = async (template: Template, status: number) => {
  try {
    // 如果是禁用操作且模板有绑定设备，不允许禁用
    if (status === 0 && template.deviceCount && template.deviceCount > 0) {
      await ElMessageBox.alert(
        `模板 "${template.name}" 当前绑定了 ${template.deviceCount} 个设备，无法直接禁用。请先解除所有设备的绑定关系，然后再禁用模板。`,
        '无法禁用',
        {
          confirmButtonText: '我知道了',
          type: 'warning',
          dangerouslyUseHTMLString: false
        }
      )
      return // 不执行禁用操作
    }

    // 执行状态更新
    const response = await updateTemplateStatus(template.id, status)
    if (response.success) {
      ElMessage.success(status === 1 ? '模板已启用' : '模板已禁用')
      fetchTemplates()
    } else {
      ElMessage.error(response.message || '操作失败')
    }
  } catch (error) {
    // 如果是用户取消操作，不显示错误信息
    if (error === 'cancel') {
      return
    }
    console.error('修改模板状态错误:', error)
    ElMessage.error('操作失败')
  }
}

// 确认删除模板
const confirmDeleteTemplate = async (template: Template) => {
  try {
    // 检查模板是否有绑定设备
    if (template.deviceCount && template.deviceCount > 0) {
      // 有绑定设备，不允许删除
      await ElMessageBox.alert(
        `模板 "${template.name}" 当前绑定了 ${template.deviceCount} 个设备，无法直接删除。请先解除所有设备的绑定关系，然后再删除模板。`,
        '无法删除',
        {
          confirmButtonText: '我知道了',
          type: 'warning',
          dangerouslyUseHTMLString: false
        }
      )
      return // 不执行删除操作
    }

    // 没有绑定设备，确认删除
    await ElMessageBox.confirm(
      `确认删除模板 "${template.name}" 吗？删除后将无法恢复，请谨慎操作。`,
      '删除确认',
      {
        confirmButtonText: '确认删除',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: false
      }
    )

    // 用户确认删除，执行删除操作
    handleDeleteTemplate(template.id)
  } catch (error) {
    // 用户取消操作或关闭对话框，不执行任何操作
    if (error === 'cancel' || error === 'close') {
      return
    }
    console.error('删除确认错误:', error)
  }
}

// 删除模板
const handleDeleteTemplate = async (id: number) => {
  try {
    const response = await deleteTemplate(id)
    if (response.success) {
      ElMessage.success('模板已删除')

      // 如果当前页没有数据了，且不是第一页，则跳转到前一页
      if (templates.value.length === 1 && currentPage.value > 1) {
        currentPage.value--
      }

      await fetchTemplates()
      // 删除后强制重新布局
      forceRelayout()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    console.error('删除模板错误:', error)
    ElMessage.error('删除失败')
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 格式化配置JSON
const formatConfig = (configString: string) => {
  try {
    if (!configString) return '{}'
    const config = JSON.parse(configString)
    return JSON.stringify(config, null, 2)
  } catch (error) {
    return configString || '{}'
  }
}

// 获取模板组件列表
const getTemplateComponents = (template: Template) => {
  try {
    if (!template.config) return []

    const config = JSON.parse(template.config)
    let components = config.components || []

    // 标准化组件数据结构，确保正确解析位置和尺寸
    components = components.map((comp, index) => {
      // 解析组件的config字段获取缩略图
      let thumbnail = null
      try {
        if (comp.config && typeof comp.config === 'string') {
          const configObj = JSON.parse(comp.config)
          thumbnail = configObj.thumbnail
        }
      } catch (error) {
        console.warn('解析组件config失败:', error)
      }

      const standardized = {
        id: comp.id || `comp-${index}`,
        name: comp.name || comp.title || comp.label || `组件${index + 1}`,
        type: comp.type || comp.chartType || 'chart',
        // 正确解析位置信息
        x: comp.position?.x ?? comp.x ?? 0,
        y: comp.position?.y ?? comp.y ?? 0,
        // 正确解析尺寸信息
        width: comp.size?.width ?? comp.width ?? 200,
        height: comp.size?.height ?? comp.height ?? 150,
        // 获取缩略图 - 优先从config中解析的thumbnail
        thumbnail: thumbnail || comp.thumbnail || comp.image || comp.chartImage || comp.preview
      }

      return standardized
    })
    return components
  } catch (error) {
    console.error('解析模板配置失败:', error, template.config)
    return []
  }
}



// 计算预览高度
const getPreviewHeight = (template: Template) => {
  const components = getTemplateComponents(template)

  if (components.length === 0) {
    return 200
  }

  // 计算实际内容的边界
  let maxBottom = 0
  let maxRight = 0

  components.forEach(component => {
    const x = component.x || 0
    const y = component.y || 0
    const width = component.width || 200
    const height = component.height || 150

    maxRight = Math.max(maxRight, x + width)
    maxBottom = Math.max(maxBottom, y + height)
  })

  // 如果没有有效的组件位置，使用默认高度
  if (maxBottom === 0) {
    return 250
  }

  // 计算缩放比例，确保内容完全可见
  const cardWidth = 280 // 卡片内容区域宽度
  const scaleX = cardWidth / Math.max(maxRight, 800) // 基于宽度的缩放
  const scaleY = scaleX // 保持比例一致

  // 计算缩放后的高度
  const scaledHeight = maxBottom * scaleY

  // 添加一些内边距，确保内容不会贴边
  const paddedHeight = scaledHeight + 40

  // 限制高度范围：最小200px，最大600px
  return Math.min(Math.max(paddedHeight, 200), 600)
}

/**
 * 获取预览包装器样式 - 解决 transform: scale() 居中问题的关键函数
 *
 * 问题背景：
 * transform: scale() 只改变元素的视觉效果，不改变元素在布局中的占用空间
 * 这导致 flexbox 的 justify-content: center 基于原始尺寸计算，无法正确居中缩放后的内容
 *
 * 解决方案：
 * 通过包装器提供缩放后的正确占用空间，让 flexbox 能基于实际显示尺寸进行居中计算
 *
 * @param {Object} template - 模板对象
 * @returns {Object} 包装器的 CSS 样式对象
 */
const getWrapperStyle = (template: Template) => {
  const components = getTemplateComponents(template)

  // 空模板的默认处理
  if (components.length === 0) {
    const scale = 0.35                    // 默认缩放比例
    const originalWidth = 800             // 原始画板宽度
    const scaledWidth = originalWidth * scale  // 缩放后的实际显示宽度

    return {
      width: scaledWidth + 'px',          // 关键：设置缩放后的实际宽度
      height: (600 * scale) + 'px',       // 关键：设置缩放后的实际高度
      position: 'relative'
    }
  }

  // 计算所有组件的边界，确定内容的实际尺寸
  let maxRight = 0
  let maxBottom = 0

  components.forEach(component => {
    const x = component.x || 0
    const y = component.y || 0
    const width = component.width || 200
    const height = component.height || 150

    maxRight = Math.max(maxRight, x + width)
    maxBottom = Math.max(maxBottom, y + height)
  })

  // 计算缩放比例和最终的显示尺寸
  const actualWidth = Math.max(maxRight, 800)      // 内容的实际宽度
  const actualHeight = Math.max(maxBottom, 600)    // 内容的实际高度
  const scale = Math.min(1, 300 / actualWidth)     // 计算合适的缩放比例

  return {
    width: (actualWidth * scale) + 'px',   // 关键：缩放后的实际显示宽度
    height: (actualHeight * scale) + 'px', // 关键：缩放后的实际显示高度
    position: 'relative'
  }
}

/**
 * 获取预览画板样式 - 应用缩放变换
 *
 * 功能说明：
 * 1. 保持元素的原始尺寸（width/height）
 * 2. 通过 transform: scale() 进行视觉缩放
 * 3. 配合包装器实现完美居中效果
 *
 * 注意事项：
 * - 必须保持原始尺寸，缩放只通过 transform 实现
 * - transformOrigin 设置为 'top left' 确保缩放基准点一致
 * - 缩放比例必须与包装器中的计算保持一致
 *
 * @param {Object} template - 模板对象
 * @returns {Object} 画板的 CSS 样式对象
 */
const getCanvasStyle = (template: Template) => {
  const components = getTemplateComponents(template)

  // 空模板的默认处理
  if (components.length === 0) {
    return {
      width: '800px',                     // 保持原始宽度
      height: '600px',                    // 保持原始高度
      transform: 'scale(0.35)',           // 应用缩放变换（与包装器中的 scale 一致）
      transformOrigin: 'top left',        // 缩放基准点：左上角
      position: 'relative'
    }
  }

  // 计算内容边界，确定画板的原始尺寸
  let maxRight = 0
  let maxBottom = 0

  components.forEach(component => {
    const x = component.x || 0
    const y = component.y || 0
    const width = component.width || 200
    const height = component.height || 150

    maxRight = Math.max(maxRight, x + width)
    maxBottom = Math.max(maxBottom, y + height)
  })

  // 计算缩放比例（必须与包装器中的计算保持一致）
  const actualWidth = Math.max(maxRight, 800)
  const scale = Math.min(1, 300 / actualWidth)

  return {
    width: actualWidth + 'px',            // 保持原始宽度
    height: Math.max(maxBottom, 600) + 'px', // 保持原始高度
    transform: `scale(${scale})`,         // 应用缩放变换
    transformOrigin: 'top left',          // 缩放基准点：左上角
    position: 'relative'
  }
}

// 获取组件样式
const getComponentStyle = (component: any) => {
  // 使用标准化后的属性
  const x = component.x || 0
  const y = component.y || 0
  const width = component.width || 200
  const height = component.height || 150

  return {
    position: 'absolute',
    left: x + 'px',
    top: y + 'px',
    width: width + 'px',
    height: height + 'px'
  }
}

// 处理图片加载错误
const handleImageError = (event: Event) => {
  const target = event.target as HTMLImageElement
  console.log('图片加载失败:', target?.src)
  // 可以在这里设置默认图片或隐藏图片
}

// 配置设备处理函数
const handleConfigDevice = async (template: Template) => {
  // 检查模板是否已禁用
  if (template.status === 0) {
    ElMessage.warning('已禁用的模板不允许绑定设备')
    return
  }

  currentConfigTemplate.value = template
  boundDeviceIds.value = []
  transferDeviceList.value = []
  deviceConfigDialogVisible.value = true

  // 加载设备列表
  await fetchDeviceList()
}

// 获取设备列表
const fetchDeviceList = async () => {
  deviceListLoading.value = true
  try {
    const response = await getDeviceList({
      page: 1,
      pageSize: 2000 // 获取所有设备
    })

    if (response.success) {
      const devices = (response.data as any).list

      // 批量获取所有设备的模板配置状态
      const deviceIds = devices.map((device: any) => device.id)

      try {
        // 使用批量API一次性获取所有设备的模板配置状态
        const batchConfigResponse = await getBatchDeviceTemplateConfigs(
          deviceIds,
          currentConfigTemplate.value?.id
        )

        if (batchConfigResponse.success) {
          // 创建设备配置状态映射
          const deviceConfigMap = new Map()
          batchConfigResponse.data.forEach(item => {
            deviceConfigMap.set(item.deviceId, {
              hasTemplate: item.hasTemplate,
              isCurrentTemplate: item.isCurrentTemplate,
              configs: item.configs
            })
          })

          // 处理设备数据，添加模板配置状态
          const devicesWithConfig = devices.map((device: any) => {
            const configStatus = deviceConfigMap.get(device.id) || {
              hasTemplate: false,
              isCurrentTemplate: false,
              configs: []
            }

            return {
              ...device,
              hasTemplate: configStatus.hasTemplate,
              isCurrentTemplate: configStatus.isCurrentTemplate
            }
          })

          allDevices.value = devicesWithConfig

          // 构建穿梭框数据源
          transferDeviceList.value = devicesWithConfig.map((device: DeviceWithTemplate) => ({
            key: device.id,
            label: device.deviceName || '',
            serialNumber: device.serialNumber || '',
            hasOtherTemplate: device.hasTemplate && !device.isCurrentTemplate
          }))

          // 设置已绑定的设备ID列表
          const currentBoundIds = devicesWithConfig
            .filter((device: DeviceWithTemplate) => device.isCurrentTemplate)
            .map((device: DeviceWithTemplate) => device.id)

          boundDeviceIds.value = [...currentBoundIds]
          originalBoundDeviceIds.value = [...currentBoundIds]
        } else {
          // 批量API调用失败，使用设备列表但不包含配置状态
          const devicesWithoutConfig = devices.map((device: any) => ({
            ...device,
            hasTemplate: false,
            isCurrentTemplate: false
          }))

          allDevices.value = devicesWithoutConfig

          // 构建穿梭框数据源
          transferDeviceList.value = devicesWithoutConfig.map((device: any) => ({
            key: device.id,
            label: device.deviceName || '',
            serialNumber: device.serialNumber || '',
            hasOtherTemplate: false
          }))

          boundDeviceIds.value = []
          originalBoundDeviceIds.value = []
        }
      } catch (configError) {
        console.error('获取设备配置失败:', configError)
        // 如果配置查询失败，至少返回设备列表
        const devicesWithoutConfig = devices.map((device: any) => ({
          ...device,
          hasTemplate: false,
          isCurrentTemplate: false
        }))

        allDevices.value = devicesWithoutConfig

        // 构建穿梭框数据源
        transferDeviceList.value = devicesWithoutConfig.map((device: any) => ({
          key: device.id,
          label: device.deviceName,
          serialNumber: device.serialNumber,
          hasOtherTemplate: false
        }))

        boundDeviceIds.value = []
      }
    } else {
      ElMessage.error(response.message || '获取设备列表失败')
    }
  } catch (error) {
    console.error('获取设备列表错误:', error)
    ElMessage.error('获取设备列表失败')
  } finally {
    deviceListLoading.value = false
  }
}

// 穿梭框过滤方法
const filterDevice = (query: string, item: TransferDevice) => {
  const keyword = query.toLowerCase()
  const label = item.label || ''
  const serialNumber = item.serialNumber || ''
  return label.toLowerCase().includes(keyword) ||
         serialNumber.toLowerCase().includes(keyword)
}

// 更新本地设备状态，避免重新调用接口
const updateLocalDeviceStates = (results: any[], direction: 'left' | 'right') => {
  results.forEach(result => {
    if (result.success) {
      const deviceIndex = allDevices.value.findIndex((device: any) => device.id === result.deviceId)
      if (deviceIndex !== -1) {
        if (direction === 'right') {
          // 绑定操作：设置为当前模板
          allDevices.value[deviceIndex].isCurrentTemplate = true
          allDevices.value[deviceIndex].hasTemplate = true
        } else {
          // 解绑操作：清除模板绑定
          allDevices.value[deviceIndex].isCurrentTemplate = false
          allDevices.value[deviceIndex].hasTemplate = false
        }
      }

      // 同时更新穿梭框数据源
      const transferIndex = transferDeviceList.value.findIndex((item: any) => item.key === result.deviceId)
      if (transferIndex !== -1) {
        if (direction === 'right') {
          // 绑定操作：清除"已绑定其他模板"标记
          transferDeviceList.value[transferIndex].hasOtherTemplate = false
        } else {
          // 解绑操作：设备变为未绑定状态
          transferDeviceList.value[transferIndex].hasOtherTemplate = false
        }
      }
    }
  })
}

// 穿梭框变化处理方法
const handleTransferChange = async (newBoundIds: number[], direction: 'left' | 'right', movedKeys: number[]) => {
  if (transferLoading.value) return // 防止重复操作

  transferLoading.value = true

  try {
    let result: any

    if (direction === 'right') {
      // 向右穿梭 - 批量绑定设备
      result = await batchBindDevicesToTemplate(movedKeys, currentConfigTemplate.value!.id)
    } else {
      // 向左穿梭 - 批量解绑设备
      result = await batchUnbindDevicesFromTemplate(movedKeys, currentConfigTemplate.value!.id)
    }

    if (result.success) {
      const action = direction === 'right' ? '绑定' : '解绑'
      const { successCount, failCount, results } = result.data

      let message = `批量${action}完成：成功 ${successCount} 个设备`

      if (failCount > 0) {
        message += `，失败 ${failCount} 个设备`
      }

      ElMessage.success(message)

      // 更新本地设备状态，避免重新调用接口
      updateLocalDeviceStates(results, direction)

      // 更新原始绑定状态
      originalBoundDeviceIds.value = [...newBoundIds]

      // 刷新模板列表以更新设备数量
      fetchTemplates()
    } else {
      ElMessage.error(result.message || `批量${direction === 'right' ? '绑定' : '解绑'}失败`)
      // 操作失败，恢复原始状态
      boundDeviceIds.value = [...originalBoundDeviceIds.value]
    }
  } catch (error) {
    console.error('穿梭操作错误:', error)
    ElMessage.error('操作失败')
    // 操作失败，恢复原始状态
    boundDeviceIds.value = [...originalBoundDeviceIds.value]
  } finally {
    transferLoading.value = false
  }
}

</script>

<style scoped>
.template-container {
  padding: 24px;
  background-color: var(--apple-background);
}

/* 筛选区域卡片 */
.filter-card {
  margin-bottom: 24px;
}

/* 筛选区域样式 */
.filter-section {
  margin-bottom: 0;
}

.filter-buttons {
  display: flex;
  gap: 10px;
  align-items: center;
  height: 40px; /* 与分页信息高度保持一致 */
}

/* 分页信息样式 */
.pagination-info {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 40px; /* 增加高度以匹配其他组件 */
}

.pagination-info .el-pagination {
  padding: 0;
  margin: 0;
}

/* 确保整个筛选区域的所有列都垂直居中 */
.filter-section .el-row {
  align-items: center;
}

.filter-section .el-col {
  display: flex;
  align-items: center;
}

/* JavaScript驱动的瀑布流布局 */
.template-waterfall {
  position: relative;
  width: 100%;
  margin-bottom: 30px;
  min-height: 300px;
}

.template-card {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  /* JavaScript会动态设置position: absolute, left, top, width */
}

.template-card:hover {
  box-shadow: 0 8px 24px 0 rgba(0, 0, 0, 0.15);
  transform: translateY(-4px);
}

/* 禁用状态的模板卡片样式 */
.template-card-disabled {
  opacity: 0.6;
  background-color: #f5f7fa !important;
}

.template-card-disabled .template-card-header {
  background-color: #f0f2f5;
}

.template-card-disabled .template-name {
  color: #909399 !important;
}

.template-card-disabled .template-preview-container {
  background-color: #f0f2f5 !important;
}

.template-card-disabled:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
  transform: none;
}

.template-card-header {
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.template-name {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 模板预览区域 */
.template-preview-container {
  position: relative;
  background: #f8f9fa;
  border-bottom: 1px solid #ebeef5;
  overflow: hidden;
}

/*
 * 三层结构解决 transform: scale() 居中问题的 CSS 样式
 *
 * 层级结构：
 * .template-preview (外层容器)
 *   └── .preview-wrapper (包装器)
 *       └── .preview-canvas (画板)
 */

/* 外层容器：负责 flexbox 居中布局 */
.template-preview {
  position: relative;
  background: white;
  width: 100%;
  overflow: hidden;
  display: flex;                    /* 启用 flexbox 布局 */
  align-items: flex-start;          /* 垂直对齐：顶部对齐 */
  justify-content: center;          /* 水平对齐：居中 - 关键属性 */
}

/* 包装器：提供缩放后的正确占用空间，让 flexbox 能正确计算居中位置 */
.preview-wrapper {
  position: relative;
  background: white;
  /*
   * 重要：尺寸由 getWrapperStyle() 动态计算
   * 宽高 = 原始尺寸 × 缩放比例
   * 这样 flexbox 就能基于正确的尺寸进行居中计算
   */
}

/* 画板：应用 transform: scale() 缩放变换 */
.preview-canvas {
  background: white;
  position: relative;
  min-height: 100px;
  /*
   * 重要：保持原始尺寸，只通过 transform: scale() 进行视觉缩放
   * 样式由 getCanvasStyle() 动态计算
   */
}

.preview-component {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  z-index: 1;
  min-width: 50px;
  min-height: 50px;
  transition: all 0.2s ease;
}

.preview-component:hover {
  border-color: #c0c4cc;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.preview-component-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.preview-component-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 8px;
  background: #f8f9fa;
  border-bottom: 1px solid #ebeef5;
  font-size: 10px;
}

.preview-component-title {
  font-weight: 500;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  margin-right: 4px;
}

.preview-component-body {
  flex: 1;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 40px;
}

.preview-component-chart {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
}

.preview-component-chart img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 2px;
}

.preview-component-placeholder {
  text-align: center;
  color: #909399;
  font-size: 10px;
}

.preview-icon {
  font-size: 16px;
  margin-bottom: 4px;
  color: #c0c4cc;
}

.preview-empty {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #909399;
  font-size: 12px;
}

.preview-empty-icon {
  font-size: 32px;
  margin-bottom: 8px;
  color: #c0c4cc;
}

.preview-empty-tip {
  font-size: 10px;
  color: #a8abb2;
  margin-top: 4px;
}

.template-card-content {
  padding: 5px 15px;
  flex: 1;
  border-bottom: 1px solid #f0f0f0;
}

.template-description {
  color: #606266;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.4;
  height: 1.4em;
}

.template-meta {
  color: #909399;
  font-size: 12px;
}

.template-card-footer {
  padding: 10px 15px;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 禁用模板的按钮样式 */
.template-card-disabled .template-card-footer .el-button:disabled {
  color: #c0c4cc !important;
  cursor: not-allowed !important;
}

.template-card-disabled .template-card-footer .el-button:disabled .el-icon {
  color: #c0c4cc !important;
}



/* 模板详情样式 */
.template-detail {
  padding: 10px;
}

.detail-item {
  margin-bottom: 15px;
}

.detail-label {
  font-weight: 600;
  color: #303133;
  margin-right: 10px;
}

.config-item {
  display: flex;
  flex-direction: column;
}

.config-item pre {
  margin-top: 10px;
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  overflow: auto;
  max-height: 300px;
}

/* 空状态样式 */
.empty-data {
  grid-column: 1 / -1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  margin: 20px 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .filter-buttons {
    text-align: left;
    margin-top: 16px;
  }

  .template-card {
    margin-bottom: 0;
  }
}

/* 设备配置对话框样式 */
.device-config-content {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 420px;
  padding: 20px;
}

/* 穿梭框内设备项样式 */
.transfer-device-item {
  width: 100%;
  padding: 6px 0;
  box-sizing: border-box;
  display: block;
}

.transfer-device-item .device-name {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 2px;
  line-height: 1.2;
  word-break: break-word;
}

.transfer-device-item .device-serial {
  font-size: 12px;
  color: #909399;
  margin-bottom: 2px;
  line-height: 1.2;
}

.transfer-device-item .device-warning {
  font-size: 11px;
  color: #e6a23c;
  background-color: #fdf6ec;
  padding: 1px 4px;
  border-radius: 2px;
  display: inline-block;
}

/* 穿梭框样式调整 */
:deep(.el-transfer) {
  display: inline-block;
}

:deep(.el-transfer .el-transfer-panel) {
  width: 350px;
  height: 400px;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
}

:deep(.el-transfer .el-transfer-panel__header) {
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  padding: 10px 16px;
  font-weight: 600;
  color: #303133;
  height: 40px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

:deep(.el-transfer .el-transfer-panel__body) {
  height: calc(100% - 40px);
  padding: 0;
  display: flex;
  flex-direction: column;
}

:deep(.el-transfer .el-transfer-panel__filter) {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-transfer .el-transfer-panel__list) {
  flex: 1;
  overflow-y: auto;
  margin: 0;
  padding: 0;
}

:deep(.el-transfer .el-transfer-panel__item) {
  padding: 0;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
  height: auto;
}

:deep(.el-transfer .el-transfer-panel__item:last-child) {
  border-bottom: none;
}

:deep(.el-transfer .el-transfer-panel__item:hover) {
  background-color: #f5f7fa;
}

:deep(.el-transfer .el-transfer-panel__item .el-checkbox) {
  width: 100%;
  margin: 0;
  padding: 0;
  height: auto;
  display: block;
}

:deep(.el-transfer .el-transfer-panel__item .el-checkbox__input) {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  margin: 0;
}

:deep(.el-transfer .el-transfer-panel__item .el-checkbox__label) {
  width: 100%;
  padding: 8px 16px 8px 36px;
  margin: 0;
  display: block;
  line-height: 1.2;
  min-height: 45px;
  box-sizing: border-box;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
<template>
  <div class="template-form-container">
    <div class="header-container">
      <el-page-header @back="goBack">
        <template #content>
          <span class="template-title">{{ isEdit ? '编辑模板' : '创建模板' }}</span>
        </template>
      </el-page-header>
    </div>

    <div class="content-layout">
      <!-- 左侧图表组件列表 -->
      <div
        class="chart-list-container"
        :style="{ height: leftContainerHeight + 'px' }"
      >
        <el-card class="chart-list-card">
          <div class="chart-list-header">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索组件"
              prefix-icon="el-icon-search"
              clearable
              class="search-input"
              @input="debouncedFilterCharts"
            />
          </div>
          <div class="chart-list">
            <el-empty
              v-if="filteredCharts.length === 0"
              description="暂无图表组件"
            />
            <div
              v-for="chart in filteredCharts"
              :key="chart.id"
              class="chart-item"
              draggable="true"
              @dragstart="handleDragStart(chart, $event)"
              @dragend="handleDragEnd"
            >
              <el-card shadow="hover">
                <div
                  v-if="getChartThumbnail(chart)"
                  class="chart-thumbnail"
                >
                  <img
                    :src="getChartThumbnail(chart)"
                    :alt="chart.name"
                    class="thumbnail-image"
                  >
                </div>
                <div class="chart-item-content">
                  <div class="chart-item-name">
                    {{ chart.name }}
                  </div>
                  <div
                    v-if="chart.description"
                    class="chart-item-desc"
                  >
                    {{ chart.description }}
                  </div>
                </div>
              </el-card>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 右侧画板区域 -->
      <div class="canvas-container">
        <!-- 基本信息表单 -->
        <el-card class="template-info-card">
          <el-form
            ref="templateFormRef"
            :model="templateForm"
            :rules="formRules"
            label-width="100px"
            class="template-info-form"
          >
            <el-row :gutter="20">
              <el-col :span="10">
                <el-form-item
                  label="模板名称"
                  prop="name"
                >
                  <el-input
                    v-model="templateForm.name"
                    placeholder="请输入模板名称"
                    size="small"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="10">
                <el-form-item
                  label="模板描述"
                  prop="description"
                >
                  <el-input
                    v-model="templateForm.description"
                    placeholder="请输入模板描述"
                    maxlength="200"
                    size="small"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="4">
                <el-form-item>
                  <el-button
                    type="primary"
                    :loading="submitting"
                    size="small"
                    @click="submitForm"
                  >
                    {{ isEdit ? '保存' : '创建' }}
                  </el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>

        <!-- 画板区域 -->
        <el-card class="canvas-card">
          <template #header>
            <div class="canvas-header">
              <span>模板画板</span>
              <div class="canvas-tools">
                <el-switch
                  v-model="snapToGrid"
                  size="small"
                  active-text="网格对齐"
                  inactive-text=""
                  style="margin-right: 12px;"
                />
                <el-button
                  size="small"
                  @click="clearCanvas"
                >
                  清空画板
                </el-button>
                <el-button
                  size="small"
                  @click="previewTemplate"
                >
                  预览
                </el-button>
              </div>
            </div>
          </template>

          <div
            ref="canvasRef"
            class="canvas-area"
            :class="{
              'drag-over': isDragOver,
              'grid-enabled': snapToGrid
            }"
            :style="{
              height: canvasHeight + 'px',
              '--grid-size': gridSize + 'px'
            }"
            @drop="handleDrop"
            @dragover="handleDragOver"
            @dragenter="handleDragEnter"
            @dragleave="handleDragLeave"
            @mousedown="handleCanvasMouseDown"
          >
            <div
              v-if="canvasComponents.length === 0"
              class="canvas-placeholder"
            >
              <el-icon class="placeholder-icon">
                <Plus />
              </el-icon>
              <p>拖拽左侧图表组件到此处开始设计</p>
            </div>

            <!-- 画板上的组件 -->
            <div
              v-for="component in canvasComponents"
              :key="component.id"
              class="canvas-component"
              :class="{
                'active': activeComponent?.id === component.id,
                'dragging': isDragging && activeComponent?.id === component.id,
                'resizing': isResizing && activeComponent?.id === component.id
              }"
              :style="{
                left: component.x + 'px',
                top: component.y + 'px',
                width: component.width + 'px',
                height: component.height + 'px'
              }"
              @mousedown="handleComponentMouseDown(component, $event)"
            >
              <div class="component-content">
                <div class="component-header">
                  <span class="component-title">{{ component.name }}</span>
                  <el-button
                    size="small"
                    type="danger"
                    text
                    @click="removeComponent(component.id)"
                  >
                    <el-icon><Close /></el-icon>
                  </el-button>
                </div>
                <div class="component-body">
                  <div
                    v-if="component.thumbnail"
                    class="component-preview"
                  >
                    <img
                      :src="component.thumbnail"
                      :alt="component.name"
                    >
                  </div>
                  <div
                    v-else
                    class="component-placeholder"
                  >
                    {{ component.type }} 图表
                  </div>
                </div>
              </div>

              <!-- 调整大小的控制点 -->
              <div
                class="resize-handle resize-se"
                @mousedown.stop="handleResizeStart(component, $event)"
              />
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 预览对话框 -->
    <el-dialog
      v-model="previewVisible"
      title="模板预览"
      width="90%"
      :close-on-click-modal="false"
      class="preview-dialog"
    >
      <div class="preview-container">
        <div class="preview-toolbar">
          <span class="preview-info">预览模式 - {{ templateForm.name || '未命名模板' }}</span>
          <div class="preview-controls">
            <el-button-group size="small">
              <el-button
                :type="previewScale === 0.5 ? 'primary' : ''"
                @click="setPreviewScale(0.5)"
              >
                50%
              </el-button>
              <el-button
                :type="previewScale === 0.75 ? 'primary' : ''"
                @click="setPreviewScale(0.75)"
              >
                75%
              </el-button>
              <el-button
                :type="previewScale === 1 ? 'primary' : ''"
                @click="setPreviewScale(1)"
              >
                100%
              </el-button>
            </el-button-group>
          </div>
        </div>

        <div
          class="preview-content"
          :style="{ transform: `scale(${previewScale})` }"
        >
          <div
            class="preview-canvas"
            :style="{
              width: previewCanvasWidth + 'px',
              height: previewCanvasHeight + 'px'
            }"
          >
            <!-- 预览中的组件 -->
            <div
              v-for="component in canvasComponents"
              :key="'preview-' + component.id"
              class="preview-component"
              :style="{
                left: component.x + 'px',
                top: component.y + 'px',
                width: component.width + 'px',
                height: component.height + 'px'
              }"
            >
              <div class="preview-component-content">
                <div class="preview-component-header">
                  <span class="preview-component-title">{{ component.name }}</span>
                  <el-tag
                    size="small"
                    type="info"
                  >
                    {{ component.type }}
                  </el-tag>
                </div>
                <div class="preview-component-body">
                  <div
                    v-if="component.thumbnail"
                    class="preview-component-chart"
                  >
                    <img
                      :src="component.thumbnail"
                      :alt="component.name"
                    >
                  </div>
                  <div
                    v-else
                    class="preview-component-placeholder"
                  >
                    <el-icon class="preview-icon">
                      <TrendCharts />
                    </el-icon>
                    <p>{{ component.type }} 图表</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 空状态 -->
            <div
              v-if="canvasComponents.length === 0"
              class="preview-empty"
            >
              <el-icon class="preview-empty-icon">
                <DocumentCopy />
              </el-icon>
              <p>模板为空</p>
              <p class="preview-empty-desc">
                请先在画板中添加图表组件
              </p>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="preview-footer">
          <span class="preview-stats">
            共 {{ canvasComponents.length }} 个组件
          </span>
          <el-button @click="previewVisible = false">
            关闭预览
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/**
 * 模板表单页面
 * 提供拖拽式图表组件配置界面，支持创建和编辑模板
 */

import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Plus, Close, TrendCharts, DocumentCopy } from '@element-plus/icons-vue'
import { getAllCharts } from '@/api/chartConfig'
import { getTemplateDetail, createTemplate, updateTemplate } from '@/api/template'
import { debouncedFilter, throttledDrag, debouncedConfigUpdate } from '@/utils/debounce'

// 图表组件类型
interface ChartComponent {
  id: string
  name: string
  description: string
  type: string
  thumbnail?: string
  config?: any
}

// 画板组件类型
interface CanvasComponent {
  id: string
  chartId: string
  name: string
  type: string
  x: number
  y: number
  width: number
  height: number
  minWidth?: number
  minHeight?: number
  config?: any
}

// 模板表单类型
interface TemplateForm {
  name: string
  description: string
  config: string
}

// 位置信息类型
interface Position {
  x: number
  y: number
}

// 尺寸信息类型
interface Size {
  width: number
  height: number
}

// 画板尺寸缓存类型
interface CanvasRect {
  left: number
  top: number
  width: number
  height: number
}

const route = useRoute()
const router = useRouter()
const templateId = computed(() => route.params.id as string)
const isEdit = computed(() => !!templateId.value)
const submitting = ref<boolean>(false)

// 图表组件相关
const allCharts = ref<ChartComponent[]>([])
const filteredCharts = ref<ChartComponent[]>([])
const searchKeyword = ref<string>('')
const loading = ref<boolean>(false)

// 表单引用
const templateFormRef = ref<any>(null)

// 画板相关
const canvasRef = ref<HTMLDivElement | null>(null)
const canvasComponents = ref<CanvasComponent[]>([])
const isDragOver = ref<boolean>(false)
const draggedChart = ref<ChartComponent | null>(null)

// 拖拽和调整大小相关
const isDragging = ref<boolean>(false)
const isResizing = ref<boolean>(false)
const dragStartPos = ref<Position>({ x: 0, y: 0 })
const componentStartPos = ref<Position>({ x: 0, y: 0 })
const resizeStartSize = ref<Size>({ width: 0, height: 0 })
const activeComponent = ref<CanvasComponent | null>(null)

// 缓存画板尺寸信息，减少DOM查询
const cachedCanvasRect = ref<CanvasRect>({ left: 0, top: 0, width: 800, height: 500 })

// 更新画板尺寸缓存
const updateCanvasRectCache = () => {
  if (canvasRef.value) {
    const rect = canvasRef.value.getBoundingClientRect()
    cachedCanvasRect.value = {
      left: rect.left,
      top: rect.top,
      width: rect.width,
      height: rect.height
    }
  }
}

// 预览相关
const previewVisible = ref(false)
const previewScale = ref(1)
const previewCanvasWidth = ref(800)
const previewCanvasHeight = ref(600)

// 画板动态高度相关
const canvasHeight = ref(0)
const minCanvasHeight = ref(0)
let resizeTimer = null

// 左侧容器高度计算 - 跟随右侧画板高度变化
const leftContainerHeight = computed(() => {

  // 右侧容器的总高度 = 画板高度 + 顶部padding + 底部padding + 头部区域高度 + gap + 顶部名称区域高度
  const rightContainerTotalHeight = canvasHeight.value + 20 + 20 + 57 + 16 + 90

  // 左侧容器高度跟随右侧容器的总高度，但不小于基础高度
  return rightContainerTotalHeight
})

// ID生成器
let componentIdCounter = 0
const generateComponentId = () => {
  return `component_${Date.now()}_${++componentIdCounter}`
}

// 网格对齐配置
const gridSize = ref(10) // 网格大小，可以调整
const snapToGrid = ref(true) // 是否启用网格对齐

// 网格对齐函数
const snapToGridValue = (value) => {
  if (!snapToGrid.value) return value
  return Math.round(value / gridSize.value) * gridSize.value
}

// 表单数据
const templateForm = reactive({
  name: '',
  description: '',
  config: '{}',
  status: 1
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入模板名称', trigger: 'blur' },
    { max: 64, message: '名称长度不能超过64个字符', trigger: 'blur' }
  ]
}

// 初始化画板高度
const initCanvasHeight = () => {
  // 等待DOM渲染完成后计算精确高度
  nextTick(() => {
    if (!canvasRef.value) return

    // 获取画板元素的位置信息
    const canvasRect = canvasRef.value.getBoundingClientRect()
    const viewportHeight = window.innerHeight

    // 计算画板顶部到屏幕顶部的距离
    const canvasTop = canvasRect.top

    // 计算可用的高度：屏幕高度 - 画板顶部位置 - 底部边距(20px)
    const bottomPadding = 60 // 与屏幕底部保留20px距离
    const availableHeight = viewportHeight - canvasTop - bottomPadding

    minCanvasHeight.value = Math.max(400, availableHeight) // 最小400px

    // 如果当前画板高度小于最小高度，则更新为最小高度
    // 这样可以确保编辑模式下不会缩小已有内容的高度
    if (canvasHeight.value < minCanvasHeight.value) {
      canvasHeight.value = minCanvasHeight.value
    }
  })
}

// 防抖的窗口大小变化处理
const handleWindowResize = () => {
  if (resizeTimer) {
    clearTimeout(resizeTimer)
  }
  resizeTimer = setTimeout(() => {
    updateCanvasRectCache() // 更新缓存
    initCanvasHeight()
  }, 300) // 300ms防抖
}

// 计算画板所需的最小高度
const calculateRequiredCanvasHeight = () => {
  if (canvasComponents.value.length === 0) {
    return minCanvasHeight.value
  }

  // 找到最底部组件的位置
  let maxBottom = 0
  canvasComponents.value.forEach(component => {
    const bottom = component.y + component.height
    if (bottom > maxBottom) {
      maxBottom = bottom
    }
  })

  // 添加一些底部边距
  const bottomPadding = 100
  const requiredHeight = maxBottom + bottomPadding

  // 返回所需高度和最小高度中的较大值
  return Math.max(requiredHeight, minCanvasHeight.value)
}

// 更新画板高度
const updateCanvasHeight = () => {
  const requiredHeight = calculateRequiredCanvasHeight()
  if (requiredHeight !== canvasHeight.value) {
    canvasHeight.value = requiredHeight
  }
}

// 初始化
onMounted(async () => {
  // 加载图表组件数据
  await fetchChartComponents()

  if (isEdit.value) {
    await fetchTemplateData()
    // 编辑模式：先根据内容计算高度，再确保不小于屏幕高度
    await nextTick()
    updateCanvasRectCache() // 初始化缓存
    updateCanvasHeight() // 根据已有组件计算高度
    initCanvasHeight() // 确保不小于屏幕高度
  } else {
    // 创建模式：直接使用屏幕高度
    await nextTick()
    updateCanvasRectCache() // 初始化缓存
    initCanvasHeight()
  }

  // 监听窗口大小变化
  window.addEventListener('resize', handleWindowResize)
})

// 清理
onUnmounted(() => {
  window.removeEventListener('resize', handleWindowResize)
  if (resizeTimer) {
    clearTimeout(resizeTimer)
  }

  // 清理可能残留的全局事件监听器
  document.removeEventListener('mousemove', handleComponentMouseMove)
  document.removeEventListener('mouseup', handleComponentMouseUp)
  document.removeEventListener('mousemove', handleResizeMouseMove)
  document.removeEventListener('mouseup', handleResizeMouseUp)
})

// 获取图表组件数据
const fetchChartComponents = async () => {
  loading.value = true
  try {
    const response = await getAllCharts({
      limit: 100,
      offset: 0,
      status: 1 // 只获取启用状态的组件
    })
    if (response.success) {
      allCharts.value = response.data.list
      filteredCharts.value = [...allCharts.value]
    } else {
      ElMessage.warning('获取图表组件失败')
    }
  } catch (error) {
    console.error('获取图表组件错误:', error)
    ElMessage.error('获取图表组件数据失败')
  } finally {
    loading.value = false
  }
}

// 过滤图表组件
const filterCharts = () => {
  if (!searchKeyword.value) {
    filteredCharts.value = [...allCharts.value]
    return
  }

  const keyword = searchKeyword.value.toLowerCase()
  filteredCharts.value = allCharts.value.filter(chart =>
    chart.name.toLowerCase().includes(keyword) ||
    chart.type.toLowerCase().includes(keyword) ||
    (chart.description && chart.description.toLowerCase().includes(keyword))
  )
}

// 使用防抖的过滤函数
const debouncedFilterCharts = debouncedFilter(filterCharts)

// 拖拽开始
const handleDragStart = (chart, event) => {
  draggedChart.value = chart
  event.dataTransfer.effectAllowed = 'copy'
  event.dataTransfer.setData('text/plain', JSON.stringify(chart))
}

// 拖拽结束
const handleDragEnd = () => {
  draggedChart.value = null
}

// 拖拽进入画板
const handleDragEnter = (event) => {
  event.preventDefault()
  isDragOver.value = true
  // 更新画板尺寸缓存，确保拖拽时位置计算准确
  updateCanvasRectCache()
}

// 拖拽在画板上移动
const handleDragOver = (event) => {
  event.preventDefault()
  event.dataTransfer.dropEffect = 'copy'
}

// 拖拽离开画板
const handleDragLeave = (event) => {
  // 只有当离开画板区域时才设置为false
  if (!canvasRef.value?.contains(event.relatedTarget)) {
    isDragOver.value = false
  }
}

// 放置到画板
const handleDrop = (event) => {
  event.preventDefault()
  isDragOver.value = false

  try {
    const chartData = JSON.parse(event.dataTransfer.getData('text/plain'))

    // 实时获取画板位置信息，确保位置计算准确
    if (!canvasRef.value) return
    const canvasRect = canvasRef.value.getBoundingClientRect()

    // 计算鼠标在画板内的相对位置
    const x = event.clientX - canvasRect.left
    const y = event.clientY - canvasRect.top

    // 从配置中获取默认尺寸
    const defaultSize = getChartDefaultSize(chartData)
    const componentWidth = defaultSize.width
    const componentHeight = defaultSize.height

    // 计算放置位置，确保组件完全在画板内
    const canvasWidth = canvasRect.width
    const canvasHeight = canvasRect.height

    // 计算初始位置 - 以鼠标位置为中心放置组件
    let componentX = Math.max(0, Math.min(x - componentWidth / 2, canvasWidth - componentWidth))
    let componentY = Math.max(0, Math.min(y - componentHeight / 2, canvasHeight - componentHeight))

    // 应用网格对齐
    componentX = snapToGridValue(componentX)
    componentY = snapToGridValue(componentY)

    // 创建新的画板组件
    const newComponent = {
      id: generateComponentId(),
      chartId: chartData.id,
      name: chartData.name,
      type: chartData.type,
      component: chartData.component,
      config: chartData.config,
      thumbnail: getChartThumbnail(chartData),
      x: componentX,
      y: componentY,
      width: componentWidth,
      height: componentHeight,
      minWidth: componentWidth,  // 设置最小宽度为默认宽度
      minHeight: componentHeight // 设置最小高度为默认高度
    }

    canvasComponents.value.push(newComponent)
    updateTemplateConfig()
    updateCanvasHeight()

    ElMessage.success(`已添加图表组件: ${chartData.name}`)
  } catch (error) {
    console.error('添加组件到画板失败:', error)
    ElMessage.error('添加组件失败')
  }
}

// 移除画板组件
const removeComponent = (componentId) => {
  const index = canvasComponents.value.findIndex(c => c.id === componentId)
  if (index > -1) {
    canvasComponents.value.splice(index, 1)
    updateTemplateConfig()
    updateCanvasHeight()
    ElMessage.success('组件已移除')
  }
}

// 清空画板
const clearCanvas = () => {
  canvasComponents.value = []
  updateTemplateConfig()
  updateCanvasHeight()
  ElMessage.success('画板已清空')
}

// 预览模板
const previewTemplate = () => {
  if (canvasComponents.value.length === 0) {
    ElMessage.warning('画板为空，无法预览')
    return
  }

  // 获取当前画板尺寸作为预览尺寸
  if (canvasRef.value) {
    const rect = canvasRef.value.getBoundingClientRect()
    previewCanvasWidth.value = rect.width
    previewCanvasHeight.value = rect.height
  }

  previewVisible.value = true
}

// 设置预览缩放比例
const setPreviewScale = (scale) => {
  previewScale.value = scale
}

// 更新模板配置
const updateTemplateConfig = () => {
  // 获取实际画板尺寸
  let canvasWidth = 800
  let canvasHeight = 500

  if (canvasRef.value) {
    const rect = canvasRef.value.getBoundingClientRect()
    canvasWidth = rect.width
    canvasHeight = rect.height
  }

  const config = {
    canvas: {
      width: canvasWidth,
      height: canvasHeight
    },
    components: canvasComponents.value.map(comp => ({
      id: comp.id,
      chartId: comp.chartId,
      name: comp.name,
      type: comp.type,
      component: comp.component,
      config: comp.config,
      position: {
        x: comp.x,
        y: comp.y
      },
      size: {
        width: comp.width,
        height: comp.height
      }
    }))
  }
  templateForm.config = JSON.stringify(config, null, 2)
}

// 使用防抖的配置更新函数
const debouncedUpdateTemplateConfig = debouncedConfigUpdate(() => {
  updateTemplateConfig()
  updateCanvasHeight()
})

// 组件鼠标按下事件（开始拖拽移动）
const handleComponentMouseDown = (component, event) => {
  if (event.target.classList.contains('resize-handle')) {
    return // 如果点击的是调整大小的控制点，不处理移动
  }

  activeComponent.value = component
  isDragging.value = true

  dragStartPos.value = {
    x: event.clientX,
    y: event.clientY
  }

  componentStartPos.value = {
    x: component.x,
    y: component.y
  }

  // 添加全局鼠标事件监听
  document.addEventListener('mousemove', handleComponentMouseMove)
  document.addEventListener('mouseup', handleComponentMouseUp)

  event.preventDefault()
}

// 使用节流的位置更新函数
const throttledPositionUpdate = throttledDrag((event) => {
  if (!isDragging.value || !activeComponent.value || !canvasRef.value) return

  const deltaX = event.clientX - dragStartPos.value.x
  const deltaY = event.clientY - dragStartPos.value.y

  // 使用缓存的画板宽度
  const canvasWidth = cachedCanvasRect.value.width

  // 计算新位置
  let newX = componentStartPos.value.x + deltaX
  let newY = componentStartPos.value.y + deltaY

  // 应用网格对齐
  newX = snapToGridValue(newX)
  newY = snapToGridValue(newY)

  // 限制在画板范围内，但允许Y轴超出当前高度（会自动扩展）
  activeComponent.value.x = Math.max(0, Math.min(newX, canvasWidth - activeComponent.value.width))
  activeComponent.value.y = Math.max(0, newY)
})

// 组件鼠标移动事件
const handleComponentMouseMove = (event) => {
  throttledPositionUpdate(event)
}

// 组件鼠标释放事件
const handleComponentMouseUp = () => {
  if (isDragging.value) {
    isDragging.value = false
    activeComponent.value = null
    // 使用防抖的配置更新
    debouncedUpdateTemplateConfig()
  }

  // 移除全局事件监听
  document.removeEventListener('mousemove', handleComponentMouseMove)
  document.removeEventListener('mouseup', handleComponentMouseUp)
}

// 开始调整大小
const handleResizeStart = (component, event) => {
  activeComponent.value = component
  isResizing.value = true

  dragStartPos.value = {
    x: event.clientX,
    y: event.clientY
  }

  resizeStartSize.value = {
    width: component.width,
    height: component.height
  }

  // 添加全局鼠标事件监听
  document.addEventListener('mousemove', handleResizeMouseMove)
  document.addEventListener('mouseup', handleResizeMouseUp)

  event.preventDefault()
  event.stopPropagation()
}

// 调整大小鼠标移动事件
const handleResizeMouseMove = (event) => {
  if (!isResizing.value || !activeComponent.value || !canvasRef.value) return

  const deltaX = event.clientX - dragStartPos.value.x
  const deltaY = event.clientY - dragStartPos.value.y

  // 使用缓存的画板宽度
  const canvasWidth = cachedCanvasRect.value.width

  // 计算新尺寸
  let newWidth = resizeStartSize.value.width + deltaX
  let newHeight = resizeStartSize.value.height + deltaY

  // 应用网格对齐到尺寸
  newWidth = snapToGridValue(newWidth)
  newHeight = snapToGridValue(newHeight)

  // 获取组件的最小尺寸，如果没有设置则使用默认值
  const minWidth = activeComponent.value.minWidth || 100
  const minHeight = activeComponent.value.minHeight || 80

  // 限制最小尺寸和宽度不能超出画板边界，高度可以自由扩展
  const maxWidth = canvasWidth - activeComponent.value.x

  activeComponent.value.width = Math.max(minWidth, Math.min(newWidth, maxWidth))
  activeComponent.value.height = Math.max(minHeight, newHeight)
}

// 调整大小鼠标释放事件
const handleResizeMouseUp = () => {
  if (isResizing.value) {
    isResizing.value = false
    activeComponent.value = null
    updateTemplateConfig()
    updateCanvasHeight()
  }

  // 移除全局事件监听
  document.removeEventListener('mousemove', handleResizeMouseMove)
  document.removeEventListener('mouseup', handleResizeMouseUp)
}

// 获取模板数据
const fetchTemplateData = async () => {
  try {
    const response = await getTemplateDetail(templateId.value)
    if (response.success) {
      const { name, description, config, status } = response.data

      // 填充表单数据
      templateForm.name = name
      templateForm.description = description || ''
      templateForm.config = config || '{}'
      templateForm.status = status

      // 解析配置并加载画板组件
      loadCanvasFromConfig(config)
    } else {
      ElMessage.error('获取模板数据失败')
      router.push('/template')
    }
  } catch (error) {
    console.error('获取模板数据错误:', error)
    ElMessage.error('获取模板数据失败')
    router.push('/template')
  }
}

// 从配置中加载画板组件
const loadCanvasFromConfig = (configStr) => {
  try {
    if (!configStr) return

    const config = JSON.parse(configStr)
    if (config.components && Array.isArray(config.components)) {
      canvasComponents.value = config.components.map(comp => {
        // 获取组件的默认尺寸作为最小尺寸
        const defaultSize = getChartDefaultSize({ config: comp.config })
        const componentWidth = comp.size?.width || defaultSize.width
        const componentHeight = comp.size?.height || defaultSize.height

        return {
          id: comp.id || generateComponentId(),
          chartId: comp.chartId,
          name: comp.name,
          type: comp.type,
          component: comp.component,
          config: comp.config,
          thumbnail: comp.thumbnail || getChartThumbnailFromConfig(comp.config),
          x: comp.position?.x || 0,
          y: comp.position?.y || 0,
          width: componentWidth,
          height: componentHeight,
          minWidth: defaultSize.width,   // 设置最小宽度为配置中的默认宽度
          minHeight: defaultSize.height  // 设置最小高度为配置中的默认高度
        }
      })
    }
  } catch (error) {
    console.error('解析模板配置失败:', error)
    // 如果解析失败，保持画板为空
    canvasComponents.value = []
  }
}

// 从配置中获取缩略图
const getChartThumbnailFromConfig = (configStr) => {
  try {
    if (!configStr) return null
    const config = typeof configStr === 'string' ? JSON.parse(configStr) : configStr
    return config.thumbnail || null
  } catch (error) {
    return null
  }
}

// 画板鼠标按下事件（取消选中组件）
const handleCanvasMouseDown = (event) => {
  // 如果点击的是画板空白区域，取消选中组件
  if (event.target === canvasRef.value) {
    activeComponent.value = null
    event.preventDefault()
  }
}

// 提交表单
const submitForm = async () => {
  if (!templateFormRef.value) return
  
  await templateFormRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      
      try {
        // 处理配置字段，确保是有效的JSON
        let configValue = templateForm.config
        try {
          // 尝试解析并重新格式化JSON
          const parsedConfig = JSON.parse(configValue)
          configValue = JSON.stringify(parsedConfig)
        } catch (error) {
          // 如果解析失败，使用默认的空对象
          configValue = '{}'
        }
        
        // 构造提交数据
        const formData = {
          name: templateForm.name,
          description: templateForm.description,
          config: configValue,
          status: templateForm.status
        }
        
        let response
        if (isEdit.value) {
          // 更新模板
          response = await updateTemplate(templateId.value, formData)
        } else {
          // 创建模板
          response = await createTemplate(formData)
        }

        if (response.success) {
          ElMessage.success(isEdit.value ? '模板更新成功' : '模板创建成功')
          router.push('/template')
        } else {
          ElMessage.error(response.message || '操作失败')
        }
      } catch (error) {
        console.error('提交表单错误:', error)
        ElMessage.error('操作失败')
      } finally {
        submitting.value = false
      }
    }
  })
}

// 返回上一页
const goBack = () => {
  router.push('/template')
}

// 从图表组件配置中获取缩略图URL
const getChartThumbnail = (chart) => {
  if (!chart.config) return null;

  try {
    const config = JSON.parse(chart.config);
    return config.thumbnail || null;
  } catch (error) {
    console.error('解析配置失败:', error);
    return null;
  }
}

// 从图表组件配置中获取默认尺寸
const getChartDefaultSize = (chart) => {
  const defaultSize = { width: 200, height: 150 }

  if (!chart.config) return defaultSize;

  try {
    const config = JSON.parse(chart.config);
    if (config.size && typeof config.size === 'object') {
      return {
        width: config.size.width || defaultSize.width,
        height: config.size.height || defaultSize.height
      }
    }
    return defaultSize;
  } catch (error) {
    console.error('解析配置失败:', error);
    return defaultSize;
  }
}
</script>

<style scoped>
.template-form-container {
  padding: 24px;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  overflow: auto;
}

.header-container {
  margin-bottom: 20px;
  flex-shrink: 0;
}

.template-title {
  font-size: 18px;
  font-weight: bold;
}

.content-layout {
  display: flex;
  align-items: stretch;
  gap: 20px;
  flex: 1;
  overflow: visible;
}

.chart-list-container {
  width: 300px;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  /* 高度现在通过动态计算设置，不再使用固定高度 */
  /* 添加高度变化的动画效果，与右侧画板同步 */
  transition: height 0.3s ease;
}

.chart-list-card {
  height: 100%; /* 占满容器高度 */
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 防止卡片本身溢出 */
}

/* 确保el-card的body能正确处理滚动 */
.chart-list-card :deep(.el-card__body) {
  flex: 1; /* 占满剩余空间 */
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 防止body溢出 */
  padding: 20px; /* 保持默认padding */
}

.chart-list-header {
  margin-bottom: 16px;
  flex-shrink: 0; /* 防止header被压缩 */
}

.search-input {
  margin-top: 8px;
  margin-bottom: 16px;
  flex-shrink: 0; /* 防止搜索框被压缩 */
}

.chart-list {
  flex: 1; /* 占满剩余空间 */
  overflow-y: auto; /* 启用垂直滚动 */
  overflow-x: hidden; /* 隐藏水平滚动 */
  padding: 8px;
  padding-right: 12px; /* 为滚动条留出空间 */
  /* 改为flex布局，避免grid自动调整子项高度 */
  display: flex;
  flex-direction: column;
  gap: 16px;
  /* 确保滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 transparent;
}

/* 自定义滚动条样式 */
.chart-list::-webkit-scrollbar {
  width: 6px;
}

.chart-list::-webkit-scrollbar-track {
  background: transparent;
}

.chart-list::-webkit-scrollbar-thumb {
  background-color: #c1c1c1;
  border-radius: 3px;
}

.chart-list::-webkit-scrollbar-thumb:hover {
  background-color: #a1a1a1;
}

.chart-item {
  flex-shrink: 0; /* 防止被flex容器压缩 */
  cursor: grab;
  border-radius: 8px;
  transition: all 0.3s ease;
  border: 1px solid transparent;
  overflow: hidden;
}

.chart-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  border-color: #409EFF;
}

.chart-item:active {
  cursor: grabbing;
}

.chart-thumbnail {
  margin-bottom: 0;
  width: 100%;
  height: 140px;
  overflow: hidden;
  position: relative;
  background-color: #f0f2f5;
}

.chart-thumbnail::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background: linear-gradient(0deg, rgba(0,0,0,0.02) 0%, rgba(255,255,255,0) 100%);
  z-index: 1;
}

.thumbnail-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.chart-item:hover .thumbnail-image {
  transform: scale(1.05);
}

.chart-item-content {
  padding: 16px;
  background-color: white;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.chart-item-name {
  font-weight: 600;
  font-size: 16px;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 4px;
}

.chart-item-desc {
  font-size: 13px;
  color: #606266;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 画板容器样式 */
.canvas-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  min-height: calc(100vh - 150px);
}

.template-info-card {
  flex-shrink: 0;
}

.template-info-form {
  margin: 0;
}

.canvas-card {
  display: flex;
  flex-direction: column;
}

.canvas-card :deep(.el-card__body) {
  display: flex;
  flex-direction: column;
  padding: 20px;
}

.canvas-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.canvas-tools {
  display: flex;
  gap: 8px;
}

.canvas-area {
  position: relative;
  width: 100%;
  background: #fafafa;
  border: 2px dashed #ddd;
  border-radius: 8px;
  transition: all 0.3s ease;
  overflow: hidden;
}

.canvas-area.drag-over {
  border-color: #409EFF;
  background: #f0f9ff;
}

/* 网格背景样式 */
.canvas-area.grid-enabled {
  background-image:
    linear-gradient(to right, #e0e0e0 1px, transparent 1px),
    linear-gradient(to bottom, #e0e0e0 1px, transparent 1px);
  background-size: var(--grid-size, 10px) var(--grid-size, 10px);
  background-position: 0 0, 0 0;
}

.canvas-area.grid-enabled::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(to right, #d0d0d0 1px, transparent 1px),
    linear-gradient(to bottom, #d0d0d0 1px, transparent 1px);
  background-size: calc(var(--grid-size, 10px) * 5) calc(var(--grid-size, 10px) * 5);
  background-position: 0 0, 0 0;
  pointer-events: none;
  opacity: 0.3;
  z-index: 0;
}

.canvas-placeholder {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #999;
}

.placeholder-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #ddd;
}

.canvas-component {
  position: absolute;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: move;
  transition: box-shadow 0.2s ease;
  user-select: none;
  z-index: 1; /* 确保组件在网格上方 */
}

.canvas-component:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  border-color: #409EFF;
}

.canvas-component.active {
  border-color: #409EFF;
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.3);
}

.canvas-component.dragging {
  opacity: 0.8;
  z-index: 1000;
}

.canvas-component.resizing {
  border-color: #67C23A;
  box-shadow: 0 4px 16px rgba(103, 194, 58, 0.3);
}

.component-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.component-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f5f5f5;
  border-bottom: 1px solid #eee;
  border-radius: 8px 8px 0 0;
}

.component-title {
  font-size: 12px;
  font-weight: 500;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  margin-right: 8px;
}

.component-body {
  flex: 1;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden; /* 防止内容溢出 */
}

.component-preview {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden; /* 防止图片溢出 */
}

.component-preview img {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  object-fit: contain;
  border-radius: 4px;
}

.component-placeholder {
  color: #999;
  font-size: 12px;
  text-align: center;
}

/* 调整大小控制点 */
.resize-handle {
  position: absolute;
  background: #409EFF;
  border: 1px solid white;
  border-radius: 2px;
}

.resize-se {
  bottom: -4px;
  right: -4px;
  width: 8px;
  height: 8px;
  cursor: se-resize;
}

/* 预览对话框样式 */
.preview-dialog :deep(.el-dialog) {
  margin-top: 5vh;
  margin-bottom: 5vh;
  height: 90vh;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

.preview-dialog :deep(.el-dialog__body) {
  flex: 1;
  padding: 0;
  overflow: hidden;
}

.preview-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.preview-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #ebeef5;
  background: #f8f9fa;
}

.preview-info {
  font-weight: 500;
  color: #303133;
}

.preview-content {
  flex: 1;
  overflow: auto;
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  background: #f0f2f5;
  transform-origin: center top;
}

.preview-canvas {
  background: white;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.preview-component {
  position: absolute;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background: white;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.preview-component-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.preview-component-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border-bottom: 1px solid #ebeef5;
}

.preview-component-title {
  font-size: 12px;
  font-weight: 500;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  margin-right: 8px;
}

.preview-component-body {
  flex: 1;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden; /* 防止内容溢出 */
}

.preview-component-chart {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden; /* 防止图片溢出 */
}

.preview-component-chart img {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  object-fit: contain;
  border-radius: 4px;
}

.preview-component-placeholder {
  text-align: center;
  color: #909399;
}

.preview-icon {
  font-size: 24px;
  margin-bottom: 8px;
  color: #c0c4cc;
}

.preview-empty {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #909399;
}

.preview-empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #c0c4cc;
}

.preview-empty-desc {
  font-size: 12px;
  color: #c0c4cc;
  margin-top: 8px;
}

.preview-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.preview-stats {
  color: #606266;
  font-size: 14px;
}

/* 响应式调整 */
@media (max-width: 992px) {
  .content-layout {
    flex-direction: column;
  }

  .chart-list-container {
    width: 100%;
    margin-bottom: 20px;
    /* 在小屏幕上也使用动态高度计算 */
  }

  .chart-list-card {
    height: 100%; /* 保持占满容器高度 */
  }

  /* 在小屏幕上保持flex布局 */
}

@media (max-width: 576px) {
  /* 在小屏幕上保持flex布局 */
}
</style> 
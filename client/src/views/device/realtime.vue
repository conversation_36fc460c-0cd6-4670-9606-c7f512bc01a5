<template>
  <div class="device-realtime-container">
    <!-- 头部 -->
    <el-page-header @back="goBack">
      <template #content>
        <div class="header-content">
          <span class="device-name">{{ deviceDetail?.deviceName || '设备' }}</span>
          <span class="device-title text-primary">实时数据</span>
        </div>
      </template>
    </el-page-header>

    <!-- 使用v-loading指令展示加载状态 -->
    <div
      v-loading="loading" 
      element-loading-text="获取设备数据中..." 
      element-loading-background="rgba(0, 0, 0, 0.1)"
      style="width: 100%; min-height: 200px;"
    >
      <!-- 连接状态和控制 -->
      <el-card class="status-card">
        <div class="status-header">
          <div class="connection-status">
            <el-tag
              :type="mqttConnected ? 'success' : 'danger'"
              effect="dark"
              size="large"
              class="status-tag"
            >
              <i
                v-if="mqttConnected"
                class="el-icon-connection"
              />
              <i
                v-else
                class="el-icon-warning"
              />
              {{ mqttConnected ? 'MQTT已连接' : 'MQTT未连接' }}
            </el-tag>
            <div
              v-if="mqttConnected"
              class="topic-container"
            >
              <span class="topic-label">订阅主题:</span>
              <el-tag
                type="primary"
                effect="light"
              >
                {{ subscribedTopic }}
              </el-tag>
            </div>
          </div>
          <div class="control-btns">
            <el-button
              type="primary"
              :disabled="mqttConnected"
              :loading="connecting"
              size="large"
              class="action-button"
              @click="connectMqtt"
            >
              连接
            </el-button>
            <el-button
              type="danger"
              :disabled="!mqttConnected"
              size="large"
              class="action-button"
              @click="disconnectMqtt"
            >
              断开
            </el-button>
          </div>
        </div>
      </el-card>

      <!-- 新增的DrillMonitorPanel组件 -->
      <el-card
        class="realtime-view-card"
        style="margin-bottom: 16px; overflow: visible; background-color: #21272D; border: none; max-height: 700px;"
      >
        <div style="width: 100%; height: 700px; overflow: visible; position: relative;">
          <DrillMonitorPanel 
            :telemetry-data="latestTelemetryData" 
            :use-test-data="!latestTelemetryData && mqttConnected"
          />
        </div>
      </el-card>

      <!-- 简化钻进曲线图表组件 -->
      <el-card
        class="chart-card"
        style="margin-bottom: 16px;"
      >
        <MultiGridDrillChart :latest-data="latestTelemetryData" />
      </el-card>

      <!-- 实时数据展示 -->
      <el-card
        class="data-card"
        style="flex: 1; display: flex; flex-direction: column; min-height: 550px;"
      >
        <template #header>
          <div class="card-header">
            <span class="header-title">实时数据</span>
            <div class="header-actions">
              <!-- 分页信息和控件 -->
              <div
                v-if="formatMessages.length > pageSize"
                class="pagination-container"
                style="margin-right: 16px;"
              >
                <div
                  class="pagination-info"
                  style="margin-right: 12px;"
                >
                  共 {{ formatMessages.length }} 条，显示 {{ (currentPage - 1) * pageSize + 1 }}-{{ Math.min(currentPage * pageSize, formatMessages.length) }}
                </div>
                <el-pagination
                  v-model:current-page="currentPage"
                  style="margin-top: 0;"
                  :page-size="pageSize"
                  :total="formatMessages.length"
                  layout="prev, pager, next"
                  :small="true"
                  background
                />
              </div>
              <el-button
                type="primary"
                plain
                size="small"
                :disabled="messages.length === 0"
                class="clear-button"
                @click="clearMessages"
              >
                清空消息
              </el-button>
            </div>
          </div>
        </template>
        <div
          v-if="messages.length === 0"
          style="
              flex: 1;
              display: flex;
              justify-content: center;
              align-items: center;
              background-color: #f8faff;
              border-radius: 8px;
            "
        >
          <el-empty description="暂无数据" />
        </div>
        <div
          v-else
          style="margin: 0; padding: 0; flex: 1; position: relative; width: 100%; overflow: hidden; min-height: 557px;"
        >
          <div
            class="table-container"
            style="
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                overflow-x: auto;
                overflow-y: auto;
                max-width: 100%;
                height: 100%;
              "
          >
            <div style="width: 1200px; max-width: 1200px">
              <table style="width: 100%; table-layout: fixed; border-collapse: collapse">
                <colgroup>
                  <col
                    v-for="header in tableHeaders"
                    :key="'col-' + header"
                    :style="{
                      width:
                        header === 'Collection_At' || header === 'Tunnel_Name'
                          ? '200px'
                          : ['Heart', 'Mode', 'Strk_Pct', '18B03', 'Wrn_Cd', 'Dpth'].includes(header)
                            ? '100px'
                            : '120px'
                    }"
                  >
                </colgroup>
                <thead>
                  <tr>
                    <th
                      v-for="header in tableHeaders"
                      :key="header"
                      style="
                          position: sticky;
                          top: 0;
                          z-index: 10;
                          padding: 14px 12px;
                          text-align: center;
                          white-space: normal;
                          overflow: hidden;
                          background-color: #f0f6ff;
                          color: #2c3e50;
                          font-weight: 600;
                          border-bottom: 2px solid #c0d6ff;
                          border-right: 1px solid #e0eaff;
                          line-height: 1.4;
                          min-height: 60px;
                          vertical-align: middle;
                        "
                    >
                      <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; min-height: 40px;">
                        <div style="font-weight: 600; margin-bottom: 2px;">
                          {{ fieldMappings[header]?.name || header }}
                        </div>
                        <div
                          v-if="fieldMappings[header]?.description"
                          style="font-size: 11px; color: #666; font-weight: 400; line-height: 1.3; word-break: break-word; max-width: 100%;"
                        >
                          {{ fieldMappings[header].description }}
                        </div>
                      </div>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    v-for="(row, rowIndex) in paginatedMessages"
                    :key="row._messageId || `fallback-${currentPage}-${rowIndex}`"
                    class="table-row"
                    :class="{ 'table-row-even': rowIndex % 2 === 0, 'table-row-odd': rowIndex % 2 === 1 }"
                  >
                    <td
                      v-for="header in tableHeaders"
                      :key="`${row._messageId || rowIndex}-${header}`"
                      class="table-cell"
                    >
                      {{ formatCellValue(row[header]) }}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import mqtt from 'mqtt'
import { getDeviceDetail, getDeviceArithmetic } from '@/api/device'
import { executeChainedAlgorithmMethods } from '@/api/arithmetic'
import { getFieldMappings } from '@/api/data'
import DrillMonitorPanel, { TelemetryData } from '@/components/DrillMonitorPanel.vue'
import MultiGridDrillChart from '@/components/dashboard/MultiGridDrillChart.vue'
import { throttledMqtt } from '@/utils/debounce'

// 工具函数：下划线命名转驼峰命名 - 优化版本，使用缓存
const snakeToCamel = (obj: Record<string, any>): Record<string, any> => {
  // 生成缓存键，基于对象的键结构
  const cacheKey = Object.keys(obj).sort().join('|')
  // 检查缓存中是否有映射关系
  if (conversionCache.has(cacheKey)) {
    const keyMapping = conversionCache.get(cacheKey)!
    const result: Record<string, any> = {}
    Object.keys(obj).forEach(key => {
      const camelKey = keyMapping[key] || key
      result[camelKey] = obj[key]
    })
    return result
  }

  // 首次转换时创建映射关系并缓存
  const result: Record<string, any> = {}
  const keyMapping: Record<string, string> = {}

  Object.keys(obj).forEach(key => {
    let finalKey: string

    // 特殊情况处理
    if (key === 'HI_Ang') {
      finalKey = 'hlAng'
    } else if (key === 'Frcst_kN') {
      finalKey = 'frcstKn'
    } else {
      // 通用转换逻辑：将下划线格式转为驼峰格式
      const camelKey = key.replace(/_([a-z])/gi, (_, letter) => letter.toUpperCase())
      // 首字母小写
      finalKey = camelKey.charAt(0).toLowerCase() + camelKey.slice(1)
    }

    result[finalKey] = obj[key]
    keyMapping[key] = finalKey
  })

  // 缓存映射关系
  conversionCache.set(cacheKey, keyMapping)

  return result
}

// 映射存储原始字段名称与格式
const originalFieldFormats: Record<string, string> = {}

// 转换缓存，避免重复转换相同的对象结构 - 添加大小限制防止内存泄漏
const MAX_CACHE_SIZE = 50 // 限制缓存大小
const conversionCache = new Map<string, Record<string, string>>()
const reverseConversionCache = new Map<string, Record<string, string>>()

// 缓存清理函数
const cleanupCaches = () => {
  // 当缓存超过限制时，清理最旧的条目
  if (conversionCache.size > MAX_CACHE_SIZE) {
    const keysToDelete = Array.from(conversionCache.keys()).slice(0, conversionCache.size - MAX_CACHE_SIZE)
    keysToDelete.forEach(key => conversionCache.delete(key))
  }

  if (reverseConversionCache.size > MAX_CACHE_SIZE) {
    const keysToDelete = Array.from(reverseConversionCache.keys()).slice(0, reverseConversionCache.size - MAX_CACHE_SIZE)
    keysToDelete.forEach(key => reverseConversionCache.delete(key))
  }

  // 限制originalFieldFormats的大小
  const formatKeys = Object.keys(originalFieldFormats)
  if (formatKeys.length > MAX_CACHE_SIZE) {
    const keysToDelete = formatKeys.slice(0, formatKeys.length - MAX_CACHE_SIZE)
    keysToDelete.forEach(key => delete originalFieldFormats[key])
  }
}

// 预处理并存储原始字段格式
const storeOriginalFormat = (obj: Record<string, any>): void => {
  Object.keys(obj).forEach(key => {
    // 保存原始格式的字段名称
    const camelKey = key.replace(/_([a-z])/gi, (_, letter) => letter.toUpperCase())
    const finalKey = camelKey.charAt(0).toLowerCase() + camelKey.slice(1)
    
    originalFieldFormats[finalKey] = key
  })
}

// 工具函数：驼峰命名转回原始格式 - 优化版本，使用缓存
const camelToOriginalFormat = (obj: Record<string, any>): Record<string, any> => {
  // 生成缓存键，基于对象的键结构
  const cacheKey = Object.keys(obj).sort().join('|')

  // 检查反向缓存中是否有映射关系
  if (reverseConversionCache.has(cacheKey)) {
    const keyMapping = reverseConversionCache.get(cacheKey)!
    const result: Record<string, any> = {}
    Object.keys(obj).forEach(key => {
      const originalKey = keyMapping[key] || key
      result[originalKey] = obj[key]
    })
    return result
  }

  // 首次转换时创建映射关系并缓存
  const result: Record<string, any> = {}
  const keyMapping: Record<string, string> = {}

  // 算法添加的标记字段，保持原样不转换
  const algorithmFields = [
    'isStuckPoint', 'isUpMutation', 'isDownMutation', 'isMutationPoint',
    'mutationType', 'rockStrengthLevel', 'rockStrengthDesc',
    'rockGradeDesc', 'rockGradeLevel'
  ]

  Object.keys(obj).forEach(key => {
    // 算法添加的字段保持原样
    if (algorithmFields.includes(key)) {
      result[key] = obj[key]
      keyMapping[key] = key
      return
    }

    let originalKey: string

    // 特殊情况的反向转换
    if (key === 'hlAng') {
      originalKey = 'HI_Ang'
    } else if (key === 'frcstKn') {
      originalKey = 'Frcst_kN'
    } else if (originalFieldFormats[key]) {
      // 使用保存的原始格式
      originalKey = originalFieldFormats[key]
    } else {
      // 生成一个默认格式（首字母大写并加下划线）
      const snakeKey = key.replace(/([A-Z])/g, '_$1')
      // 首字母大写
      originalKey = snakeKey.charAt(0).toUpperCase() + snakeKey.slice(1).toUpperCase()
    }

    result[originalKey] = obj[key]
    keyMapping[key] = originalKey
  })

  // 缓存反向映射关系
  reverseConversionCache.set(cacheKey, keyMapping)

  return result
}

// MQTT配置项
const mqttConfig = {
  // 服务器连接配置
  server: {
    url: 'ws://8.154.34.3:8083/mqtt', // WebSocket连接地址
    options: {
      clean: true,
      username: 'test',
      password: 'test001',
      connectTimeout: 3000, // 连接超时时间(ms)
      reconnectPeriod: 3000, // 自动重连间隔(ms)
      keepalive: 60, // 心跳间隔(s)
      will: {  // 遗嘱消息，断开连接时服务器会发布此消息
        topic: 'client/status',
        payload: JSON.stringify({ status: 'offline' }),
        qos: 1 as 0 | 1 | 2, // 显式类型转换为MQTT QoS类型
        retain: false
      }
    }
  },
  // 主题配置
  topics: {
    data: (deviceSn: string) => `hub/device/${deviceSn}/data`, // 设备数据主题格式
    control: (deviceSn: string) => `hub/device/${deviceSn}/control` // 控制指令主题格式(预留)
  },
  // 消息配置 - 优化内存使用
  message: {
    maxCount: 100, // 减少最大消息存储数量，从100减少到20，减少80%内存占用
    retainLatest: true // 是否保留最新消息(true为保留最新的,false为保留最早的)
  }
}

const route = useRoute()
const router = useRouter()
const deviceId = route.params.id
const deviceDetail = ref<any>(null)
const deviceSn = ref('')
const mqttClient = ref<any>(null)
const mqttConnected = ref(false)
const connecting = ref(false)
const messages = ref<{ id: string; time: string; topic: string; payload: any }[]>([])
const tableHeaders = ref<string[]>([])
const subscribedTopic = ref('')
const deviceArithmeticId = ref<string | null>(null)
const loading = ref(true)
const fieldMappings = ref<Record<string, { name: string; description: string }>>({})
const fieldMappingsLoaded = ref(false)

// 获取字段映射 - 支持取消操作
const fetchFieldMappings = async () => {
  try {
    // 检查是否已被取消
    if (abortSignal.aborted) {
      return
    }

    const response = await getFieldMappings()

    // 再次检查是否已被取消
    if (abortSignal.aborted) {
      return
    }

    if (response.success && response.data) {
      // 将接口返回的驼峰式字段映射为MQTT使用的下划线格式
      const mappings: Record<string, { name: string; description: string }> = {}

      response.data.forEach((fieldObj: any) => {
        // 每个对象只有一个键值对，键是驼峰式字段名，值是描述
        const camelFieldName = Object.keys(fieldObj)[0]
        const description = fieldObj[camelFieldName]

        // 将驼峰式字段名转换为MQTT使用的下划线格式
        const mqttFieldName = camelToMqttFieldName(camelFieldName)

        // 以MQTT字段名（下划线格式）为key创建映射
        mappings[mqttFieldName] = {
          name: mqttFieldName, // 显示MQTT字段名
          description: description
        }
      })

      fieldMappings.value = mappings
      fieldMappingsLoaded.value = true
    }
  } catch (error) {
    if (abortSignal.aborted) {
      return
    }
    console.error('获取字段映射失败:', error)
  }
}

// 将驼峰式字段名转换为MQTT使用的下划线格式
const camelToMqttFieldName = (camelName: string): string => {
  // 处理特殊情况的映射
  const specialCases: Record<string, string> = {
    'collectionAt': 'Collection_At',
    'deviceSn': 'Device_Sn',
    'wrmCd': 'Wrn_Cd', // 告警码字段的特殊映射
    'hghWrk': 'Hgh_Wrk',
    'lwWrk': 'Lw_Wrk',
    'tunnelName': 'Tunnel_Name',
    'hlNum': 'HI_Num',
    'hlAng': 'HI_Ang',
    'strkPct': 'Strk_Pct',
    'rtnTq': 'Rtn_Tq',
    'frcstKn': 'Frcst_kN',
    'wtrPrsH': 'Wtr_Prs_H',
    'rtnSpd': 'Rtn_Spd',
    'advncSpd': 'Advnc_Spd',
    'hydrPrs': 'Hydr_Prs',
    'wtrPrsL': 'Wtr_Prs_L',
    'rtnPrs': 'Rtn_Prs',
    'frcstPrs': 'Frcst_Prs',
    'clctType': 'Clct_Type',
    'clctSts': 'Clct_Sts',
    'tunnelDpth': 'Tunnel_Dpth',
    'rc0': 'rc0',
    'rc1': 'rc1',
    'rc2': 'rc2',
    'rc3': 'rc3',
    'rc4': 'rc4',
    'rc5': 'rc5',
    'rc6': 'rc6',
    'led_08': 'LED_08',
    'ledAf': 'LED_AF',
    '18b03': '18B03'
  }

  if (specialCases[camelName]) {
    return specialCases[camelName]
  }

  // 一般情况：驼峰转下划线，每个单词首字母大写
  return camelName.replace(/([A-Z])/g, '_$1').replace(/^_/, '').split('_').map(part =>
    part.charAt(0).toUpperCase() + part.slice(1).toLowerCase()
  ).join('_')
}

// 获取设备详情 - 支持取消操作
const fetchDeviceDetail = async () => {
  try {
    // 检查是否已被取消
    if (abortSignal.aborted) {
      return
    }

    loading.value = true
    const response = await getDeviceDetail(deviceId as string)

    // 再次检查是否已被取消
    if (abortSignal.aborted) {
      return
    }

    deviceDetail.value = response.data
    deviceSn.value = response.data.serialNumber || ''

    // 获取设备关联的算法
    try {
      if (abortSignal.aborted) {
        return
      }

      const arithmeticResponse = await getDeviceArithmetic(deviceId as string) as {
        success: boolean;
        data: Array<{
          id: number;
          deviceId: number;
          arithmeticId: number;
          arithmeticType: number;
          arithmeticTypeName: string;
          arithmeticName: string;
          createdAt: string;
          modifiedAt: string;
        }>
      }

      if (abortSignal.aborted) {
        return
      }

      if (arithmeticResponse.success && arithmeticResponse.data) {
        if (arithmeticResponse.data.length > 0) {
          // 控件算法类型为1，注意比较条件改为双等号，避免类型问题
          if (arithmeticResponse.data[0].arithmeticType == 1) {
            deviceArithmeticId.value = arithmeticResponse.data[0].arithmeticId.toString()
          }
        } 
      } 
    } catch (arithmeticError) {
      if (abortSignal.aborted) {
        return
      }
      console.error('获取设备算法失败:', arithmeticError)
    }
  } catch (error) {
    if (abortSignal.aborted) {
      return
    }
    console.error('获取设备详情失败:', error)
    ElMessage.error('获取设备详情失败')
  } finally {
    if (!abortSignal.aborted) {
      loading.value = false
    }
  }
}

// 连接MQTT
const connectMqtt = () => {
  if (!deviceSn.value) {
    ElMessage.warning('设备SN号不存在，无法连接MQTT')
    return
  }

  // 先清理旧的连接和监听器，防止内存泄漏
  if (mqttClient.value) {
    mqttClient.value.removeAllListeners() // 移除所有事件监听器
    if (mqttClient.value.connected) {
      mqttClient.value.end(true) // 强制断开连接
    }
    mqttClient.value = null
  }

  connecting.value = true
  // 添加连接提示
  const connectingMsg = ElMessage({
    message: '正在连接到MQTT服务器...',
    type: 'info',
    duration: 0
  })

  // 获取MQTT连接选项
  const options = {
    ...mqttConfig.server.options,
    clientId: `hj_web_${Date.now()}` // 客户端ID使用动态生成的时间戳保证唯一性
  }

  // 尝试连接MQTT服务器
  try {
    mqttClient.value = mqtt.connect(mqttConfig.server.url, options)
  } catch (err) {
    connecting.value = false
    connectingMsg.close()
    ElMessage.error(`MQTT初始化连接错误: ${(err as Error).message || String(err)}`)
    return
  }

  // 使用命名函数，便于后续移除事件监听器
  const handleConnect = () => {
    mqttConnected.value = true
    connecting.value = false
    connectingMsg.close()
    ElMessage.success('MQTT连接成功')

    // 订阅设备数据主题
    const topic = mqttConfig.topics.data(deviceSn.value)
    mqttClient.value.subscribe(topic, (err: any) => {
      if (err) {
        ElMessage.error(`订阅主题失败: ${err.message}`)
        return
      }
      subscribedTopic.value = topic
    })
  }

  const handleMessage = (topic: string, payload: Buffer) => {
    handleMqttMessage(topic, payload)
  }

  const handleError = (error: any) => {
    connecting.value = false
    const errorMsg = error.message || '未知错误'

    // 提供更具体的错误信息和建议
    if (
      errorMsg.includes('WebSocket') ||
      errorMsg.includes('network') ||
      errorMsg.includes('timeout')
    ) {
      ElMessage.error({
        message: `MQTT连接失败: 无法通过WebSocket连接到服务器。可能的原因：
        1. 服务器未开启WebSocket支持（端口8083/8084）
        2. 服务器防火墙阻止了WebSocket连接
        3. 服务器地址或端口不正确
        请联系系统管理员确认MQTT服务器配置。`,
        duration: 5000
      })
    } else {
      ElMessage.error(`MQTT连接错误: ${errorMsg}`)
    }
  }

  const handleReconnect = () => {
    connecting.value = true
  }

  const handleClose = () => {
    mqttConnected.value = false
    connecting.value = false
    subscribedTopic.value = ''
  }

  // 注册事件监听器
  mqttClient.value.on('connect', handleConnect)
  mqttClient.value.on('message', handleMessage)
  mqttClient.value.on('error', handleError)
  mqttClient.value.on('reconnect', handleReconnect)
  mqttClient.value.on('close', handleClose)

}

// 断开MQTT连接
const disconnectMqtt = () => {
  if (mqttClient.value) {

    // 先移除所有事件监听器，防止内存泄漏
    mqttClient.value.removeAllListeners()

    // 如果连接状态，则断开连接
    if (mqttClient.value.connected) {
      mqttClient.value.end(true, {}, () => {
        ElMessage.info('MQTT连接已断开')
      })
    } 

    // 重置状态变量
    mqttConnected.value = false
    subscribedTopic.value = ''
    connecting.value = false

    // 清空客户端引用，确保垃圾回收
    mqttClient.value = null

  } 
}

// 返回上一页
const goBack = () => {
  router.go(-1)
}

// 格式化单元格值展示
const formatCellValue = (value: any): string => {
  if (value === null || value === undefined) {
    return '-'
  }

  if (typeof value === 'object') {
    return JSON.stringify(value)
  }

  if (typeof value === 'boolean') {
    return value ? '是' : '否'
  }

  return String(value)
}

// 根据收到的消息，动态生成表格表头 - 优化性能，避免重复更新
const updateTableHeaders = (payload: any) => {
  if (!payload || typeof payload !== 'object') return

  const keys = Object.keys(payload)
  let hasNewHeaders = false

  // 先处理Collection_At，确保它在数组的第一位
  if (keys.includes('Collection_At') && !tableHeaders.value.includes('Collection_At')) {
    tableHeaders.value.unshift('Collection_At')
    hasNewHeaders = true
  }

  // 添加其他新列（除了Collection_At）
  keys.forEach(key => {
    if (key !== 'Collection_At' && !tableHeaders.value.includes(key)) {
      tableHeaders.value.push(key)
      hasNewHeaders = true
    }
  })

  // 只在有新表头时输出日志
  if (hasNewHeaders) {
    console.log('表头已更新，当前列数:', tableHeaders.value.length)
  }
}

// 缓存上一次的formatMessages结果，减少对象创建
let lastFormatMessagesResult: any[] = []
let lastMessagesLength = 0
let lastMaxVisibleRows = 0
let lastFirstMessageId: string | null = null

// 格式化后的消息，用于表格展示 - 优化性能和DOM渲染，减少内存泄漏
const formatMessages = computed(() => {
  // 检查是否需要重新计算
  const currentLength = messages.value.length
  const currentMaxRows = maxVisibleRows.value

  // 获取第一条消息的ID作为缓存键的一部分
  const firstMessageId = messages.value.length > 0 ? messages.value[0].id : null

  // 修复缓存逻辑：不仅检查长度，还要检查内容是否变化
  if (currentLength === lastMessagesLength &&
      currentMaxRows === lastMaxVisibleRows &&
      lastFormatMessagesResult.length > 0 &&
      firstMessageId === lastFirstMessageId) {
    console.log('📋 使用formatMessages缓存')
    return lastFormatMessagesResult
  }

  console.log(`🔄 重新格式化消息：长度${currentLength}，第一条ID${firstMessageId}`)

  // 更新状态
  lastMessagesLength = currentLength
  lastMaxVisibleRows = currentMaxRows
  lastFirstMessageId = firstMessageId

  // 限制渲染数量，避免DOM节点过多
  const limitedMessages = messages.value.slice(0, maxVisibleRows.value)

  // 创建新的结果数组，减少对象创建
  const result = limitedMessages.map((msg: any) => {
    // 使用最小化的对象，只包含必要属性
    const formattedMsg: any = { _messageId: msg.id }

    // 直接返回payload，如果是对象则返回对象，否则包装为对象
    if (typeof msg.payload === 'object' && msg.payload !== null) {
      // 只复制必要的属性，避免引用整个对象
      Object.keys(msg.payload).forEach(key => {
        formattedMsg[key] = msg.payload[key]
      })
    } else {
      formattedMsg.value = msg.payload
    }

    return formattedMsg
  })

  // 更新缓存
  lastFormatMessagesResult = result

  return result
})

// 分页显示的消息 - 进一步减少DOM节点
const paginatedMessages = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return formatMessages.value.slice(start, end)
})

// 总页数
const totalPages = computed(() => {
  return Math.ceil(formatMessages.value.length / pageSize.value)
})

// 清空消息 - 增强版，彻底清理内存
const clearMessages = () => {

  // 清空消息数组
  messages.value.length = 0
  tableHeaders.value.length = 0

  // 清空待处理消息队列
  pendingMessages.length = 0

  // 清理formatMessages缓存
  lastFormatMessagesResult = []
  lastMessagesLength = 0
  lastMaxVisibleRows = 0
  lastFirstMessageId = null

  // 重置分页
  currentPage.value = 1

  // 执行轻量级内存清理
  lightMemoryCleanup()

}

// 消息ID生成器，确保每条消息有唯一标识
let messageIdCounter = 0
const generateMessageId = () => {
  return `msg_${Date.now()}_${++messageIdCounter}`
}

// 移除批量处理函数，改为实时处理

// 实时消息添加函数 - 移除节流，实现实时更新
const addMessage = (topic: string, payloadData: any) => {
  const now = new Date()
  const timeStr = now.toLocaleTimeString()

  // 只在表头为空时更新表头，避免重复操作
  if (tableHeaders.value.length === 0 && typeof payloadData === 'object') {
    updateTableHeaders(payloadData)
  }

  // 直接添加到消息数组开头，实现实时更新
  const messageId = generateMessageId()
  messages.value.unshift({
    id: messageId,
    time: timeStr,
    topic,
    payload: payloadData
  })

  console.log(`📨 新消息添加：ID=${messageId}，数组长度=${messages.value.length}`)

  // 限制数组长度，保持Vue响应式（使用splice而不是直接设置length）
  if (messages.value.length > mqttConfig.message.maxCount) {
    messages.value.splice(mqttConfig.message.maxCount)
    console.log(`🔄 数组长度限制：保留前${mqttConfig.message.maxCount}条消息`)
  }

  // 轻量级缓存清理（每20条消息清理一次）
  if (messages.value.length % 20 === 0) {
    cleanupCaches()
  }
}

// 使用非null初始值
const latestTelemetryData = ref<TelemetryData>({});

// 移除图表历史数据维护，改为在组件内部维护

// DOM渲染优化 - 分页相关
const currentPage = ref(1)
const pageSize = ref(20) // 每页显示20条数据，减少DOM节点
const maxVisibleRows = ref(100) // 最大可见行数，防止DOM过多

// 保留定时器变量用于清理
let domUpdateTimer: number | null = null

// 待处理的消息队列（保留用于清理）
const pendingMessages: Array<{ topic: string; payloadData: any }> = []

// 防止并发执行的标志
let isProcessingAlgorithm = false

// 内存清理定时器
let memoryCleanupTimer: number | null = null
const MEMORY_CLEANUP_INTERVAL = 30000 // 30秒清理一次

// 简化的内存监控函数
const checkMemoryUsage = () => {
  if (typeof performance !== 'undefined' && (performance as any).memory) {
    const memory = (performance as any).memory
    const usedMB = Math.round(memory.usedJSHeapSize / 1024 / 1024)

    // 内存使用超过200MB时发出警告
    if (usedMB > 200) {
      console.warn(`⚠️ 内存使用过高: ${usedMB}MB，建议清理`)
      return true
    }

    console.log(`📊 内存使用情况: ${usedMB}MB`)
  }
  return false
}

// 轻量级内存清理函数 - 移除对消息数组的影响
const lightMemoryCleanup = () => {
  // 清理缓存
  cleanupCaches()

  // 清理待处理消息队列（如果还在使用）
  if (pendingMessages.length > 50) {
    pendingMessages.splice(0, pendingMessages.length - 50)
  }

  // 重置formatMessages缓存
  lastFormatMessagesResult = []
  lastMessagesLength = 0
  lastMaxVisibleRows = 0
  lastFirstMessageId = null

  // 清理转换缓存（但不影响消息数组）
  if (conversionCache.size > 100) {
    conversionCache.clear()
  }
  if (reverseConversionCache.size > 100) {
    reverseConversionCache.clear()
  }

  // 强制垃圾回收（如果可用）
  if (typeof window !== 'undefined' && (window as any).gc) {
    try {
      (window as any).gc()
    } catch (e) {
      // 忽略错误
    }
  }
}

// 启动定期内存清理
const startMemoryCleanup = () => {
  if (memoryCleanupTimer) {
    clearInterval(memoryCleanupTimer)
  }

  memoryCleanupTimer = window.setInterval(() => {
    if (!isDestroyed) {
      lightMemoryCleanup()
    }
  }, MEMORY_CLEANUP_INTERVAL)

}

// 异步操作控制器，用于取消pending的异步操作
const abortController = new AbortController()
const abortSignal = abortController.signal

// 组件销毁标志，防止销毁后的异步操作继续执行
let isDestroyed = false

// 优化：高效获取历史数据的函数，避免重复转换
const getHistoricalDataPoints = (count: number = 4): any[] => {
  const historicalData: any[] = []
  let collected = 0

  // 限制搜索范围，避免在大数据量时遍历过多数据
  const searchLimit = Math.min(messages.value.length, count * 2) // 最多搜索count*2条

  for (let i = 0; i < searchLimit && collected < count; i++) {
    const msg = messages.value[i]
    if (msg && typeof msg.payload === 'object') {
      // 利用缓存机制转换数据
      historicalData.push(snakeToCamel(msg.payload))
      collected++
    }
  }

  return historicalData
}

// 直接数据处理函数 - 移除节流，实现实时处理
const processDataDirectly = async (topic: string, payloadData: any) => {
  // 检查组件是否已销毁
  if (isDestroyed || abortSignal.aborted) {
    return
  }

  // 如果有算法配置且未在处理中，进行算法处理
  if (deviceArithmeticId.value && !isProcessingAlgorithm) {
    isProcessingAlgorithm = true
    try {
      // 再次检查组件状态
      if (isDestroyed || abortSignal.aborted) {
        return
      }
      // 优化：只转换当前数据，避免重复转换历史数据
      const currentCamelData = snakeToCamel(payloadData);
      // 优化：使用高效的历史数据获取函数
      const historicalData = getHistoricalDataPoints(4)

      // 构建包含数据点的数组（历史数据 + 当前1个）
      const dataPoints = [...historicalData, currentCamelData];

      // 调用算法处理
      const result = await executeChainedAlgorithmMethods(
        deviceArithmeticId.value,
        ['dataPrecisionProcessing', 'generateDrillingRealTimeData'],
        dataPoints // 传入数据点数组
      );

      if (result?.data) {
        // 检查组件是否已销毁
        if (isDestroyed || abortSignal.aborted) {
          return
        }

        // 优化：只处理最后一个结果，减少不必要的转换
        const processedData = Array.isArray(result.data)
          ? result.data[result.data.length - 1] // 只取最后一个结果
          : result.data;

        // 优化：只在有数据时进行转换，避免对象引用累积
        if (processedData) {
          const convertedData = camelToOriginalFormat(processedData);
          // 使用浅拷贝避免引用累积，并限制属性数量
          const mergedData = { ...payloadData }
          Object.keys(convertedData).forEach(key => {
            mergedData[key] = convertedData[key]
          })
          payloadData = mergedData // 重新赋值而不是修改原对象
        }

        // 更新为处理后的遥测数据，使用新对象避免引用累积
        if (!isDestroyed && !abortSignal.aborted) {
          latestTelemetryData.value = { ...payloadData };
        }
      }
    } catch (algorithmError) {
      console.error('算法处理数据失败:', algorithmError);
    } finally {
      isProcessingAlgorithm = false
    }
  } else {
    // 没有算法处理的情况，直接更新遥测数据
    latestTelemetryData.value = payloadData;
  }

  // 将数据（原始或处理后的）直接添加到消息列表
  addMessage(topic, payloadData);
};

// 处理MQTT消息方法 - 优化内存使用
const handleMqttMessage = async (topic: string, payload: Buffer) => {
  try {
    const payloadStr = payload.toString();

    // 检查组件是否已销毁
    if (isDestroyed || abortSignal.aborted) {
      return
    }

    let payloadData: any
    try {
      payloadData = JSON.parse(payloadStr);
    } catch (parseError) {
      console.error('JSON解析错误:', parseError);
      // 对于无法解析为JSON的消息，直接返回，避免创建无效对象
      return
    }

    // 存储原始字段格式（不节流），但限制存储数量
    if (Object.keys(originalFieldFormats).length < MAX_CACHE_SIZE) {
      storeOriginalFormat(payloadData);
    }

    // 直接处理数据，实现实时更新
    processDataDirectly(topic, payloadData);

  } catch (error) {
    console.error('消息处理错误:', error);
    // 减少错误情况下的对象创建
  }
};

onMounted(async () => {
  // 启动定期内存清理
  startMemoryCleanup()

  // 并行获取设备详情和字段映射
  await Promise.all([
    fetchDeviceDetail(),
    fetchFieldMappings()
  ])

  // 获取设备详情后自动连接MQTT
  if (deviceSn.value) {
    connectMqtt()
  }
})

onBeforeUnmount(() => {

  try {
    // 第1步：设置销毁标志，阻止新的异步操作
    try {
      isDestroyed = true
    } catch (error) {
      console.error('❌ 设置销毁标志失败:', error)
    }

    // 第2步：取消所有pending的异步操作
    try {
      abortController.abort()
    } catch (error) {
      console.error('❌ 取消异步操作失败:', error)
    }

    // 第3步：停止算法处理
    try {
      isProcessingAlgorithm = false
    } catch (error) {
      console.error('❌ 停止算法处理失败:', error)
    }

    // 第4步：清理定时器
    try {
      // 清理DOM更新定时器
      if (domUpdateTimer) {
        clearTimeout(domUpdateTimer)
        domUpdateTimer = null
      }

      // 清理内存清理定时器
      if (memoryCleanupTimer) {
        clearInterval(memoryCleanupTimer)
        memoryCleanupTimer = null
      }

      // 清理待处理消息队列
      pendingMessages.length = 0
    } catch (error) {
      console.error('❌ 清理定时器失败:', error)
    }

    // 第5步：断开MQTT连接并清理所有相关资源
    try {
      disconnectMqtt()
    } catch (error) {
      console.error('❌ 断开MQTT连接失败:', error)
    }

    // 第6步：深度清理缓存和全局变量
    try {
      // 清理转换缓存
      conversionCache.clear()
      reverseConversionCache.clear()

      // 清理formatMessages缓存
      lastFormatMessagesResult = []
      lastMessagesLength = 0
      lastMaxVisibleRows = 0
      lastFirstMessageId = null

      // 清理待处理消息队列
      pendingMessages.length = 0

    } catch (error) {
      console.error('❌ 清理转换缓存失败:', error)
    }

    try {
      // 清理全局变量
      Object.keys(originalFieldFormats).forEach(key => {
        delete originalFieldFormats[key]
      })

      // 重置计数器
      messageIdCounter = 0

    } catch (error) {
      console.error('❌ 清理全局变量失败:', error)
    }

    // 第7步：清空响应式数据数组
    try {
      messages.value.length = 0
      tableHeaders.value.length = 0
    } catch (error) {
      console.error('❌ 清理响应式数据失败:', error)
    }

  } catch (globalError) {
    console.error('💥 组件清理过程中发生严重错误:', globalError)
    // 即使发生错误，也要尝试基本的清理
    try {
      isDestroyed = true
      isProcessingAlgorithm = false
      if (mqttClient.value) {
        mqttClient.value.end(true)
        mqttClient.value = null
      }
    } catch (fallbackError) {
      console.error('💥 回退清理也失败:', fallbackError)
    }
  }
})


</script>

<style scoped>
.device-realtime-container {
  padding: 16px 24px;
  background-color: #f5f7fa;
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
  min-height: calc(100vh - 100px);
  height: auto;
  display: flex;
  flex-direction: column;
}

.page-header {
  background-color: white;
  padding: 16px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  margin-bottom: 16px;
  flex-shrink: 0;
}

.header-content {
  display: flex;
  align-items: center;
}

.device-name {
  font-size: 20px;
  font-weight: bold;
  margin-right: 16px;
  color: #303133;
}

.device-title {
  font-size: 14px;
  background-color: rgba(0, 113, 227, 0.1);
  padding: 4px 12px;
  border-radius: 16px;
}

/* 卡片统一样式 */
.status-card,
.data-card {
  margin-bottom: 16px;
  border-radius: 12px;
  width: 100%;
  box-sizing: border-box;
  min-width: 0;
  transition: all 0.3s ease;
  box-shadow: 0 4px 18px rgba(0, 0, 0, 0.08);
  border: none;
  overflow: hidden;
}

/* 悬停效果已禁用
.status-card:hover,
.data-card:hover {
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}
*/

/* 状态卡片 */
.status-card {
  margin-top: 12px;
  margin-bottom: 12px;
  background: #fff;
  position: relative;
  flex-shrink: 0;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
}

.connection-status {
  display: flex;
  align-items: center;
}

.topic-container {
  display: flex;
  align-items: center;
  margin-left: 20px;
}

.topic-label {
  margin-right: 8px;
  font-size: 14px;
  color: #606266;
}

.topic-tag {
  font-family: monospace;
  font-size: 13px;
  padding: 0 12px;
  height: 32px;
  line-height: 30px;
  border-radius: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 300px;
}

.status-tag {
  font-size: 13px;
  padding: 6px 12px;
  height: auto;
  border-radius: 6px;
}

.action-button {
  min-width: 90px;
  margin-left: 12px;
  transition: all 0.3s ease;
  font-size: 14px;
  padding: 8px 15px;
  height: auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.pagination-info {
  white-space: nowrap;
  font-weight: 500;
  color: #606266;
  font-size: 13px;
  background-color: #f5f7fa;
  padding: 4px 8px;
  border-radius: 3px;
}

.pagination-container {
  display: flex;
  align-items: center;
  background-color: #fafbfc;
  padding: 6px 12px;
  border-radius: 6px;
}

.clear-button {
  color: #ffffff !important;
  font-weight: 600 !important;
  background-color: #409eff !important;
  border-color: #409eff !important;
  padding: 8px 16px;
}

.clear-button:hover {
  color: #ffffff !important;
  background-color: #66b1ff !important;
  border-color: #66b1ff !important;
}

.clear-button[disabled] {
  color: #c0c4cc !important;
  background-color: #ecf5ff !important;
  border-color: #d9ecff !important;
}

/* DOM渲染优化 - 表格样式 */
.table-row {
  transition: background-color 0.2s ease;
  height: 46px;
  /* 使用transform代替background-color变化，减少重绘 */
  will-change: transform;
}

.table-row-even {
  background-color: #ffffff;
}

.table-row-odd {
  background-color: #f8faff;
}

.table-row:hover {
  /* 使用transform提升性能 */
  transform: translateZ(0);
  background-color: #ecf5ff;
}

.table-cell {
  padding: 12px 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  border-bottom: 1px solid #ebeef5;
  border-right: 1px solid #ebeef5;
  color: #606266;
  text-align: center;
  height: 46px;
  /* 启用硬件加速 */
  transform: translateZ(0);
  /* 避免重排 */
  box-sizing: border-box;
}

/* 表格容器优化 */
.table-container {
  /* 启用GPU加速 */
  transform: translateZ(0);
  /* 优化滚动性能 */
  -webkit-overflow-scrolling: touch;
  /* 减少重绘区域 */
  contain: layout style paint;
}

/* 无数据状态 */
.no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f8faff;
  height: 400px;
  border-radius: 8px;
}

/* 表格容器 */
.table-container {
  overflow: hidden;
  margin: 0;
  padding: 0;
}

/* 简化表格包装器 */
.simple-table-wrapper {
  width: 100%;
  height: 400px;
  overflow: auto;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

/* 简化表格样式 */
.simple-table {
  width: 1200px;
  table-layout: fixed;
  border-collapse: collapse;
  margin: 0;
  padding: 0;
}

/* 表头样式 */
.table-header {
  position: sticky;
  top: 0;
  z-index: 10;
  padding: 14px 12px;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  background-color: #f0f6ff;
  color: #2c3e50;
  font-weight: 600;
  border-bottom: 2px solid #c0d6ff;
  border-right: 1px solid #e0eaff;
  width: 140px;
}

/* 日期列宽度 */
.date-column {
  width: 180px;
}

/* 窄列宽度 */
.narrow-column {
  width: 100px;
}

/* 表格行样式 */
.simple-table tr {
  transition: background-color 0.2s ease;
}

/* 奇偶行样式 */
.simple-table tr:hover {
  background-color: #ecf5ff !important;
}

.even-row {
  background-color: #ffffff;
}

.simple-table tr:not(.even-row) {
  background-color: #f8faff;
}

/* 单元格样式 */
.simple-table td {
  padding: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  border-bottom: 1px solid #ebeef5;
  border-right: 1px solid #ebeef5;
  color: #606266;
  transition: background-color 0.2s ease;
}

/* 数字单元格对齐右侧 */
.numeric-cell {
  text-align: right;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .status-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .control-btns {
    margin-top: 16px;
    width: 100%;
    display: flex;
    justify-content: flex-end;
  }

  .topic-container {
    margin-left: 0;
    margin-top: 10px;
    width: 100%;
  }

  .topic-tag {
    max-width: 200px;
  }
}

/* 数据卡片 - 调整样式 */
.data-card {
  margin-bottom: 16px;
  border-radius: 12px;
  width: 100%;
  box-sizing: border-box;
  min-width: 0;
  transition: all 0.3s ease;
  box-shadow: 0 4px 18px rgba(0, 0, 0, 0.08);
  border: none;
  overflow: hidden;
}

.el-card {
  max-width: 100%;
  overflow: hidden !important;
}

:deep(.el-card__body) {
  padding: 15px;
  position: relative;
  overflow: hidden !important;
  display: flex;
  flex-direction: column;
  flex: 1;
  max-width: 100%;
}

.data-card :deep(.el-card__body) {
  padding: 20px;
  display: flex;
  flex-direction: column;
  flex: 1;
  max-width: 100%;
  overflow: hidden !important;
}

.realtime-view-card {
  margin-bottom: 16px;
}

.chart-card {
  background: #fff; /* 白色背景，匹配原组件 */
  border: none;
  border-radius: 12px;
  transition: all 0.3s ease;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08); /* 匹配原组件阴影 */
  height: 800px; /* 为多网格图表提供足够的高度 */
  overflow: hidden;
  position: relative;
}

.chart-card:hover {
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

/* 添加顶部装饰条，匹配原组件 */
.chart-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #409eff, #67c23a);
  z-index: 1;
}

.chart-card :deep(.el-card__body) {
  padding: 20px;
  height: calc(100% - 0px); /* 没有header，所以减去0px */
}
</style>

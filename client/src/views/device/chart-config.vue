<template>
  <div class="chart-config-container">
    <el-page-header @back="goBack">
      <template #content>
        <div class="page-header-info">
          <span class="device-name">{{ deviceName }}</span>
          <span class="page-title text-primary">图表配置</span>
        </div>
      </template>
    </el-page-header>

    <div
      v-loading="loading"
      class="config-content"
    >
      <!-- 图表配置说明 -->
      <div>
        <el-alert
          type="info"
          :closable="false"
          show-icon
        >
          <p>在此页面配置设备详情页的图表展示。打开开关表示显示图表，关闭表示隐藏图表。配置将会立即生效并保存。</p>
        </el-alert>
      </div>

      <div
        v-if="availableCharts.length === 0"
        class="no-data"
      >
        <el-empty description="暂无可用图表组件" />
      </div>

      <!-- 卡片式布局 -->
      <div
        v-else
        class="chart-cards"
      >
        <div 
          v-for="chart in availableCharts" 
          :key="chart.id" 
          class="chart-card"
          :class="{ 'disabled': !chart.enabled }"
        >
          <div class="card-content">
            <div class="card-header">
              <div class="chart-name">
                {{ chart.name }}
              </div>
              <div class="chart-type">
                <el-tag
                  :type="getChartTypeTag(chart.type)"
                  size="small"
                >
                  {{ chart.type }}
                </el-tag>
              </div>
            </div>
            <div class="card-body">
              <div class="thumbnail-container">
                <img 
                  v-if="getThumbnail(chart)" 
                  :src="getThumbnail(chart)" 
                  alt="图表缩略图" 
                  class="chart-image" 
                >
                <div
                  v-else
                  class="chart-icon"
                >
                  <el-icon v-if="chart.type === 'pie'">
                    <pie-chart />
                  </el-icon>
                  <el-icon v-else-if="chart.type === 'bar'">
                    <histogram />
                  </el-icon>
                  <el-icon v-else-if="chart.type === 'line'">
                    <trend-charts />
                  </el-icon>
                  <el-icon v-else-if="chart.type === 'table'">
                    <grid />
                  </el-icon>
                  <el-icon v-else>
                    <data-analysis />
                  </el-icon>
                </div>
              </div>
              <div class="chart-meta">
                <!-- <div class="meta-item">
                  <span class="meta-label">ID:</span>
                  <span class="meta-value">{{ chart.id }}</span>
                </div>
                <div class="meta-item">
                  <span class="meta-label">组件:</span>
                  <span class="meta-value">{{ chart.component }}</span>
                </div> -->
                <div class="meta-item description">
                  <span class="meta-value">{{ chart.description }}</span>
                </div>
              </div>
            </div>
            <div class="card-footer">
              <el-switch
                v-model="chart.enabled"
                active-color="#13ce66"
                inactive-color="#ff4949"
                :loading="chart.loading"
                @change="(val) => handleChartStatusChange(chart, val)"
              />
              <span class="status-text">{{ chart.enabled ? '已启用' : '已禁用' }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
/**
 * 设备图表配置页面
 * 配置设备详情页面显示的图表组件
 */

import { ref, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  PieChart, Histogram, TrendCharts, Grid, DataAnalysis
} from '@element-plus/icons-vue'
import {
  createDeviceChartConfig,
  deleteDeviceChartConfig,
  getDeviceChartConfigStatus
} from '@/api/chartConfig'
import { getDeviceDetail } from '@/api'
import { debouncedApiCall } from '@/utils/debounce'

// 图表配置项类型
interface ChartConfig {
  id: string
  name: string
  description: string
  type: string
  enabled: boolean
  icon?: string
}

// API响应类型
interface ChartConfigResponse {
  success: boolean
  data: ChartConfig[]
}

const route = useRoute()
const router = useRouter()
const deviceId = route.params.id as string
const deviceName = ref<string>('')
const loading = ref<boolean>(false)

const availableCharts = ref<ChartConfig[]>([])

// 获取设备详情
const fetchDeviceDetail = async () => {
  try {
    const response = await getDeviceDetail(deviceId)
    if (response.success) {
      deviceName.value = response.data.deviceName || '未命名设备'
    } else {
      ElMessage.error('获取设备详情失败')
    }
  } catch (error) {
    console.error('获取设备详情失败:', error)
    ElMessage.error('获取设备详情失败: ' + (error.message || '未知错误'))
  }
}

// 获取图表配置
const fetchChartConfig = async () => {
  try {
    loading.value = true
    
    // 获取设备图表配置状态（已经包含了所有图表组件和它们的启用状态）
    const response = await getDeviceChartConfigStatus(deviceId)
    if (!response.success) {
      throw new Error(response.message || '获取图表配置失败')
    }
    
    // 直接使用返回的数据，不需要额外处理
    availableCharts.value = response.data.list.map(chart => ({
      ...chart,
      loading: false // 添加loading状态用于UI控制
    }))
    
  } catch (error) {
    console.error('获取图表配置失败:', error)
    ElMessage.error('获取图表配置失败: ' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

// 创建防抖的图表状态变更函数
const debouncedChartStatusChange = debouncedApiCall(async (chart, enabled) => {
  try {
    chart.loading = true

    if (enabled) {
      // 启用图表，创建配置
      const response = await createDeviceChartConfig({
        deviceId: parseInt(deviceId),
        chartComponentId: chart.id
      })

      if (response.success) {
        chart.configId = response.data.id
        ElMessage.success(`已启用图表: ${chart.name}`)
      } else {
        throw new Error(response.message || '启用图表失败')
      }
    } else {
      // 禁用图表，删除配置
      if (chart.configId) {
        const response = await deleteDeviceChartConfig(chart.configId)

        if (response.success) {
          chart.configId = null
          ElMessage.success(`已禁用图表: ${chart.name}`)
        } else {
          throw new Error(response.message || '禁用图表失败')
        }
      } else {
        ElMessage.warning(`无法找到图表配置ID，请刷新页面再试`)
        chart.enabled = true // 恢复状态
      }
    }
  } catch (error) {
    console.error('更改图表状态失败:', error)
    ElMessage.error('更改图表状态失败: ' + (error.message || '未知错误'))
    chart.enabled = !enabled // 恢复之前的状态
    await fetchChartConfig() // 重新获取配置，确保UI状态与服务器一致
  } finally {
    chart.loading = false
  }
})

// 处理图表状态变更
const handleChartStatusChange = (chart, enabled) => {
  debouncedChartStatusChange(chart, enabled)
}

// 获取图表类型对应的tag类型
const getChartTypeTag = (type) => {
  const typeMap = {
    'pie': 'success',
    'bar': 'warning',
    'line': 'primary',
    'table': 'info',
    '3d': 'danger',
    'tree': 'success',
    'matrix': 'warning'
  }
  return typeMap[type] || ''
}

// 获取图表缩略图
const getThumbnail = (chart) => {
  // 优先使用直接的thumbnail属性
  if (chart.thumbnail) {
    return chart.thumbnail
  }

  // 如果没有，则尝试从config字段解析
  if (chart.config) {
    try {
      const configObj = JSON.parse(chart.config)
      return configObj.thumbnail || null
    } catch (e) {
      console.error('解析图表配置失败:', e)
      return null
    }
  }

  return null
}

// 返回上一页
const goBack = () => {
  router.go(-1)
}

onMounted(() => {
  fetchDeviceDetail()
  fetchChartConfig()
})

// 组件卸载时清理防抖函数
onUnmounted(() => {
  if (debouncedChartStatusChange && typeof debouncedChartStatusChange.cancel === 'function') {
    debouncedChartStatusChange.cancel()
  }
})
</script>

<style scoped>
.chart-config-container {
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
}

.page-header-info {
  display: flex;
  align-items: center;
}

.device-name {
  font-size: 18px;
  font-weight: bold;
  margin-right: 16px;
}

.page-title {
  font-size: 14px;
  background-color: rgba(0, 113, 227, 0.1);
  padding: 4px 12px;
  border-radius: 16px;
}

.config-content {
  margin-top: 20px;
}

/* 图表列表标题和操作 */
.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 0 0 16px;
  border-bottom: 1px solid #ebeef5;
}

.chart-title {
  font-size: 16px;
  font-weight: bold;
}

.batch-actions {
  display: flex;
  gap: 10px;
}

.no-data {
  padding: 40px 0;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

/* 卡片式布局样式 - 参考digitalcore.vue */
.chart-cards {
  display: flex;
  flex-wrap: wrap;
  gap: 28px;
  padding: 20px 20px 40px 20px;
}

.chart-card {
  background-color: rgba(255, 255, 255, 0.98);
  border-radius: 16px;
  box-shadow: 
    0 4px 24px rgba(0, 0, 0, 0.04),
    0 1px 6px rgba(0, 0, 0, 0.03);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  overflow: hidden;
  border: none;
  width: calc(25% - 21px);
  backdrop-filter: blur(10px);
  position: relative;
}

.chart-card:hover {
  transform: translateY(-3px) scale(1.01);
  box-shadow: 
    0 10px 30px rgba(0, 0, 0, 0.07),
    0 3px 10px rgba(0, 0, 0, 0.05);
}

.chart-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #007AFF, #5AC8FA);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.chart-card:hover::before {
  opacity: 1;
}

.chart-card.disabled {
  opacity: 0.6;
  filter: grayscale(1);
}

.chart-card.disabled::before {
  background: linear-gradient(90deg, #909399, #C0C4CC);
}

.card-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.card-header {
  padding: 24px;
  position: relative;
  border-bottom: 1px solid rgba(0, 0, 0, 0.02);
}

.chart-name {
  font-weight: 500;
  font-size: 16px;
  color: #262626;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  letter-spacing: -0.01em;
}

.chart-type {
  margin-top: 8px;
}

.card-body {
  padding: 20px 24px;
  flex-grow: 1;
  background-color: transparent;
}

.thumbnail-container {
  height: 120px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16px;
}

.chart-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.chart-icon {
  width: 80px;
  height: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #e6f1fa;
  border-radius: 50%;
}

.chart-icon .el-icon {
  font-size: 40px;
  color: #409eff;
}

.chart-meta {
  display: flex;
  flex-direction: column;
  gap: 14px;
}

.meta-item {
  display: flex;
  align-items: flex-start;
}

.meta-label {
  font-size: 13px;
  color: #787878;
  margin-right: 10px;
  min-width: 40px;
}

.meta-value {
  font-size: 13px;
  color: #383838;
  font-weight: 450;
}

.meta-item.description {
  display: flex;
  flex-direction: column;
}

.meta-item.description .meta-label {
  margin-bottom: 4px;
}

.meta-item.description .meta-value {
  margin-left: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.card-footer {
  padding: 20px 24px;
  display: flex;
  align-items: center;
  border-top: 1px solid rgba(0, 0, 0, 0.02);
}

.status-text {
  margin-left: 10px;
  font-size: 13px;
  color: #606266;
}
</style> 
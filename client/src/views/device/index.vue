<template>
  <div class="device-container">
    <!-- 搜索和列表整合在一个卡片中 -->
    <el-card class="device-list-card">
      <!-- 筛选区域 -->
      <div class="filter-section">
        <el-row :gutter="24">
          <el-col :span="4">
            <el-select
              v-model="filterForm.type"
              placeholder="设备类型"
            >
              <el-option
                label="全部"
                value="全部"
              />
              <el-option
                label="锚杆钻"
                value="AD"
              />
              <el-option
                label="水锤"
                value="WPD"
              />
              <el-option
                label="超前钻机"
                value="PDF"
              />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-input
              v-model.trim="filterForm.name"
              placeholder="设备名称"
              maxlength="32"
              clearable
              @keyup.enter="debouncedSearchDevices"
            />
          </el-col>
          <el-col :span="4">
            <el-input
              v-model.trim="filterForm.serialNumber"
              placeholder="设备序列号"
              maxlength="32"
              clearable
              @keyup.enter="debouncedSearchDevices"
            />
          </el-col>
          <el-col
            :span="12"
            class="filter-buttons"
          >
            <el-button
              type="primary"
              @click="debouncedSearchDevices"
            >
              搜索
            </el-button>
            <el-button @click="resetForm">
              清空
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 表格容器 - 固定高度，内部滚动 -->
      <div
        ref="tableWrapperRef"
        class="device-table-wrapper"
        :style="{ height: tableHeight + 'px' }"
      >
        <div class="device-table-container">
          <el-table
            v-loading="loading"
            :data="deviceList"
            style="width: 100%"
            stripe
            height="100%"
          >
            <!-- 新增的序号列 -->
            <el-table-column
              type="index"
              label="序号"
              width="60"
              align="center"
            />
            <el-table-column
              prop="type"
              label="设备类型"
              align="center"
            >
              <template #default="scope">
                <el-tag
                  :type="getDeviceTagType(scope.row.type)"
                  effect="light"
                >
                  {{ scope.row.typeName }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column
              prop="deviceName"
              label="设备名称"
              show-overflow-tooltip
              align="center"
            />
            <el-table-column
              prop="serialNumber"
              label="设备序列号"
              show-overflow-tooltip
              align="center"
            />
            <el-table-column
              prop="lastDeviceTime"
              label="最后设备更新时间"
              align="center"
            />
            <el-table-column
              prop="lastFileTime"
              label="最后文件更新时间"
              align="center"
            />
            <el-table-column
              label="操作"
              align="center"
            >
              <template #default="scope">
                <el-button
                  type="primary"
                  link
                  @click="viewDeviceDetail(scope.row)"
                >
                  查看详情
                </el-button>
                <el-button
                  type="primary"
                  link
                  @click="viewRealTimeData(scope.row)"
                >
                  实时数据
                </el-button>
                <el-button
                  type="primary"
                  link
                  @click="viewDigitalCore(scope.row)"
                >
                  数字岩芯
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 分页部分 - 固定在底部 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next"
          :total="total"
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 40, 50, 60, 80, 100]"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
/**
 * 设备列表页面
 * 显示设备列表，支持筛选、分页和设备操作
 */

import { ref, onMounted, onUnmounted, onBeforeUnmount, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getDeviceList } from '@/api'
import { getDeviceTagType, type DeviceType, type TagType } from '@/utils/utils'
import { debouncedApiCall } from '@/utils/debounce'
import { useDeviceListStore } from '@/store/deviceList'
import { storeToRefs } from 'pinia'

// 设备信息类型
interface Device {
  id: string
  device_sn: string
  device_name: string
  product_key: string
  project_name?: string
  type?: string
  lastDataTime?: string
  lastFileTime?: string
  macAddress?: string
  createdAt?: string
  modifiedAt?: string
}



// API响应类型
interface DeviceListResponse {
  success: boolean
  data: {
    list: Device[]
    total: number,
    pageSize: number
  }
}

const router = useRouter()

// 使用设备列表Store
const deviceListStore = useDeviceListStore()
const { filterForm, currentPage, pageSize, total } = storeToRefs(deviceListStore)

// 设备列表数据（保持原有的本地状态）
const deviceList = ref<Device[]>([])
const loading = ref<boolean>(false)

// DOM引用和高度计算相关状态
const tableWrapperRef = ref<HTMLDivElement | null>(null)
const tableHeight = ref<number>(539) // 默认表格高度
let lastCalculatedHeight = 0 // 记录上次计算的高度，避免无限循环
let resizeTimer: NodeJS.Timeout | null = null // 防抖定时器

// 获取设备列表
const fetchDevices = async (): Promise<void> => {
  try {
    loading.value = true

    // 构建查询参数
    const params: Record<string, any> = {
      page: currentPage.value,
      pageSize: pageSize.value
    }

    // 添加筛选条件
    if (filterForm.value.type && filterForm.value.type !== '全部') params.type = filterForm.value.type
    if (filterForm.value.name) params.name = filterForm.value.name
    if (filterForm.value.serialNumber) params.serialNumber = filterForm.value.serialNumber

    const response = await getDeviceList(params) as DeviceListResponse

    if (response.success) {
      deviceList.value = response.data.list
      total.value = response.data.total
    } else {
      ElMessage.error('获取设备列表失败')
    }
  } catch (error: any) {
    console.error('获取设备列表失败:', error)
    ElMessage.error('获取设备列表失败: ' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

// 搜索设备
const searchDevices = () => {
  currentPage.value = 1
  fetchDevices()
}

// 使用防抖的搜索函数
const debouncedSearchDevices = debouncedApiCall(() => {
  currentPage.value = 1
  fetchDevices()
})

// 组件卸载时清理防抖函数
onUnmounted(() => {
  if (debouncedSearchDevices && typeof debouncedSearchDevices.cancel === 'function') {
    debouncedSearchDevices.cancel()
  }
  if (debouncedSaveState && typeof debouncedSaveState.cancel === 'function') {
    debouncedSaveState.cancel()
  }
})

// 重置表单
const resetForm = () => {
  deviceListStore.resetState()
  fetchDevices()
}

// 当前页变化
const handleCurrentChange = val => {
  currentPage.value = val
  fetchDevices()
}

// 页面大小变化
const handleSizeChange = val => {
  pageSize.value = val
  currentPage.value = 1 // 重置到第一页
  fetchDevices()
}

// 查看设备详情
const viewDeviceDetail = row => {
  router.push({
    path: `/device/detail/${row.id}`,
    query: { deviceName: row.deviceName }
  })
}

// 查看实时数据
const viewRealTimeData = row => {
  router.push({
    path: `/device/realtime/${row.id}`,
    query: { deviceName: row.deviceName }
  })
}

// 查看数字岩芯
const viewDigitalCore = row => {
  router.push({
    path: `/device/digitalcore/${row.id}`,
    query: { deviceName: row.deviceName }
  })
}

// 创建防抖的状态保存函数
const debouncedSaveState = debouncedApiCall(() => {
  deviceListStore.saveState()
})

// 监听设备类型筛选变化，自动发起请求
watch(() => filterForm.value.type, () => {
  currentPage.value = 1 // 重置到第一页
  fetchDevices()
})

// 监听状态变化，自动保存状态
watch([() => filterForm.value.type, () => filterForm.value.name, () => filterForm.value.serialNumber],
  debouncedSaveState, { deep: true })

watch([currentPage, pageSize], debouncedSaveState)

onMounted(async () => {
  // 尝试恢复之前保存的状态
  const hasRestoredState = deviceListStore.restoreState()

  if (hasRestoredState) {
    console.log('已恢复设备列表搜索状态')
  }

  // 加载设备数据
  await fetchDevices()
})
</script>

<style scoped>
.device-container {
  padding: 24px;
  background-color: var(--apple-background);
}

/* 整合搜索和列表的卡片 */
.device-list-card {
  margin-bottom: 24px;
}

/* 筛选区域样式 */
.filter-section {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.filter-buttons {
  text-align: right;
}

/* 分页容器样式 */
.pagination-container {
  margin-top: 20px;
  text-align: center;
}

/* 设备类型标签样式 - 与ApplyAlgorithmDialog.vue保持一致 */
.el-tag {
  width: auto;
  min-width: 90px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .filter-buttons {
    text-align: left;
    margin-top: 16px;
  }
}

.el-table :deep(.el-table__cell) {
  padding: 12px 0;
}
</style>

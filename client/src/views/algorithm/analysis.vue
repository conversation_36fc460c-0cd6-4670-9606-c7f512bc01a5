<template>
  <div class="algorithm-container">
    <!-- <el-card class="algorithm-header">
      <div class="header-content">
        <div class="algorithm-title">
          <h2>
            <el-tag effect="light">
              默认
            </el-tag>
            <el-tag
              type="warning"
              style="margin-left: 10px; margin-right: 5px"
            >
              {{ filterForm.type }}
            </el-tag>
            {{ defaultAlgorithm ? defaultAlgorithm.name : '暂无默认控件算法' }}
          </h2>
          <div class="algorithm-info">
            {{
              defaultAlgorithm
                ? '应用时间: ' + (defaultAlgorithm.modifiedAt || defaultAlgorithm.createdAt)
                : ''
            }}
          </div>
        </div>
        <el-button
          type="primary"
          class="switch-button"
          @click="openSwitchDialog"
        >
          切换
        </el-button>
      </div>
    </el-card> -->

    <!-- 搜索和列表整合在一个卡片中 -->
    <el-card
      v-loading="loading"
      class="algorithm-list-card"
    >
      <!-- 筛选区域 -->
      <div class="filter-section">
        <el-row :gutter="24">
          <el-col :span="4">
            <el-select
              v-model="filterForm.type"
              placeholder="控件类型"
              @change="handleTypeChange"
            >
              <el-option
                label="全部"
                value="全部"
              />
              <el-option
                label="超前钻机"
                value="PDF"
              />
              <el-option
                label="水锤"
                value="WPD"
              />
              <el-option
                label="锚杆钻"
                value="AD"
              />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-input
              v-model.trim="filterForm.name"
              placeholder="算法名称"
              clearable
              maxlength="32"
              show-word-limit
              @keyup.enter="debouncedHandleSearch"
            />
          </el-col>
          <el-col
            :span="4"
            class="filter-buttons"
          >
            <el-button
              type="primary"
              @click="debouncedHandleSearch"
            >
              搜索
            </el-button>
            <el-button @click="resetForm">
              清空
            </el-button>
          </el-col>
          <el-col
            :span="12"
            class="action-buttons"
          >
            <el-button
              type="primary"
              @click="addNewAlgorithm"
            >
              新增算法
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 算法列表 -->
      <el-table
        :data="algorithmList"
        style="width: 100%"
        stripe
      >
        <!-- 新增的序号列 -->
        <el-table-column
          type="index"
          label="序号"
          width="60"
          align="center"
        />
        <el-table-column
          label="设备类型"
          align="center"
        >
          <template #default="scope">
            <el-tag
              v-if="scope.row.product_key"
              :type="getDeviceTagType(scope.row.product_key)"
            >
              {{ scope.row.productName }}
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="name"
          label="算法名称"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          prop="usedNum"
          label="应用设备数量"
          align="center"
        />
        <el-table-column
          prop="updateTime"
          label="最后修改时间"
          align="center"
        />
        <el-table-column
          label="操作"
          align="center"
          width="360"
        >
          <template #default="scope">
            <el-button
              type="primary"
              link
              @click="editAlgorithm(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              type="primary"
              link
              @click="copyAlgorithm(scope.row)"
            >
              复制
            </el-button>
            <el-button
              type="primary"
              link
              @click="applyAlgorithm(scope.row)"
            >
              应用
            </el-button>

            <el-button
              type="danger"
              link
              @click="deleteAlgorithm(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页部分 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next"
          :total="total"
          :current-page="currentPage"
          :page-sizes="[10, 20, 40, 50, 60, 80, 100]"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </el-card>

    <!-- 新增算法对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="新增算法"
      width="30%"
      :close-on-click-modal="false"
    >
      <el-form
        :model="algorithmForm"
        label-width="100px"
      >
        <el-form-item
          label="算法名称"
          required
        >
          <el-input
            v-model="algorithmForm.name"
            placeholder="请输入算法名称"
            maxlength="32"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="算法类型">
          <el-select
            v-model="algorithmForm.type"
            disabled
          >
            <el-option
              label="控件算法"
              :value="1"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="设备类型">
          <el-select v-model="algorithmForm.product_key">
            <el-option
              label="超前钻机"
              value="PDF"
            />
            <el-option
              label="水锤"
              value="WPD"
            />
            <el-option
              label="锚杆钻"
              value="AD"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            :loading="submitLoading"
            @click="submitAlgorithm"
          >
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 切换默认算法对话框 -->
    <el-dialog
      v-model="switchDialogVisible"
      title="切换默认算法"
      width="50%"
    >
      <div v-loading="switchLoading">
        <el-table
          :data="availableAlgorithms"
          style="width: 100%"
          border
          highlight-current-row
        >
          <el-table-column
            prop="name"
            label="算法名称"
          />
          <el-table-column
            prop="updateTime"
            label="最后修改时间"
          />
          <el-table-column
            label="当前状态"
            width="100"
          >
            <template #default="scope">
              <el-tag
                v-if="scope.row.isDefault"
                type="success"
              >
                默认
              </el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            width="120"
            align="center"
          >
            <template #default="scope">
              <el-button
                type="primary"
                size="small"
                :disabled="scope.row.isDefault"
                @click="setAsDefault(scope.row)"
              >
                设为默认
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="switchDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 应用算法对话框 -->
    <ApplyAlgorithmDialog
      v-model:visible="applyDialogVisible"
      :algorithm-id="currentAlgorithm.id"
      :algorithm-name="currentAlgorithm.name"
      :algorithm-type="1"
      :product-key="currentAlgorithm.product_key"
      @success="handleApplySuccess"
    />

    <!-- 复制算法对话框 -->
    <el-dialog
      v-model="copyDialogVisible"
      title="复制算法"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form label-width="100px">
        <el-input
          v-model="copyAlgorithmName"
          placeholder="请输入新算法名称"
          maxlength="32"
          show-word-limit
        />
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="copyDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            :loading="copyLoading"
            :disabled="!copyAlgorithmName.trim()"
            @click="confirmCopyAlgorithm"
          >
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/**
 * 算法分析页面
 * 管理分析算法的创建、编辑、删除和应用
 */

import { ref, reactive, onMounted, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import {
  getArithmeticList,
  getArithmeticDetail,
  createArithmetic,
  updateArithmetic,
  deleteArithmetic
} from '@/api'
import ApplyAlgorithmDialog from '@/components/ApplyAlgorithmDialog.vue'
import { debouncedApiCall, useDebounce, DEBOUNCE_DELAYS } from '@/utils/debounce'
import { useAnalysisAlgorithmListStore } from '@/store/analysisAlgorithmList'
import { storeToRefs } from 'pinia'
import { getDeviceTagType } from '@/utils/utils'

// 算法信息类型
interface Algorithm {
  id: string
  name: string
  description: string
  type: number
  product_key: string
  isPublished: boolean
  createdAt?: string
  modifiedAt?: string
  status?: string
}

// 筛选表单类型
interface FilterForm {
  type: string
  name: string
  productKey: string
}

// 算法表单类型
interface AlgorithmForm {
  id: string | null
  name: string
  description: string
  type: number
  product_key: string
  isPublished: boolean
}

// API响应类型
interface AlgorithmListResponse {
  success: boolean
  data: {
    list: Algorithm[]
    total: number
  }
}

const router = useRouter()

// 使用控件算法列表Store
const analysisAlgorithmListStore = useAnalysisAlgorithmListStore()
const { filterForm, currentPage, pageSize, total } = storeToRefs(analysisAlgorithmListStore)

// 算法列表数据
const algorithmList = ref<Algorithm[]>([])
const loading = ref<boolean>(false)
const submitLoading = ref<boolean>(false)

const dialogVisible = ref<boolean>(false)
const dialogType = ref<'add' | 'edit'>('add') // 'add' 或 'edit'
const algorithmForm = reactive<AlgorithmForm>({
  id: null,
  name: '',
  description: '',
  type: 1, // 分析算法类型为1
  product_key: '全部', // 默认为PDF
  isPublished: false
})

// 算法切换对话框
const switchDialogVisible = ref<boolean>(false)
const switchLoading = ref<boolean>(false)
const availableAlgorithms = ref<Algorithm[]>([])

// 应用算法对话框
const applyDialogVisible = ref<boolean>(false)
const currentAlgorithm = ref<Algorithm | Record<string, any>>({})

// 复制算法对话框
const copyDialogVisible = ref<boolean>(false)
const copyAlgorithmName = ref<string>('')
const copyLoading = ref<boolean>(false)
const currentCopyAlgorithm = ref<Algorithm | Record<string, any>>({})

// 获取默认分析算法


// 获取算法列表
const fetchAlgorithms = async () => {
  try {
    loading.value = true

    // 构建查询参数
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      type: 1, // 分析算法类型为1
      product_key: filterForm.value.type === '全部' ? undefined : filterForm.value.type, // 设备类型
      name: filterForm.value.name
    }

    const response = await getArithmeticList(params)

    if (response.success) {
      algorithmList.value = response.data.list.map(item => ({
        id: item.id,
        productName: item.productName,
        name: item.name,
        usedNum: item.usedDeviceCount || '0', // 使用后端返回的应用设备数量
        updateTime: item.modifiedAt || item.createdAt,
        product_key: item.productKey,
        isDefault: item.isDefault
      }))
      total.value = response.data.total
    } else {
      ElMessage.error('获取算法列表失败')
    }
  } catch (error) {
    console.error('获取算法列表失败:', error)
    ElMessage.error('获取算法列表失败: ' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}



const handleCurrentChange = val => {
  currentPage.value = val
  fetchAlgorithms()
}

// 页面大小变化
const handleSizeChange = val => {
  pageSize.value = val
  currentPage.value = 1 // 重置到第一页
  fetchAlgorithms()
}

// 搜索功能（原始函数）
const handleSearchOriginal = () => {
  currentPage.value = 1 // 重置到第一页
  fetchAlgorithms()
}

// 防抖版本的搜索函数
const debouncedHandleSearch = useDebounce(handleSearchOriginal, DEBOUNCE_DELAYS.SEARCH).debouncedFn

// 当设备类型变化时触发
const handleTypeChange = async () => {
  // 重置页码
  currentPage.value = 1
  // 重新获取算法列表
  fetchAlgorithms()
}

// 重置筛选表单
const resetForm = () => {
  analysisAlgorithmListStore.resetState()
  fetchAlgorithms()
}

const addNewAlgorithm = () => {
  dialogType.value = 'add'
  Object.keys(algorithmForm).forEach(key => {
    if (key !== 'isPublished' && key !== 'type' && key !== 'product_key') {
      algorithmForm[key] = key === 'id' ? null : ''
    } else if (key === 'type') {
      algorithmForm[key] = 1 // 分析算法类型为1
    } else if (key === 'product_key') {
      algorithmForm[key] = 'PDF' // 默认为超前钻机
    } else {
      algorithmForm[key] = false
    }
  })
  dialogVisible.value = true
}

// 提交新增算法（原始函数）
const submitAlgorithmOriginal = async () => {
  // 验证表单
  if (!algorithmForm.name) {
    ElMessage.warning('请输入算法名称')
    return
  }

  try {
    submitLoading.value = true

    // 创建算法对象
    const newAlgorithm = {
      name: algorithmForm.name,
      type: 1, // 分析算法类型为1
      product_key: algorithmForm.product_key, // 设备类型
      content: '',
      is_default: false // 新创建的不是默认算法
    }

    // 调用创建API
    const response = await createArithmetic(newAlgorithm)

    if (response.success) {
      ElMessage.success('新增算法成功')
      dialogVisible.value = false
      fetchAlgorithms() // 刷新列表

      // 如果需要，可以直接跳转到编辑页面
      if (response.data && response.data.id) {
        router.push({
          path: `/algorithm/edit/${response.data.id}`,
          query: {
            type: '1',
            name: algorithmForm.name
          }
        })
      }
    } else {
      ElMessage.error('新增算法失败')
    }
  } catch (error) {
    console.error('新增算法失败:', error)
    ElMessage.error('新增算法失败: ' + (error.message || '未知错误'))
  } finally {
    submitLoading.value = false
  }
}

// 防抖版本的提交函数
const submitAlgorithm = useDebounce(submitAlgorithmOriginal, DEBOUNCE_DELAYS.API_CALL).debouncedFn

const editAlgorithm = row => {
  // 跳转到算法编辑页面
  router.push({
    path: `/algorithm/edit/${row.id}`,
    query: {
      type: '1',
      name: row.name
    }
  })
}

const copyAlgorithm = (row) => {
  // 打开自定义复制对话框
  currentCopyAlgorithm.value = row
  copyAlgorithmName.value = ''
  copyDialogVisible.value = true
}

// 确认复制算法
const confirmCopyAlgorithm = async () => {
  if (!copyAlgorithmName.value.trim()) {
    ElMessage.warning('请输入算法名称')
    return
  }

  try {
    copyLoading.value = true

    // 获取要复制的算法详情
    const response = await getArithmeticDetail(currentCopyAlgorithm.value.id)

    if (response.success) {
      // 创建新算法（复制）
      const newAlgorithm = {
        type: 1, // 分析算法
        name: copyAlgorithmName.value.trim(),
        product_key: response.data.productKey,
        content: response.data.content,
        md5: response.data.md5,
        is_default: false // 复制的不设为默认
      }

      const createResponse = await createArithmetic(newAlgorithm)

      if (createResponse.success) {
        ElMessage.success('复制算法成功')
        copyDialogVisible.value = false
        fetchAlgorithms() // 刷新列表
      } else {
        ElMessage.error('复制算法失败')
      }
    } else {
      ElMessage.error('获取算法详情失败')
    }
  } catch (error: any) {
    console.error('复制算法失败:', error)
    ElMessage.error('复制算法失败: ' + (error?.message || '未知错误'))
  } finally {
    copyLoading.value = false
  }
}

const applyAlgorithm = row => {
  currentAlgorithm.value = row
  applyDialogVisible.value = true
}

// 应用成功回调
const handleApplySuccess = () => {
  // 刷新算法列表数据，更新应用设备数量
  fetchAlgorithms()
}

const deleteAlgorithm = row => {
  if (row.isDefault) {
    ElMessage.warning('默认算法不能删除')
    return
  }

  // 检查算法是否有应用设备
  if (row.usedNum && row.usedNum > 0) {
    ElMessage.warning(`该算法已应用到 ${row.usedNum} 个设备，请先解绑设备后再删除`)
    return
  }

  ElMessageBox.confirm(`确定要删除算法 "${row.name}" 吗？此操作不可恢复。`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      try {
        const response = await deleteArithmetic(row.id)

        if (response.success) {
          ElMessage.success('删除成功')
          fetchAlgorithms() // 刷新列表
        } else {
          ElMessage.error('删除失败')
        }
      } catch (error) {
        console.error('删除算法失败:', error)
        ElMessage.error('删除失败: ' + (error.message || '未知错误'))
      }
    })
    .catch(() => {
      // 取消操作
    })
}

// 打开切换算法对话框
const openSwitchDialog = async () => {
  switchDialogVisible.value = true
  await fetchAvailableAlgorithms()
}

// 获取可用算法列表（当前设备类型的所有算法）
const fetchAvailableAlgorithms = async () => {
  try {
    switchLoading.value = true

    // 获取所有算法
    const params = {
      type: 1, // 分析算法类型为1
      page: 1,
      pageSize: 100 // 获取足够多的算法
    }

    const response = await getArithmeticList(params)

    if (response.success) {
      availableAlgorithms.value = response.data.list.map(item => ({
        id: item.id,
        name: item.name,
        updateTime: item.modifiedAt || item.createdAt,
        isDefault: item.isDefault
      }))
    } else {
      ElMessage.error('获取可用算法列表失败')
    }
  } catch (error) {
    console.error('获取可用算法列表失败:', error)
    ElMessage.error('获取可用算法列表失败: ' + (error.message || '未知错误'))
  } finally {
    switchLoading.value = false
  }
}

// 设置为默认算法
const setAsDefault = async algorithm => {
  try {
    switchLoading.value = true

    // 将选中的算法设为默认
    const response = await updateArithmetic(algorithm.id, { is_default: true })

    if (response.success) {
      ElMessage.success(`已将 "${algorithm.name}" 设为默认算法`)

      // 更新算法列表
      await fetchAvailableAlgorithms()
      switchDialogVisible.value = false // 设置成功后关闭对话框

      // 刷新主列表
      fetchAlgorithms()
    } else {
      ElMessage.error('设置默认算法失败')
    }
  } catch (error) {
    console.error('设置默认算法失败:', error)
    ElMessage.error('设置默认算法失败: ' + (error.message || '未知错误'))
  } finally {
    switchLoading.value = false
  }
}

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '-'
  try {
    const date = new Date(dateStr)
    return date.toLocaleString('zh-CN', { 
      year: 'numeric', 
      month: '2-digit', 
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  } catch (e) {
    return dateStr
  }
}



// 监听状态变化，自动保存状态
watch([() => filterForm.value.type, () => filterForm.value.name, () => currentPage.value, () => pageSize.value],
  () => {
    analysisAlgorithmListStore.saveState()
  },
  { deep: true }
)

onMounted(async () => {
  // 恢复保存的搜索状态
  const restored = analysisAlgorithmListStore.restoreState()
  if (restored) {
    console.log('已恢复控件算法列表搜索状态')
  }

  // 加载算法列表
  fetchAlgorithms()
})


</script>

<style scoped>
.algorithm-container {
  padding: 20px;
}

.algorithm-header {
  margin-bottom: 20px;
  background-color: #409eff;
  color: white;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.algorithm-title {
  display: flex;
  justify-content: start;
  align-items: baseline;
}

.algorithm-info {
  margin-left: 10px;
  font-size: 12px;
}

.switch-button {
  margin-left: auto;
}

/* 整合搜索和列表的卡片 */
.algorithm-list-card {
  margin-bottom: 24px;
}

/* 筛选区域样式 */
.filter-section {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.filter-buttons {
  text-align: left;
}

.action-buttons {
  text-align: right;
}

/* 分页容器样式 */
.pagination-container {
  margin-top: 20px;
  text-align: center;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .action-buttons {
    text-align: left;
    margin-top: 16px;
  }
}

.ml-2 {
  margin-left: 8px;
}

.error-message {
  color: #f56c6c;
}
</style>

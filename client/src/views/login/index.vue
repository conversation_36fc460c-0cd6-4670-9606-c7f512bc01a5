<template>
  <div class="login-container">
    <!-- 添加背景容器 -->
    <div
      id="bg-container"
      class="bg-fluid"
    />
    
    <div class="login-box">
      <div class="login-left">
        <div class="company-info">
          <img
            src="@/assets/images/logo.png"
            alt="海聚科技"
            class="logo"
          >
          <h1
            ref="companyNameRef"
            class="company-name"
          >
            <span
              v-for="(char, index) in '海聚科技'"
              :key="index"
              class="text-char"
            >{{ char }}</span>
          </h1>
          <p class="company-desc">
            装备改善劳动品质
          </p>
        </div>
      </div>
      <div class="login-form">
        <h2 class="login-title">
          欢迎回来
        </h2>
        <p class="login-subtitle">
          请登录您的账户
        </p>
        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          class="form"
        >
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              placeholder="用户名"
              prefix-icon="User"
              class="login-input"
              maxlength="15"
            />
          </el-form-item>
          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="密码"
              prefix-icon="Lock"
              show-password
              class="login-input"
              maxlength="15"
              @keyup.enter="handleLogin"
            />
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              class="login-button"
              :loading="loading"
              @click="handleLogin"
            >
              登录
            </el-button>
          </el-form-item>

          <!-- 钉钉登录按钮 -->
          <div class="dingtalk-login">
            <el-button
              class="dingtalk-button"
              @click="handleDingTalkLogin"
            >
              <svg
                class="dingtalk-icon"
                viewBox="0 0 1024 1024"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z"
                  fill="#4786FF"
                />
                <path
                  d="M720.738462 445.046154c0 3.938462-3.938462 11.815385-7.876924 15.753846-19.692308 39.384615-66.953846 118.153846-66.953846 118.153846l-15.753846 23.630769h66.953846l-133.907692 173.292308 31.507692-118.153846h-55.138461l19.692307-78.769231c-15.753846 3.938462-31.507692 7.876923-55.138461 15.753846 0 0-27.569231 15.753846-82.707692-31.507692 0 0-35.446154-31.507692-15.753847-39.384615l70.892308-11.815385c35.446154-3.938462 59.076923-7.876923 59.076923-7.876923s-114.215385 0-141.784615-3.938462c-27.569231-3.938462-63.015385-47.261538-66.953846-90.584615 0 0-11.815385-23.630769 23.630769-11.815385 35.446154 11.815385 181.169231 39.384615 181.169231 39.384616s-185.107692-51.2-196.923077-66.953846c-19.692308-35.446154-31.507692-74.830769-31.507693-118.153847 0-3.938462 0-3.938462 3.938462-7.876923H315.076923s141.784615 63.015385 236.307692 98.461539 181.169231 55.138462 169.353847 102.4z"
                  fill="#FFFFFF"
                />
              </svg>
            </el-button>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
/**
 * 登录页面
 * 提供用户名密码登录和钉钉OAuth登录功能
 */

import { ref, reactive, onMounted, onBeforeUnmount, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/store/user'
import { debouncedApiCall } from '@/utils/debounce'
import { generateDingTalkAuthUrl, getEnvironmentInfo } from '@/utils/dingtalk'
import gsap from 'gsap'

// 登录表单类型
interface LoginForm {
  username: string
  password: string
}

// 表单验证规则类型
interface LoginRules {
  username: Array<{
    required: boolean
    message: string
    trigger: string
  }>
  password: Array<{
    required: boolean
    message: string
    trigger: string
  }>
}

// 流体背景实例类型声明
declare global {
  interface Window {
    FluidBackground: any
    Color4Bg: any
  }
}

const router = useRouter()
const route = useRoute()
const loginFormRef = ref<any>(null)
const loading = ref<boolean>(false)
const userStore = useUserStore()
// 背景实例引用
let fluidBg: any = null
// 添加公司名称引用
const companyNameRef = ref<HTMLElement | null>(null)

const loginForm = reactive<LoginForm>({
  username: '',
  password: ''
})

const loginRules: LoginRules = {
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }]
}

// 检查输入是否完整
const validateInputs = (): boolean => {
  // 检查用户名
  if (!loginForm.username || loginForm.username.trim() === '') {
    ElMessage.warning('请输入用户名')
    return false
  }

  // 检查密码
  if (!loginForm.password || loginForm.password.trim() === '') {
    ElMessage.warning('请输入密码')
    return false
  }

  return true
}

// 创建防抖的登录函数
const debouncedLogin = debouncedApiCall(async () => {
  if (!loginFormRef.value) return

  try {
    // 首先检查基本输入
    if (!validateInputs()) {
      return
    }

    // 然后进行表单验证
    await loginFormRef.value.validate()
    loading.value = true

    const success = await userStore.loginAction(loginForm.username, loginForm.password)

    if (success) {
      // 跳转到首页
      router.push('/')
    }
  } catch (error: any) {
    console.error('登录失败:', error)
    ElMessage.error('登录失败: ' + (error?.message || '未知错误'))
  } finally {
    loading.value = false
  }
})

// 处理登录
const handleLogin = () => {
  // 防止重复提交
  if (loading.value) return

  // 先进行基本输入检查，通过后再调用防抖登录
  if (validateInputs()) {
    debouncedLogin()
  }
}

// 处理钉钉登录
const handleDingTalkLogin = () => {
  const dingTalkUrl = generateDingTalkAuthUrl()
  window.location.href = dingTalkUrl
}

// 处理钉钉登录回调
const handleDingTalkCallback = async (code: any) => {
  try {
    loading.value = true

    // 调用后端API，使用code换取用户信息并完成登录
    const success = await userStore.dingTalkLoginAction(code)
    if (success) {
      router.push('/')
    }

  } catch (error: any) {
    console.error('钉钉登录回调处理失败:', error)
    ElMessage.error('钉钉登录失败: ' + (error?.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

// 组件挂载时初始化背景
onMounted(() => {
  // 检查是否有钉钉登录回调的code参数
  const code = route.query.code || route.query.authCode
  if (code) {
    handleDingTalkCallback(code)
    // 清理URL参数，避免刷新页面时重复处理
    router.replace('/login')
  }

  // 尝试加载背景脚本的函数
  const loadFluidBackground = async () => {
    try {
      // 方法1：尝试通过script标签加载
      const script = document.createElement('script');
      script.src = '/jsm/AestheticFluidBg.min.js';
      
      const scriptLoaded = new Promise((resolve, reject) => {
        script.onload = () => {
          setTimeout(() => {
            if (window.Color4Bg && window.Color4Bg.AestheticFluidBg) {
              try {
                fluidBg = new window.Color4Bg.AestheticFluidBg({
                  dom: "bg-container",
                  colors: ["#F00911", "#F3AA00", "#F6EE0B", "#39E90D", "#195ED2", "#F00911"],
                  loop: true
                });
                resolve(true);
              } catch (error) {
                console.error('背景初始化失败:', error);
                reject(error);
              }
            } else {
              console.error('未找到背景构造函数');
              reject(new Error('背景构造函数未找到'));
            }
          }, 100);
        };
        
        script.onerror = (error) => {
          console.error('背景脚本加载失败:', error);
          console.error('请确保 AestheticFluidBg.min.js 文件位于 public/jsm/ 目录下');
          reject(error);
        };
      });
      
      document.head.appendChild(script);
      await scriptLoaded;
      
    } catch (error) {
      console.warn('流体背景加载失败，使用静态背景:', error);
      // 如果加载失败，设置静态渐变背景
      const bgContainer = document.getElementById('bg-container');
      if (bgContainer) {
        bgContainer.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
      }
    }
  };
  
  // 执行背景加载
  loadFluidBackground();
  
  // 初始化GSAP动画
  initTextAnimation();
})

// 添加文字动画初始化函数
const initTextAnimation = () => {
  if (!companyNameRef.value) return;
  
  // 获取所有文字字符元素
  const chars = companyNameRef.value.querySelectorAll('.text-char');
  
  // 重置初始状态
  gsap.set(chars, { 
    y: -50,
    opacity: 0
  });
  
  // 创建动画
  gsap.to(chars, { 
    y: 0,
    opacity: 1,
    stagger: 0.2,
    duration: 0.8,
    ease: "back.out(1.7)",
    onComplete: () => {
      // 设置循环动画
      gsap.fromTo(chars, 
        { scale: 1, y: 0 },
        { 
          scale: 1.1, 
          y: -5,
          stagger: 0.1,
          duration: 0.8,
          repeat: -1,
          yoyo: true,
          ease: "sine.inOut"
        }
      );
    }
  });
}

// 组件卸载前清理资源
onBeforeUnmount(() => {
  if (fluidBg) {
    // 如果背景实例有提供销毁方法，调用它
    if (typeof fluidBg.destroy === 'function') {
      fluidBg.destroy()
    }
    fluidBg = null
  }
})

// 组件卸载时清理防抖函数
onUnmounted(() => {
  if (debouncedLogin && typeof debouncedLogin.cancel === 'function') {
    debouncedLogin.cancel()
  }
})
</script>

<style scoped>
.login-container {
  height: 100%;
  width: 100%;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 背景容器样式 */
.bg-fluid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

.login-box {
  width: 800px;
  height: 500px;
  background-color: rgba(15, 23, 42, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  display: flex;
  overflow: hidden;
  position: relative;
  z-index: 1;
}

.login-left {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
  background-color: rgba(15, 23, 42, 0.5);
}

.company-info {
  text-align: center;
  position: relative;
  z-index: 1;
}

.logo {
  width: 100px;
  margin-bottom: 24px;
  filter: brightness(0) invert(1);
}

.company-name {
  font-size: 28px;
  color: white;
  margin-bottom: 12px;
  font-weight: 500;
  display: flex;
  justify-content: center;
}

.text-char {
  display: inline-block;
}

.company-desc {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
}

.login-form {
  width: 380px;
  padding: 50px 40px;
  background-color: white;
  display: flex;
  flex-direction: column;
}

.login-title {
  font-size: 24px;
  color: #0f172a;
  margin-bottom: 8px;
  font-weight: 600;
}

.login-subtitle {
  color: #64748b;
  margin-bottom: 28px;
  font-size: 14px;
}

.form {
  width: 100%;
}

.login-input :deep(.el-input__inner) {
  height: 45px;
  background: #ffffff;
  color: #0f172a;
  font-size: 14px;
}

.login-input :deep(.el-input__prefix) {
  font-size: 16px;
  color: #94a3b8;
}



.login-button {
  width: 100%;
  height: 45px;
  border-radius: 6px;
  background-color: #ef4444;
  border: none;
  font-size: 14px;
  font-weight: 500;
  color: white;
  transition: all 0.2s ease;
}

.login-button:hover {
  background-color: #dc2626;
}

/* 钉钉登录样式 */
.dingtalk-login {
  margin-top: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.dingtalk-button {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: transparent;
  border: none;
  padding: 0;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dingtalk-button:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(71, 134, 255, 0.3);
}

.dingtalk-icon {
  width: 48px;
  height: 48px;
}
</style>

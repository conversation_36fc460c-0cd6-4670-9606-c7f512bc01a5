/**
 * 文件列表状态管理Store
 * 海聚科技钻井数据监控系统 - 文件列表搜索状态持久化
 * 
 * 基于通用状态持久化工具类实现，为文件列表页面提供
 * 搜索状态的自动保存和恢复功能。特别处理日期范围的序列化逻辑。
 */

import { defineStore } from 'pinia'
import { ref, reactive, computed } from 'vue'
import { 
  createStatePersistence, 
  createDebouncedSaveState,
  type BaseListState,
  type StatePersistenceConfig,
  type StateValidator
} from '@/utils/listStatePersistence'

// ==================== 类型定义 ====================

/**
 * 文件列表筛选表单接口
 * 定义文件搜索和筛选的条件
 */
export interface FileFilterForm {
  /** 设备序列号 */
  deviceSn: string
  /** 文件名称搜索关键词 */
  fileName: string
  /** 开始日期 (YYYY-MM-DD HH:mm:ss格式) */
  startDate: string
  /** 结束日期 (YYYY-MM-DD HH:mm:ss格式) */
  endDate: string
}

/**
 * 文件列表状态接口
 * 包含所有需要在组件中使用的状态
 */
export interface FileListState extends BaseListState<FileFilterForm> {
  /** 筛选表单状态 */
  filterForm: FileFilterForm
  /** 当前页码 */
  currentPage: number
  /** 每页显示数量 */
  pageSize: number
  /** 总记录数 */
  total: number
}

/**
 * 文件列表Store接口
 * 定义Store暴露的所有状态和方法
 */
export interface FileListStore extends FileListState {
  /** 日期范围数组 (用于日期选择器) */
  dateRange: string[]
  /** 保存状态到sessionStorage */
  saveState: () => void
  /** 从sessionStorage恢复状态 */
  restoreState: () => boolean
  /** 清除保存的状态 */
  clearState: () => void
  /** 重置状态到默认值 */
  resetState: () => void
  /** 从startDate和endDate构建dateRange */
  buildDateRange: () => string[]
  /** 从dateRange更新startDate和endDate */
  updateDateFromRange: (dateRange: string[]) => void
}

// ==================== 日期处理工具函数 ====================

/**
 * 验证日期格式是否正确
 * @param dateStr 日期字符串
 * @returns 是否为有效日期格式
 */
export const validateDateFormat = (dateStr: string): boolean => {
  if (!dateStr) return true // 空字符串是有效的（表示未选择）
  
  // 检查基本格式 YYYY-MM-DD HH:mm:ss
  const dateTimeRegex = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/
  if (!dateTimeRegex.test(dateStr)) {
    return false
  }
  
  // 检查日期是否有效
  const date = new Date(dateStr)
  return !isNaN(date.getTime())
}

/**
 * 从日期时间字符串提取日期部分
 * @param dateTimeStr 日期时间字符串 (YYYY-MM-DD HH:mm:ss)
 * @returns 日期部分 (YYYY-MM-DD)
 */
export const extractDatePart = (dateTimeStr: string): string => {
  if (!dateTimeStr) return ''
  return dateTimeStr.split(' ')[0] || ''
}

/**
 * 构建日期时间字符串
 * @param datePart 日期部分 (YYYY-MM-DD)
 * @param isEndDate 是否为结束日期（结束日期使用23:59:59）
 * @returns 完整的日期时间字符串
 */
export const buildDateTime = (datePart: string, isEndDate: boolean = false): string => {
  if (!datePart) return ''
  const timePart = isEndDate ? '23:59:59' : '00:00:00'
  return `${datePart} ${timePart}`
}

// ==================== 验证器函数 ====================

/**
 * 文件列表筛选表单验证器
 * 验证筛选表单数据的格式是否正确，特别验证日期格式
 */
const validateFileFilterForm: StateValidator<FileFilterForm> = (filterForm: any): filterForm is FileFilterForm => {
  if (!filterForm || typeof filterForm !== 'object') {
    return false
  }

  // 检查必需字段类型
  if (typeof filterForm.deviceSn !== 'string' ||
      typeof filterForm.fileName !== 'string' ||
      typeof filterForm.startDate !== 'string' ||
      typeof filterForm.endDate !== 'string') {
    return false
  }

  // 验证日期格式
  if (!validateDateFormat(filterForm.startDate) || !validateDateFormat(filterForm.endDate)) {
    console.warn('无效的日期格式，将重置日期字段')
    return false
  }

  // 验证日期逻辑（开始日期不能晚于结束日期）
  if (filterForm.startDate && filterForm.endDate) {
    const startTime = new Date(filterForm.startDate).getTime()
    const endTime = new Date(filterForm.endDate).getTime()
    if (startTime > endTime) {
      console.warn('开始日期不能晚于结束日期，将重置日期字段')
      return false
    }
  }

  return true
}

// ==================== 状态持久化配置 ====================

/**
 * 状态持久化配置
 */
const persistenceConfig: StatePersistenceConfig<FileFilterForm> = {
  storageKeyPrefix: 'file_list_state',
  defaultFilterForm: {
    deviceSn: '',
    fileName: '',
    startDate: '',
    endDate: ''
  },
  validateFilterForm: validateFileFilterForm,
  expiryTime: 60 * 60 * 1000 // 1小时过期
}

// ==================== Store定义 ====================

/**
 * 文件列表状态管理Store
 * 使用Pinia defineStore创建，提供响应式状态管理和持久化功能
 */
export const useFileListStore = defineStore('fileList', (): FileListStore => {
  // ==================== 响应式状态 ====================
  
  /**
   * 筛选表单状态
   * 使用reactive创建响应式对象，支持深度监听
   */
  const filterForm = reactive<FileFilterForm>({
    deviceSn: '',
    fileName: '',
    startDate: '',
    endDate: ''
  })

  /**
   * 分页相关状态
   * 使用ref创建响应式引用
   */
  const currentPage = ref<number>(1)
  const pageSize = ref<number>(10)
  const total = ref<number>(0)

  /**
   * 日期范围数组（用于日期选择器）
   * 根据startDate和endDate计算得出
   */
  const dateRange = computed<string[]>({
    get: () => buildDateRange(),
    set: (value: string[]) => updateDateFromRange(value)
  })

  // ==================== 状态持久化管理器 ====================
  
  /**
   * 创建状态持久化管理器实例
   */
  const persistence = createStatePersistence(persistenceConfig)

  /**
   * 创建防抖状态保存函数
   * 避免频繁的状态保存操作，提高性能
   */
  const debouncedSaveState = createDebouncedSaveState(
    persistence,
    () => ({
      filterForm: { ...filterForm },
      currentPage: currentPage.value,
      pageSize: pageSize.value
    }),
    300 // 300ms防抖延迟
  )

  // ==================== 日期处理方法 ====================

  /**
   * 从startDate和endDate构建dateRange数组
   * @returns 日期范围数组
   */
  const buildDateRange = (): string[] => {
    if (!filterForm.startDate || !filterForm.endDate) {
      return []
    }
    
    const startDatePart = extractDatePart(filterForm.startDate)
    const endDatePart = extractDatePart(filterForm.endDate)
    
    if (!startDatePart || !endDatePart) {
      return []
    }
    
    return [startDatePart, endDatePart]
  }

  /**
   * 从dateRange数组更新startDate和endDate
   * @param dateRange 日期范围数组
   */
  const updateDateFromRange = (dateRange: string[]): void => {
    if (!dateRange || dateRange.length !== 2) {
      filterForm.startDate = ''
      filterForm.endDate = ''
      return
    }
    
    filterForm.startDate = buildDateTime(dateRange[0], false)
    filterForm.endDate = buildDateTime(dateRange[1], true)
  }

  // ==================== 状态管理方法 ====================

  /**
   * 保存状态到sessionStorage
   * 将当前的筛选条件和分页状态保存到sessionStorage
   */
  const saveState = (): void => {
    const currentState = {
      filterForm: { ...filterForm },
      currentPage: currentPage.value,
      pageSize: pageSize.value
    }
    persistence.saveState(currentState)
  }

  /**
   * 从sessionStorage恢复状态
   * 从sessionStorage中恢复之前保存的搜索状态
   * @returns 是否成功恢复状态
   */
  const restoreState = (): boolean => {
    try {
      const restoredState = persistence.restoreState()
      
      if (!restoredState) {
        return false
      }

      // 恢复状态前进行备份（用于回滚）
      const backupState = {
        filterForm: { ...filterForm },
        currentPage: currentPage.value,
        pageSize: pageSize.value,
        total: total.value
      }

      try {
        // 恢复筛选表单状态
        Object.assign(filterForm, restoredState.filterForm)
        
        // 恢复分页状态
        currentPage.value = restoredState.currentPage
        pageSize.value = restoredState.pageSize
        // 注意：total不从持久化状态恢复，因为数据可能已变化

        console.log('已恢复文件列表搜索状态', {
          filterForm: restoredState.filterForm,
          currentPage: restoredState.currentPage,
          pageSize: restoredState.pageSize,
          dateRange: buildDateRange()
        })
        
        return true
      } catch (restoreError) {
        console.error('恢复文件列表状态时发生错误，回滚到备份状态:', restoreError)
        
        // 回滚到备份状态
        Object.assign(filterForm, backupState.filterForm)
        currentPage.value = backupState.currentPage
        pageSize.value = backupState.pageSize
        total.value = backupState.total
        
        // 清除损坏的状态数据
        persistence.clearState()
        return false
      }
    } catch (error) {
      console.error('恢复文件列表状态失败:', error)
      return false
    }
  }

  /**
   * 清除保存的状态
   * 从sessionStorage中清除保存的搜索状态
   */
  const clearState = (): void => {
    persistence.clearState()
  }

  /**
   * 重置状态到默认值
   * 将所有状态重置为初始默认值
   */
  const resetState = (): void => {
    // 重置筛选表单
    filterForm.deviceSn = ''
    filterForm.fileName = ''
    filterForm.startDate = ''
    filterForm.endDate = ''

    // 重置分页状态
    currentPage.value = 1
    pageSize.value = 10
    total.value = 0

    // 立即保存重置后的状态
    const resetStateData = {
      filterForm: { ...filterForm },
      currentPage: currentPage.value,
      pageSize: pageSize.value
    }
    persistence.saveState(resetStateData)

    console.log('文件列表状态已重置为默认值')
  }

  // ==================== 返回Store接口 ====================
  
  return {
    // 响应式状态
    filterForm,
    currentPage,
    pageSize,
    total,
    dateRange,
    
    // 状态管理方法
    saveState: debouncedSaveState, // 使用防抖版本的保存函数
    restoreState,
    clearState,
    resetState,
    
    // 日期处理方法
    buildDateRange,
    updateDateFromRange
  }
})

/**
 * 设备列表状态管理Store
 * 海聚科技钻井数据监控系统 - 设备列表搜索状态持久化
 */

import { defineStore } from 'pinia'
import { ref, reactive } from 'vue'

// ==================== 类型定义 ====================

/**
 * 设备列表筛选表单接口
 * 用于定义设备搜索和筛选的条件
 */
export interface FilterForm {
  /** 设备类型筛选 */
  type: string
  /** 设备名称搜索关键词 */
  name: string
  /** 设备序列号搜索关键词 */
  serialNumber: string
}

/**
 * 设备列表状态接口
 * 包含所有需要在组件中使用的状态
 */
export interface DeviceListState {
  /** 筛选表单状态 */
  filterForm: FilterForm
  /** 当前页码 */
  currentPage: number
  /** 每页显示数量 */
  pageSize: number
  /** 总记录数 */
  total: number
}

/**
 * 持久化状态数据结构
 * 用于序列化到sessionStorage的数据格式
 */
export interface PersistedState {
  /** 筛选表单数据 */
  filterForm: FilterForm
  /** 当前页码 */
  currentPage: number
  /** 每页显示数量 */
  pageSize: number
  /** 状态保存时间戳 */
  timestamp: number
}

/**
 * 设备列表Store接口
 * 定义Store暴露的所有状态和方法
 */
export interface DeviceListStore extends DeviceListState {
  /** 保存状态到sessionStorage */
  saveState: () => void
  /** 从sessionStorage恢复状态 */
  restoreState: () => boolean
  /** 清除保存的状态 */
  clearState: () => void
  /** 重置状态到默认值 */
  resetState: () => void
}

/**
 * 存储键生成函数类型
 */
type StorageKeyGenerator = (userId: string) => string

/**
 * 用户ID获取函数类型
 */
type UserIdGetter = () => string | null

export const useDeviceListStore = defineStore('deviceList', (): DeviceListStore => {
  // 筛选表单状态
  const filterForm = reactive<FilterForm>({
    type: '全部',
    name: '',
    serialNumber: ''
  })

  // 分页相关状态
  const currentPage = ref<number>(1)
  const pageSize = ref<number>(10)
  const total = ref<number>(0)

  // 状态时效性检查（1小时过期）
  const STATE_EXPIRY_TIME: number = 60 * 60 * 1000 // 1小时

  /**
   * 检测sessionStorage是否可用
   * @returns 是否可用
   */
  const isSessionStorageAvailable = (): boolean => {
    try {
      const testKey = '__test_session_storage__'
      sessionStorage.setItem(testKey, 'test')
      sessionStorage.removeItem(testKey)
      return true
    } catch (error) {
      console.warn('SessionStorage不可用:', error)
      return false
    }
  }

  /**
   * 验证状态数据格式
   * @param state 待验证的状态数据
   * @returns 是否格式正确
   */
  const validateStateData = (state: any): state is PersistedState => {
    if (!state || typeof state !== 'object') {
      return false
    }

    // 检查必需字段
    if (!state.filterForm || typeof state.filterForm !== 'object') {
      return false
    }

    if (typeof state.currentPage !== 'number' || state.currentPage < 1) {
      return false
    }

    if (typeof state.pageSize !== 'number' || state.pageSize < 1) {
      return false
    }

    if (typeof state.timestamp !== 'number' || state.timestamp <= 0) {
      return false
    }

    // 检查filterForm字段
    const { filterForm } = state
    if (typeof filterForm.type !== 'string' ||
        typeof filterForm.name !== 'string' ||
        typeof filterForm.serialNumber !== 'string') {
      return false
    }

    return true
  }

  /**
   * 获取当前用户ID
   * @returns 用户ID或null
   */
  const getCurrentUserId: UserIdGetter = (): string | null => {
    try {
      // 检查localStorage可用性
      if (typeof localStorage === 'undefined') {
        console.warn('LocalStorage不可用，无法获取用户ID')
        return null
      }

      const userInfo = localStorage.getItem('userInfo')
      if (!userInfo) {
        console.log('未找到用户信息')
        return null
      }

      let user: any
      try {
        user = JSON.parse(userInfo)
      } catch (parseError) {
        console.error('解析用户信息失败:', parseError)
        return null
      }

      if (!user || typeof user !== 'object') {
        console.error('用户信息格式错误:', user)
        return null
      }

      const userId = user.id
      if (!userId || (typeof userId !== 'string' && typeof userId !== 'number')) {
        console.error('用户ID格式错误:', userId)
        return null
      }

      return String(userId)
    } catch (error) {
      console.error('获取用户ID失败:', error)
      return null
    }
  }

  /**
   * 构建存储键
   * @param userId 用户ID
   * @returns 存储键
   */
  const getStorageKey: StorageKeyGenerator = (userId: string): string => {
    return `device_list_state_${userId}`
  }

  /**
   * 保存状态到sessionStorage
   */
  const saveState = (): void => {
    try {
      // 检查sessionStorage可用性
      if (!isSessionStorageAvailable()) {
        console.warn('SessionStorage不可用，无法保存设备列表状态')
        return
      }

      const userId = getCurrentUserId()
      if (!userId) {
        console.warn('无法保存设备列表状态：用户未登录')
        return
      }

      const storageKey = getStorageKey(userId)
      const state: PersistedState = {
        filterForm: { ...filterForm },
        currentPage: currentPage.value,
        pageSize: pageSize.value,
        timestamp: Date.now()
      }

      // 验证状态数据格式
      if (!validateStateData(state)) {
        console.error('状态数据格式错误，无法保存:', state)
        return
      }

      const stateJson = JSON.stringify(state)
      sessionStorage.setItem(storageKey, stateJson)

      // 验证保存结果
      const savedData = sessionStorage.getItem(storageKey)
      if (savedData !== stateJson) {
        console.error('状态保存验证失败，数据可能未正确保存')
        return
      }

      console.log('设备列表状态已保存', { userId, timestamp: state.timestamp })
    } catch (error) {
      console.error('保存设备列表状态失败:', error)

      // 尝试清理可能损坏的数据
      try {
        const userId = getCurrentUserId()
        if (userId) {
          const storageKey = getStorageKey(userId)
          sessionStorage.removeItem(storageKey)
          console.log('已清理可能损坏的状态数据')
        }
      } catch (cleanupError) {
        console.error('清理损坏数据失败:', cleanupError)
      }
    }
  }

  /**
   * 从sessionStorage恢复状态
   * @returns 是否成功恢复状态
   */
  const restoreState = (): boolean => {
    try {
      // 检查sessionStorage可用性
      if (!isSessionStorageAvailable()) {
        console.warn('SessionStorage不可用，无法恢复设备列表状态')
        return false
      }

      const userId = getCurrentUserId()
      if (!userId) {
        console.warn('无法恢复设备列表状态：用户未登录')
        return false
      }

      const storageKey = getStorageKey(userId)
      const savedState = sessionStorage.getItem(storageKey)

      if (!savedState) {
        console.log('没有找到保存的设备列表状态')
        return false
      }

      let state: PersistedState
      try {
        state = JSON.parse(savedState)
      } catch (parseError) {
        console.error('解析保存的状态数据失败:', parseError)
        sessionStorage.removeItem(storageKey)
        return false
      }

      // 验证状态数据格式
      if (!validateStateData(state)) {
        console.error('设备列表状态数据格式错误，清除无效数据')
        sessionStorage.removeItem(storageKey)
        return false
      }

      // 检查状态时效性
      if (Date.now() - state.timestamp > STATE_EXPIRY_TIME) {
        console.log('设备列表状态已过期，清除过期状态')
        sessionStorage.removeItem(storageKey)
        return false
      }

      // 恢复状态前进行备份（用于回滚）
      const backupState = {
        filterForm: { ...filterForm },
        currentPage: currentPage.value,
        pageSize: pageSize.value
      }

      try {
        // 恢复状态
        Object.assign(filterForm, state.filterForm)
        currentPage.value = state.currentPage
        pageSize.value = state.pageSize

        console.log('设备列表状态已恢复', {
          userId,
          timestamp: state.timestamp,
          filterForm: state.filterForm,
          currentPage: state.currentPage,
          pageSize: state.pageSize
        })
        return true
      } catch (restoreError) {
        console.error('恢复状态时发生错误，回滚到备份状态:', restoreError)

        // 回滚到备份状态
        Object.assign(filterForm, backupState.filterForm)
        currentPage.value = backupState.currentPage
        pageSize.value = backupState.pageSize

        // 清除损坏的状态数据
        sessionStorage.removeItem(storageKey)
        return false
      }
    } catch (error) {
      console.error('恢复设备列表状态失败:', error)

      // 清除可能损坏的状态数据
      try {
        const userId = getCurrentUserId()
        if (userId) {
          const storageKey = getStorageKey(userId)
          sessionStorage.removeItem(storageKey)
          console.log('已清除损坏的状态数据')
        }
      } catch (cleanupError) {
        console.error('清除损坏状态数据失败:', cleanupError)
      }
      return false
    }
  }

  /**
   * 清除保存的状态
   */
  const clearState = (): void => {
    try {
      // 检查sessionStorage可用性
      if (!isSessionStorageAvailable()) {
        console.warn('SessionStorage不可用，无法清除设备列表状态')
        return
      }

      const userId = getCurrentUserId()
      if (!userId) {
        console.warn('无法清除设备列表状态：用户未登录')
        return
      }

      const storageKey = getStorageKey(userId)

      // 检查状态是否存在
      const existingState = sessionStorage.getItem(storageKey)
      if (!existingState) {
        console.log('没有找到需要清除的设备列表状态')
        return
      }

      sessionStorage.removeItem(storageKey)

      // 验证清除结果
      const remainingState = sessionStorage.getItem(storageKey)
      if (remainingState) {
        console.error('清除设备列表状态失败，状态仍然存在:', remainingState)
        // 强制清除
        try {
          sessionStorage.removeItem(storageKey)
        } catch (forceError) {
          console.error('强制清除状态失败:', forceError)
        }
      } else {
        console.log('设备列表状态已清除', { userId })
      }
    } catch (error) {
      console.error('清除设备列表状态失败:', error)
    }
  }

  /**
   * 重置状态到默认值
   */
  const resetState = (): void => {
    filterForm.type = '全部'
    filterForm.name = ''
    filterForm.serialNumber = ''
    currentPage.value = 1
    pageSize.value = 10
    total.value = 0
  }

  return {
    // 状态
    filterForm,
    currentPage,
    pageSize,
    total,
    
    // 方法
    saveState,
    restoreState,
    clearState,
    resetState
  }
})



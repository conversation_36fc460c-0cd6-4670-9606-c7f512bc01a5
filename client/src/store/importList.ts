/**
 * 导入列表状态管理Store
 * 海聚科技钻井数据监控系统 - 导入列表搜索状态持久化
 * 
 * 基于通用状态持久化工具类实现，为导入列表页面提供
 * 搜索状态的自动保存和恢复功能。特别处理日期范围数组的持久化。
 */

import { defineStore } from 'pinia'
import { ref, reactive } from 'vue'
import { 
  createStatePersistence, 
  createDebouncedSaveState,
  type BaseListState,
  type StatePersistenceConfig,
  type StateValidator
} from '@/utils/listStatePersistence'

// ==================== 类型定义 ====================

/**
 * 导入列表筛选表单接口
 * 定义导入记录搜索和筛选的条件
 */
export interface ImportFilterForm {
  /** 文件名搜索关键词 */
  fileName: string
  /** 日期范围数组 [开始日期, 结束日期] (YYYY-MM-DD格式) */
  dateRange: string[]
}

/**
 * 导入列表状态接口
 * 包含所有需要在组件中使用的状态
 */
export interface ImportListState extends BaseListState<ImportFilterForm> {
  /** 筛选表单状态 */
  filterForm: ImportFilterForm
  /** 当前页码 */
  currentPage: number
  /** 每页显示数量 */
  pageSize: number
  /** 总记录数 */
  total: number
}

/**
 * 导入列表Store接口
 * 定义Store暴露的所有状态和方法
 */
export interface ImportListStore extends ImportListState {
  /** 保存状态到sessionStorage */
  saveState: () => void
  /** 从sessionStorage恢复状态 */
  restoreState: () => boolean
  /** 清除保存的状态 */
  clearState: () => void
  /** 重置状态到默认值 */
  resetState: () => void
}

// ==================== 日期验证工具函数 ====================

/**
 * 验证日期字符串格式是否正确
 * @param dateStr 日期字符串 (YYYY-MM-DD格式)
 * @returns 是否为有效日期格式
 */
export const isValidDateString = (dateStr: string): boolean => {
  if (!dateStr || typeof dateStr !== 'string') {
    return false
  }
  
  // 检查基本格式 YYYY-MM-DD
  const dateRegex = /^\d{4}-\d{2}-\d{2}$/
  if (!dateRegex.test(dateStr)) {
    return false
  }
  
  // 检查日期是否有效
  const date = new Date(dateStr)
  return !isNaN(date.getTime()) && dateStr === date.toISOString().split('T')[0]
}

/**
 * 验证日期范围数组格式
 * @param dateRange 日期范围数组
 * @returns 是否为有效的日期范围数组
 */
export const validateDateRange = (dateRange: any): boolean => {
  // 空数组是有效的（表示未选择日期范围）
  if (!dateRange || (Array.isArray(dateRange) && dateRange.length === 0)) {
    return true
  }
  
  // 必须是数组且长度为2
  if (!Array.isArray(dateRange) || dateRange.length !== 2) {
    return false
  }
  
  // 检查数组元素是否为有效日期字符串
  if (!isValidDateString(dateRange[0]) || !isValidDateString(dateRange[1])) {
    return false
  }
  
  // 检查日期逻辑（开始日期不能晚于结束日期）
  const startDate = new Date(dateRange[0])
  const endDate = new Date(dateRange[1])
  if (startDate > endDate) {
    console.warn('开始日期不能晚于结束日期，将重置日期范围')
    return false
  }
  
  return true
}

/**
 * 检查数组元素是否为有效日期
 * @param arr 待检查的数组
 * @returns 是否为有效的日期数组
 */
export const isValidDateArray = (arr: any): boolean => {
  if (!Array.isArray(arr)) {
    return false
  }
  
  // 空数组是有效的
  if (arr.length === 0) {
    return true
  }
  
  // 检查每个元素是否为有效日期字符串
  return arr.every(item => isValidDateString(item))
}

// ==================== 验证器函数 ====================

/**
 * 导入列表筛选表单验证器
 * 验证筛选表单数据的格式是否正确，特别验证日期范围数组
 */
const validateImportFilterForm: StateValidator<ImportFilterForm> = (filterForm: any): filterForm is ImportFilterForm => {
  if (!filterForm || typeof filterForm !== 'object') {
    return false
  }

  // 检查必需字段类型
  if (typeof filterForm.fileName !== 'string') {
    return false
  }

  // 验证日期范围数组
  if (!validateDateRange(filterForm.dateRange)) {
    console.warn('无效的日期范围数组，将重置日期范围')
    return false
  }

  return true
}

// ==================== 状态持久化配置 ====================

/**
 * 状态持久化配置
 */
const persistenceConfig: StatePersistenceConfig<ImportFilterForm> = {
  storageKeyPrefix: 'import_list_state',
  defaultFilterForm: {
    fileName: '',
    dateRange: []
  },
  validateFilterForm: validateImportFilterForm,
  expiryTime: 60 * 60 * 1000 // 1小时过期
}

// ==================== Store定义 ====================

/**
 * 导入列表状态管理Store
 * 使用Pinia defineStore创建，提供响应式状态管理和持久化功能
 */
export const useImportListStore = defineStore('importList', (): ImportListStore => {
  // ==================== 响应式状态 ====================
  
  /**
   * 筛选表单状态
   * 使用reactive创建响应式对象，支持深度监听
   */
  const filterForm = reactive<ImportFilterForm>({
    fileName: '',
    dateRange: []
  })

  /**
   * 分页相关状态
   * 使用ref创建响应式引用
   */
  const currentPage = ref<number>(1)
  const pageSize = ref<number>(10)
  const total = ref<number>(0)

  // ==================== 状态持久化管理器 ====================
  
  /**
   * 创建状态持久化管理器实例
   */
  const persistence = createStatePersistence(persistenceConfig)

  /**
   * 创建防抖状态保存函数
   * 避免频繁的状态保存操作，提高性能
   */
  const debouncedSaveState = createDebouncedSaveState(
    persistence,
    () => ({
      filterForm: { ...filterForm },
      currentPage: currentPage.value,
      pageSize: pageSize.value
    }),
    300 // 300ms防抖延迟
  )

  // ==================== 状态管理方法 ====================

  /**
   * 保存状态到sessionStorage
   * 将当前的筛选条件和分页状态保存到sessionStorage
   */
  const saveState = (): void => {
    const currentState = {
      filterForm: { ...filterForm },
      currentPage: currentPage.value,
      pageSize: pageSize.value
    }
    persistence.saveState(currentState)
  }

  /**
   * 从sessionStorage恢复状态
   * 从sessionStorage中恢复之前保存的搜索状态，特别处理日期范围数组
   * @returns 是否成功恢复状态
   */
  const restoreState = (): boolean => {
    try {
      const restoredState = persistence.restoreState()
      
      if (!restoredState) {
        return false
      }

      // 恢复状态前进行备份（用于回滚）
      const backupState = {
        filterForm: { ...filterForm },
        currentPage: currentPage.value,
        pageSize: pageSize.value,
        total: total.value
      }

      try {
        // 特殊处理：验证日期范围数组的有效性
        let restoredDateRange = restoredState.filterForm.dateRange
        if (!validateDateRange(restoredDateRange)) {
          console.warn('恢复的日期范围无效，重置为空数组')
          restoredDateRange = []
        }

        // 恢复筛选表单状态
        filterForm.fileName = restoredState.filterForm.fileName
        filterForm.dateRange = [...restoredDateRange] // 创建新数组避免引用问题
        
        // 恢复分页状态
        currentPage.value = restoredState.currentPage
        pageSize.value = restoredState.pageSize
        // 注意：total不从持久化状态恢复，因为数据可能已变化

        console.log('已恢复导入列表搜索状态', {
          filterForm: {
            fileName: restoredState.filterForm.fileName,
            dateRange: restoredDateRange
          },
          currentPage: restoredState.currentPage,
          pageSize: restoredState.pageSize
        })
        
        return true
      } catch (restoreError) {
        console.error('恢复导入列表状态时发生错误，回滚到备份状态:', restoreError)
        
        // 回滚到备份状态
        Object.assign(filterForm, backupState.filterForm)
        currentPage.value = backupState.currentPage
        pageSize.value = backupState.pageSize
        total.value = backupState.total
        
        // 清除损坏的状态数据
        persistence.clearState()
        return false
      }
    } catch (error) {
      console.error('恢复导入列表状态失败:', error)
      return false
    }
  }

  /**
   * 清除保存的状态
   * 从sessionStorage中清除保存的搜索状态
   */
  const clearState = (): void => {
    persistence.clearState()
  }

  /**
   * 重置状态到默认值
   * 将所有状态重置为初始默认值
   */
  const resetState = (): void => {
    // 重置筛选表单
    filterForm.fileName = ''
    filterForm.dateRange = []

    // 重置分页状态
    currentPage.value = 1
    pageSize.value = 10
    total.value = 0

    // 立即保存重置后的状态
    const resetStateData = {
      filterForm: { ...filterForm },
      currentPage: currentPage.value,
      pageSize: pageSize.value
    }
    persistence.saveState(resetStateData)

    console.log('导入列表状态已重置为默认值')
  }

  // ==================== 返回Store接口 ====================
  
  return {
    // 响应式状态
    filterForm,
    currentPage,
    pageSize,
    total,
    
    // 状态管理方法
    saveState: debouncedSaveState, // 使用防抖版本的保存函数
    restoreState,
    clearState,
    resetState
  }
})

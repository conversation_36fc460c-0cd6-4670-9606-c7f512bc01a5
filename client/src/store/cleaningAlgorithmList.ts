/**
 * 清洗算法列表状态管理Store
 * 海聚科技钻井数据监控系统 - 清洗算法列表搜索状态持久化
 * 
 * 基于通用状态持久化工具类实现，为清洗算法列表页面提供
 * 搜索状态的自动保存和恢复功能。
 */

import { defineStore } from 'pinia'
import { ref, reactive } from 'vue'
import { 
  createStatePersistence, 
  createDebouncedSaveState,
  createStringFieldsValidator,
  type BaseListState,
  type StatePersistenceConfig
} from '@/utils/listStatePersistence'

// ==================== 类型定义 ====================

/**
 * 清洗算法筛选表单接口
 * 定义清洗算法搜索和筛选的条件
 */
export interface CleaningAlgorithmFilterForm {
  /** 算法名称搜索关键词 */
  name: string
  /** 算法状态筛选 */
  status: string
}

/**
 * 清洗算法列表状态接口
 * 包含所有需要在组件中使用的状态
 */
export interface CleaningAlgorithmListState extends BaseListState<CleaningAlgorithmFilterForm> {
  /** 筛选表单状态 */
  filterForm: CleaningAlgorithmFilterForm
  /** 当前页码 */
  currentPage: number
  /** 每页显示数量 */
  pageSize: number
  /** 总记录数 */
  total: number
}

/**
 * 清洗算法列表Store接口
 * 定义Store暴露的所有状态和方法
 */
export interface CleaningAlgorithmListStore extends CleaningAlgorithmListState {
  /** 保存状态到sessionStorage */
  saveState: () => void
  /** 从sessionStorage恢复状态 */
  restoreState: () => boolean
  /** 清除保存的状态 */
  clearState: () => void
  /** 重置状态到默认值 */
  resetState: () => void
}

// ==================== 状态持久化配置 ====================

/**
 * 清洗算法筛选表单验证器
 * 验证筛选表单数据的格式是否正确
 */
const validateCleaningAlgorithmFilterForm = createStringFieldsValidator<CleaningAlgorithmFilterForm>(
  ['name', 'status']
)

/**
 * 状态持久化配置
 */
const persistenceConfig: StatePersistenceConfig<CleaningAlgorithmFilterForm> = {
  storageKeyPrefix: 'cleaning_algorithm_list_state',
  defaultFilterForm: {
    name: '',
    status: ''
  },
  validateFilterForm: validateCleaningAlgorithmFilterForm,
  expiryTime: 60 * 60 * 1000 // 1小时过期
}

// ==================== Store定义 ====================

/**
 * 清洗算法列表状态管理Store
 * 使用Pinia defineStore创建，提供响应式状态管理和持久化功能
 */
export const useCleaningAlgorithmListStore = defineStore('cleaningAlgorithmList', (): CleaningAlgorithmListStore => {
  // ==================== 响应式状态 ====================
  
  /**
   * 筛选表单状态
   * 使用reactive创建响应式对象，支持深度监听
   */
  const filterForm = reactive<CleaningAlgorithmFilterForm>({
    name: '',
    status: ''
  })

  /**
   * 分页相关状态
   * 使用ref创建响应式引用
   */
  const currentPage = ref<number>(1)
  const pageSize = ref<number>(10)
  const total = ref<number>(0)

  // ==================== 状态持久化管理器 ====================
  
  /**
   * 创建状态持久化管理器实例
   */
  const persistence = createStatePersistence(persistenceConfig)

  /**
   * 创建防抖状态保存函数
   * 避免频繁的状态保存操作，提高性能
   */
  const debouncedSaveState = createDebouncedSaveState(
    persistence,
    () => ({
      filterForm: { ...filterForm },
      currentPage: currentPage.value,
      pageSize: pageSize.value
    }),
    300 // 300ms防抖延迟
  )

  // ==================== 状态管理方法 ====================

  /**
   * 保存状态到sessionStorage
   * 将当前的筛选条件和分页状态保存到sessionStorage
   */
  const saveState = (): void => {
    const currentState = {
      filterForm: { ...filterForm },
      currentPage: currentPage.value,
      pageSize: pageSize.value
    }
    persistence.saveState(currentState)
  }

  /**
   * 从sessionStorage恢复状态
   * 从sessionStorage中恢复之前保存的搜索状态
   * @returns 是否成功恢复状态
   */
  const restoreState = (): boolean => {
    try {
      const restoredState = persistence.restoreState()
      
      if (!restoredState) {
        return false
      }

      // 恢复状态前进行备份（用于回滚）
      const backupState = {
        filterForm: { ...filterForm },
        currentPage: currentPage.value,
        pageSize: pageSize.value,
        total: total.value
      }

      try {
        // 恢复筛选表单状态
        Object.assign(filterForm, restoredState.filterForm)
        
        // 恢复分页状态
        currentPage.value = restoredState.currentPage
        pageSize.value = restoredState.pageSize
        // 注意：total不从持久化状态恢复，因为数据可能已变化

        console.log('已恢复清洗算法列表搜索状态', {
          filterForm: restoredState.filterForm,
          currentPage: restoredState.currentPage,
          pageSize: restoredState.pageSize
        })
        
        return true
      } catch (restoreError) {
        console.error('恢复清洗算法列表状态时发生错误，回滚到备份状态:', restoreError)
        
        // 回滚到备份状态
        Object.assign(filterForm, backupState.filterForm)
        currentPage.value = backupState.currentPage
        pageSize.value = backupState.pageSize
        total.value = backupState.total
        
        // 清除损坏的状态数据
        persistence.clearState()
        return false
      }
    } catch (error) {
      console.error('恢复清洗算法列表状态失败:', error)
      return false
    }
  }

  /**
   * 清除保存的状态
   * 从sessionStorage中清除保存的搜索状态
   */
  const clearState = (): void => {
    persistence.clearState()
  }

  /**
   * 重置状态到默认值
   * 将所有状态重置为初始默认值
   */
  const resetState = (): void => {
    // 重置筛选表单
    filterForm.name = ''
    filterForm.status = ''

    // 重置分页状态
    currentPage.value = 1
    pageSize.value = 10
    total.value = 0

    // 立即保存重置后的状态
    const resetStateData = {
      filterForm: { ...filterForm },
      currentPage: currentPage.value,
      pageSize: pageSize.value
    }
    persistence.saveState(resetStateData)

    console.log('清洗算法列表状态已重置为默认值')
  }

  // ==================== 返回Store接口 ====================
  
  return {
    // 响应式状态
    filterForm,
    currentPage,
    pageSize,
    total,
    
    // 状态管理方法
    saveState: debouncedSaveState, // 使用防抖版本的保存函数
    restoreState,
    clearState,
    resetState
  }
})

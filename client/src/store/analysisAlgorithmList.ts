/**
 * 控件算法列表状态管理Store
 * 海聚科技钻井数据监控系统 - 控件算法列表搜索状态持久化
 * 
 * 基于通用状态持久化工具类实现，为控件算法列表页面提供
 * 搜索状态的自动保存和恢复功能。特别处理设备类型的默认值逻辑。
 */

import { defineStore } from 'pinia'
import { ref, reactive, type Ref } from 'vue'
import { 
  createStatePersistence, 
  createDebouncedSaveState,
  type BaseListState,
  type StatePersistenceConfig,
  type StateValidator
} from '@/utils/listStatePersistence'

// ==================== 类型定义 ====================

/**
 * 控件算法筛选表单接口
 * 定义控件算法搜索和筛选的条件
 */
export interface AnalysisAlgorithmFilterForm {
  /** 控件类型：PDF(超前钻机)/WPD(水锤)/AD(锚杆钻) */
  type: string
  /** 算法名称搜索关键词 */
  name: string
}

/**
 * 控件算法列表状态接口
 * 包含所有需要在组件中使用的状态
 */
export interface AnalysisAlgorithmListState extends BaseListState<AnalysisAlgorithmFilterForm> {
  /** 筛选表单状态 */
  filterForm: AnalysisAlgorithmFilterForm
  /** 当前页码 */
  currentPage: number
  /** 每页显示数量 */
  pageSize: number
  /** 总记录数 */
  total: number
}

/**
 * 控件算法列表Store接口
 * 定义Store暴露的所有状态和方法
 */
export interface AnalysisAlgorithmListStore {
  /** 筛选表单状态 */
  filterForm: AnalysisAlgorithmFilterForm
  /** 当前页码 */
  currentPage: Ref<number>
  /** 每页显示数量 */
  pageSize: Ref<number>
  /** 总记录数 */
  total: Ref<number>
  /** 保存状态到sessionStorage */
  saveState: () => void
  /** 从sessionStorage恢复状态 */
  restoreState: () => boolean
  /** 清除保存的状态 */
  clearState: () => void
  /** 重置状态到默认值 */
  resetState: () => void
}

// ==================== 常量定义 ====================

/**
 * 有效的设备类型列表
 */
export const VALID_DEVICE_TYPES = ['全部', 'PDF', 'WPD', 'AD'] as const

/**
 * 默认设备类型
 */
export const DEFAULT_DEVICE_TYPE = '全部'

// ==================== 验证器函数 ====================

/**
 * 验证设备类型是否有效
 * @param type 设备类型
 * @returns 是否有效
 */
export const validateDeviceType = (type: string): boolean => {
  return VALID_DEVICE_TYPES.includes(type as any)
}

/**
 * 控件算法筛选表单验证器
 * 验证筛选表单数据的格式是否正确，特别验证设备类型的有效性
 */
const validateAnalysisAlgorithmFilterForm: StateValidator<AnalysisAlgorithmFilterForm> = (filterForm: any): filterForm is AnalysisAlgorithmFilterForm => {
  if (!filterForm || typeof filterForm !== 'object') {
    return false
  }

  // 检查必需字段
  if (typeof filterForm.name !== 'string') {
    return false
  }

  if (typeof filterForm.type !== 'string') {
    return false
  }

  // 验证设备类型的有效性
  if (!validateDeviceType(filterForm.type)) {
    console.warn(`无效的设备类型: ${filterForm.type}，将重置为默认值: ${DEFAULT_DEVICE_TYPE}`)
    return false
  }

  return true
}

// ==================== 状态持久化配置 ====================

/**
 * 状态持久化配置
 */
const persistenceConfig: StatePersistenceConfig<AnalysisAlgorithmFilterForm> = {
  storageKeyPrefix: 'analysis_algorithm_list_state',
  defaultFilterForm: {
    type: DEFAULT_DEVICE_TYPE,
    name: ''
  },
  validateFilterForm: validateAnalysisAlgorithmFilterForm,
  expiryTime: 60 * 60 * 1000 // 1小时过期
}

// ==================== Store定义 ====================

/**
 * 控件算法列表状态管理Store
 * 使用Pinia defineStore创建，提供响应式状态管理和持久化功能
 */
export const useAnalysisAlgorithmListStore = defineStore('analysisAlgorithmList', (): AnalysisAlgorithmListStore => {
  // ==================== 响应式状态 ====================
  
  /**
   * 筛选表单状态
   * 使用reactive创建响应式对象，支持深度监听
   */
  const filterForm = reactive<AnalysisAlgorithmFilterForm>({
    type: DEFAULT_DEVICE_TYPE,
    name: ''
  })

  /**
   * 分页相关状态
   * 使用ref创建响应式引用
   */
  const currentPage = ref<number>(1)
  const pageSize = ref<number>(10)
  const total = ref<number>(0)

  // ==================== 状态持久化管理器 ====================
  
  /**
   * 创建状态持久化管理器实例
   */
  const persistence = createStatePersistence(persistenceConfig)

  /**
   * 创建防抖状态保存函数
   * 避免频繁的状态保存操作，提高性能
   */
  const debouncedSaveState = createDebouncedSaveState(
    persistence,
    () => ({
      filterForm: { ...filterForm },
      currentPage: currentPage.value,
      pageSize: pageSize.value
    }),
    300 // 300ms防抖延迟
  )

  // ==================== 状态管理方法 ====================

  /**
   * 保存状态到sessionStorage
   * 将当前的筛选条件和分页状态保存到sessionStorage
   */
  const saveState = (): void => {
    const currentState = {
      filterForm: { ...filterForm },
      currentPage: currentPage.value,
      pageSize: pageSize.value
    }
    persistence.saveState(currentState)
  }

  /**
   * 从sessionStorage恢复状态
   * 从sessionStorage中恢复之前保存的搜索状态，特别处理设备类型验证
   * @returns 是否成功恢复状态
   */
  const restoreState = (): boolean => {
    try {
      const restoredState = persistence.restoreState()
      
      if (!restoredState) {
        return false
      }

      // 恢复状态前进行备份（用于回滚）
      const backupState = {
        filterForm: { ...filterForm },
        currentPage: currentPage.value,
        pageSize: pageSize.value,
        total: total.value
      }

      try {
        // 特殊处理：验证设备类型的有效性
        let restoredType = restoredState.filterForm.type
        if (!validateDeviceType(restoredType)) {
          console.warn(`恢复的设备类型无效: ${restoredType}，重置为默认值: ${DEFAULT_DEVICE_TYPE}`)
          restoredType = DEFAULT_DEVICE_TYPE
        }

        // 恢复筛选表单状态
        filterForm.type = restoredType
        filterForm.name = restoredState.filterForm.name
        
        // 恢复分页状态
        currentPage.value = restoredState.currentPage
        pageSize.value = restoredState.pageSize
        // 注意：total不从持久化状态恢复，因为数据可能已变化

        console.log('已恢复控件算法列表搜索状态', {
          filterForm: {
            type: restoredType,
            name: restoredState.filterForm.name
          },
          currentPage: restoredState.currentPage,
          pageSize: restoredState.pageSize
        })
        
        return true
      } catch (restoreError) {
        console.error('恢复控件算法列表状态时发生错误，回滚到备份状态:', restoreError)
        
        // 回滚到备份状态
        Object.assign(filterForm, backupState.filterForm)
        currentPage.value = backupState.currentPage
        pageSize.value = backupState.pageSize
        total.value = backupState.total
        
        // 清除损坏的状态数据
        persistence.clearState()
        return false
      }
    } catch (error) {
      console.error('恢复控件算法列表状态失败:', error)
      return false
    }
  }

  /**
   * 清除保存的状态
   * 从sessionStorage中清除保存的搜索状态
   */
  const clearState = (): void => {
    persistence.clearState()
  }

  /**
   * 重置状态到默认值
   * 将所有状态重置为初始默认值，保持设备类型为PDF
   */
  const resetState = (): void => {
    // 重置筛选表单
    filterForm.type = DEFAULT_DEVICE_TYPE
    filterForm.name = ''

    // 重置分页状态
    currentPage.value = 1
    pageSize.value = 10
    total.value = 0

    // 立即保存重置后的状态
    const resetStateData = {
      filterForm: { ...filterForm },
      currentPage: currentPage.value,
      pageSize: pageSize.value
    }
    persistence.saveState(resetStateData)

    console.log('控件算法列表状态已重置为默认值', {
      type: DEFAULT_DEVICE_TYPE,
      name: ''
    })
  }

  // ==================== 返回Store接口 ====================
  
  return {
    // 响应式状态
    filterForm,
    currentPage,
    pageSize,
    total,
    
    // 状态管理方法
    saveState: debouncedSaveState, // 使用防抖版本的保存函数
    restoreState,
    clearState,
    resetState
  }
})

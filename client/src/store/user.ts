import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { login, logout, dingTalkLogin } from '@/api'
import { ElMessage } from 'element-plus'
import type { UserInfo } from '@/types/api'

// 用户Store类型定义
interface UserStore {
  userInfo: UserInfo
  loginAction: (username: string, password: string) => Promise<boolean>
  logoutAction: () => Promise<boolean>
  clearLocalData: () => void
  dingTalkLoginAction: (code: string) => Promise<boolean>
  isLoggedIn: boolean
}

export const useUserStore = defineStore('user', () => {
  // 移除token的本地存储，改为依赖HttpOnly Cookie
  const userInfo = ref<UserInfo>(JSON.parse(localStorage.getItem('userInfo') || '{}'))

  // 计算属性：是否已登录
  const isLoggedIn = computed(() => {
    return !!(userInfo.value && userInfo.value.id)
  })

  // 登录
  const loginAction = async (username: string, password: string): Promise<boolean> => {
    try {
      const res = await login({ username, password })
      // 不再存储token，只存储用户信息
      userInfo.value = res.user
      localStorage.setItem('userInfo', JSON.stringify(res.user))
      ElMessage.success('登录成功')
      return true
    } catch (error) {
      const errorMessage = error.message || '登录失败'
      ElMessage.error(errorMessage)
      return false
    }
  }

  // 登出
  const logoutAction = async (): Promise<boolean> => {
    try {
      // 先调用登出接口（需要token验证）
      await logout()

      // 接口调用成功后再清除本地数据
      clearLocalData()

      ElMessage.success('登出成功')
      return true
    } catch (error) {
      console.error('登出失败:', error)
      // 即使接口调用失败，也要清除本地数据，避免用户无法重新登录
      clearLocalData()
      const errorMessage = error instanceof Error ? error.message : '登出失败'
      ElMessage.error(errorMessage)
      return false
    }
  }

  // 清除本地数据
  const clearLocalData = (): void => {
    // 清除localStorage中的用户信息
    localStorage.removeItem('userInfo')

    // 清空ref值
    userInfo.value = {} as UserInfo

    // 验证清除结果
    const remainingUserInfo = localStorage.getItem('userInfo')

    if (remainingUserInfo) {
      console.error('清除本地数据失败:', {
        userInfo: remainingUserInfo
      })
      // 强制清除
      localStorage.removeItem('userInfo')
    }
  }

  // 钉钉登录
  const dingTalkLoginAction = async (code: string): Promise<boolean> => {
    try {
      const res = await dingTalkLogin(code)
      // 不再存储token，只存储用户信息
      userInfo.value = res.user
      localStorage.setItem('userInfo', JSON.stringify(res.user))
      ElMessage.success('钉钉登录成功')
      return true
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '钉钉登录失败'
      ElMessage.error(errorMessage)
      return false
    }
  }

  return {
    userInfo,
    isLoggedIn,
    loginAction,
    logoutAction,
    clearLocalData,
    dingTalkLoginAction
  }
})

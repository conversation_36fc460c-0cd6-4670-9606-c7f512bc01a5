/**
 * 模版列表状态管理Store
 * 海聚科技钻井数据监控系统 - 模版列表搜索状态持久化
 * 
 * 基于通用状态持久化工具类实现，为模版列表页面提供
 * 搜索状态的自动保存和恢复功能。考虑瀑布流布局的特殊需求。
 */

import { defineStore } from 'pinia'
import { ref, reactive } from 'vue'
import { 
  createStatePersistence, 
  createDebouncedSaveState,
  type BaseListState,
  type StatePersistenceConfig,
  type StateValidator
} from '@/utils/listStatePersistence'

// ==================== 类型定义 ====================

/**
 * 模版列表筛选表单接口
 * 定义模版搜索和筛选的条件
 */
export interface TemplateFilterForm {
  /** 搜索关键词 */
  searchKeyword: string
  /** 状态筛选：''(全部)、'1'(启用)、'0'(禁用) */
  statusFilter: string
}

/**
 * 模版列表状态接口
 * 包含所有需要在组件中使用的状态
 */
export interface TemplateListState extends BaseListState<TemplateFilterForm> {
  /** 筛选表单状态 */
  filterForm: TemplateFilterForm
  /** 当前页码 */
  currentPage: number
  /** 每页显示数量 */
  pageSize: number
  /** 总记录数 */
  total: number
}

/**
 * 模版列表Store接口
 * 定义Store暴露的所有状态和方法
 */
export interface TemplateListStore extends TemplateListState {
  /** 瀑布流布局触发器 */
  layoutTrigger: number
  /** 保存状态到sessionStorage */
  saveState: () => void
  /** 从sessionStorage恢复状态 */
  restoreState: () => boolean
  /** 清除保存的状态 */
  clearState: () => void
  /** 重置状态到默认值 */
  resetState: () => void
  /** 触发瀑布流布局重新计算 */
  triggerLayout: () => void
}

// ==================== 常量定义 ====================

/**
 * 有效的状态筛选值列表
 */
export const VALID_STATUS_FILTERS = ['', '全部', '1', '0'] as const

/**
 * 默认状态筛选值
 */
export const DEFAULT_STATUS_FILTER = '全部'

// ==================== 验证器函数 ====================

/**
 * 验证状态筛选值是否有效
 * @param statusFilter 状态筛选值
 * @returns 是否有效
 */
export const validateStatusFilter = (statusFilter: string): boolean => {
  return VALID_STATUS_FILTERS.includes(statusFilter as any)
}

/**
 * 模版列表筛选表单验证器
 * 验证筛选表单数据的格式是否正确，特别验证状态筛选值的有效性
 */
const validateTemplateFilterForm: StateValidator<TemplateFilterForm> = (filterForm: any): filterForm is TemplateFilterForm => {
  if (!filterForm || typeof filterForm !== 'object') {
    return false
  }

  // 检查必需字段类型
  if (typeof filterForm.searchKeyword !== 'string') {
    return false
  }

  if (typeof filterForm.statusFilter !== 'string') {
    return false
  }

  // 验证状态筛选值的有效性
  if (!validateStatusFilter(filterForm.statusFilter)) {
    console.warn(`无效的状态筛选值: ${filterForm.statusFilter}，将重置为默认值: ${DEFAULT_STATUS_FILTER}`)
    return false
  }

  return true
}

// ==================== 状态持久化配置 ====================

/**
 * 状态持久化配置
 */
const persistenceConfig: StatePersistenceConfig<TemplateFilterForm> = {
  storageKeyPrefix: 'template_list_state',
  defaultFilterForm: {
    searchKeyword: '',
    statusFilter: DEFAULT_STATUS_FILTER
  },
  validateFilterForm: validateTemplateFilterForm,
  expiryTime: 60 * 60 * 1000 // 1小时过期
}

// ==================== Store定义 ====================

/**
 * 模版列表状态管理Store
 * 使用Pinia defineStore创建，提供响应式状态管理和持久化功能
 */
export const useTemplateListStore = defineStore('templateList', (): TemplateListStore => {
  // ==================== 响应式状态 ====================
  
  /**
   * 筛选表单状态
   * 使用reactive创建响应式对象，支持深度监听
   */
  const filterForm = reactive<TemplateFilterForm>({
    searchKeyword: '',
    statusFilter: DEFAULT_STATUS_FILTER
  })

  /**
   * 分页相关状态
   * 使用ref创建响应式引用
   */
  const currentPage = ref<number>(1)
  const pageSize = ref<number>(10)
  const total = ref<number>(0)

  /**
   * 瀑布流布局触发器
   * 用于触发瀑布流布局重新计算
   */
  const layoutTrigger = ref<number>(0)

  // ==================== 状态持久化管理器 ====================
  
  /**
   * 创建状态持久化管理器实例
   */
  const persistence = createStatePersistence(persistenceConfig)

  /**
   * 创建防抖状态保存函数
   * 避免频繁的状态保存操作，提高性能
   */
  const debouncedSaveState = createDebouncedSaveState(
    persistence,
    () => ({
      filterForm: { ...filterForm },
      currentPage: currentPage.value,
      pageSize: pageSize.value
    }),
    300 // 300ms防抖延迟
  )

  // ==================== 瀑布流布局方法 ====================

  /**
   * 触发瀑布流布局重新计算
   * 通过改变layoutTrigger的值来通知组件重新计算布局
   */
  const triggerLayout = (): void => {
    layoutTrigger.value += 1
    console.log('已触发瀑布流布局重新计算', { trigger: layoutTrigger.value })
  }

  // ==================== 状态管理方法 ====================

  /**
   * 保存状态到sessionStorage
   * 将当前的筛选条件和分页状态保存到sessionStorage
   */
  const saveState = (): void => {
    const currentState = {
      filterForm: { ...filterForm },
      currentPage: currentPage.value,
      pageSize: pageSize.value
    }
    persistence.saveState(currentState)
  }

  /**
   * 从sessionStorage恢复状态
   * 从sessionStorage中恢复之前保存的搜索状态，特别处理状态筛选值验证
   * @returns 是否成功恢复状态
   */
  const restoreState = (): boolean => {
    try {
      const restoredState = persistence.restoreState()
      
      if (!restoredState) {
        return false
      }

      // 恢复状态前进行备份（用于回滚）
      const backupState = {
        filterForm: { ...filterForm },
        currentPage: currentPage.value,
        pageSize: pageSize.value,
        total: total.value
      }

      try {
        // 特殊处理：验证状态筛选值的有效性
        let restoredStatusFilter = restoredState.filterForm.statusFilter
        if (!validateStatusFilter(restoredStatusFilter)) {
          console.warn(`恢复的状态筛选值无效: ${restoredStatusFilter}，重置为默认值: ${DEFAULT_STATUS_FILTER}`)
          restoredStatusFilter = DEFAULT_STATUS_FILTER
        }

        // 恢复筛选表单状态
        filterForm.searchKeyword = restoredState.filterForm.searchKeyword
        filterForm.statusFilter = restoredStatusFilter
        
        // 恢复分页状态
        currentPage.value = restoredState.currentPage
        pageSize.value = restoredState.pageSize
        // 注意：total不从持久化状态恢复，因为数据可能已变化

        console.log('已恢复模版列表搜索状态', {
          filterForm: {
            searchKeyword: restoredState.filterForm.searchKeyword,
            statusFilter: restoredStatusFilter
          },
          currentPage: restoredState.currentPage,
          pageSize: restoredState.pageSize
        })
        
        // 状态恢复后触发瀑布流布局重新计算
        // 延迟触发，确保组件已经渲染完成
        setTimeout(() => {
          triggerLayout()
        }, 100)
        
        return true
      } catch (restoreError) {
        console.error('恢复模版列表状态时发生错误，回滚到备份状态:', restoreError)
        
        // 回滚到备份状态
        Object.assign(filterForm, backupState.filterForm)
        currentPage.value = backupState.currentPage
        pageSize.value = backupState.pageSize
        total.value = backupState.total
        
        // 清除损坏的状态数据
        persistence.clearState()
        return false
      }
    } catch (error) {
      console.error('恢复模版列表状态失败:', error)
      return false
    }
  }

  /**
   * 清除保存的状态
   * 从sessionStorage中清除保存的搜索状态
   */
  const clearState = (): void => {
    persistence.clearState()
  }

  /**
   * 重置状态到默认值
   * 将所有状态重置为初始默认值
   */
  const resetState = (): void => {
    // 重置筛选表单
    filterForm.searchKeyword = ''
    filterForm.statusFilter = DEFAULT_STATUS_FILTER

    // 重置分页状态
    currentPage.value = 1
    pageSize.value = 10
    total.value = 0

    // 立即保存重置后的状态
    const resetStateData = {
      filterForm: { ...filterForm },
      currentPage: currentPage.value,
      pageSize: pageSize.value
    }
    persistence.saveState(resetStateData)

    console.log('模版列表状态已重置为默认值')
  }

  // ==================== 返回Store接口 ====================
  
  return {
    // 响应式状态
    filterForm,
    currentPage,
    pageSize,
    total,
    layoutTrigger,
    
    // 状态管理方法
    saveState: debouncedSaveState, // 使用防抖版本的保存函数
    restoreState,
    clearState,
    resetState,
    
    // 瀑布流布局方法
    triggerLayout
  }
})

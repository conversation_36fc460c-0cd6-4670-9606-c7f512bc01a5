import { createApp, type App } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'

import AppComponent from './App.vue'
import router from './router'
import { initTokenCheck } from './utils/request'

import './styles/index.css'

// 创建Vue应用实例
const app: App = createApp(AppComponent)

// 注册所有Element Plus图标组件
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 配置应用插件
app.use(createPinia())
app.use(router)
app.use(ElementPlus, {
  locale: zhCn
})

// 初始化Token自动检查机制
initTokenCheck()

// 挂载应用到DOM
app.mount('#app')

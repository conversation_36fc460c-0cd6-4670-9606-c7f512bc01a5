<template>
  <div class="webgl-container">
    <div class="canvas-container">
      <canvas ref="glCanvas" />
      <div class="canvas-overlay">
        <div class="mode-indicator">
          {{ interactionMode === 'vertical' ? '垂直交互模式' : '漫游交互模式' }}
        </div>
        
        <!-- 深度标记 -->
        <div 
          v-for="(position, index) in markerScreenPositions" 
          :key="`marker-${index}`"
          class="depth-marker"
          :style="{
            left: position.x + 'px',
            top: position.y + 'px',
            transform: 'translate(-50%, -50%)',
            opacity: position.visible ? 1 : 0
          }"
        >
          <div class="depth-text">
            {{ Math.round(position.depth) }}cm --
          </div>
        </div>
      </div>
    </div>
    <div class="control-panel">
      <h2 class="panel-title">
        数字岩芯控制面板
      </h2>
      
      <el-form label-position="top">
        <div class="params-row">
          <el-form-item
            label="柱体高度"
            class="param-item"
          >
            <el-input-number
              v-model="height"
              :min="0.1"
              :step="0.1"
              controls-position="right"
            />
          </el-form-item>
          <el-form-item
            label="柱体直径"
            class="param-item"
          >
            <el-input-number
              v-model="diameter"
              :min="0.1"
              :step="0.1"
              controls-position="right"
            />
          </el-form-item>
        </div>
        <div class="params-row">
          <el-form-item
            label="渲染点数量"
            class="param-item"
          >
            <el-input-number 
              v-model="localRenderCount" 
              :min="1" 
              :max="maxRenderCount"
              :step="1"
              controls-position="right"
              @change="handleRenderCountChange"
            />
          </el-form-item>
          <el-form-item
            label="交互模式"
            class="param-item"
          >
            <el-select
              v-model="interactionMode"
              @change="onModeChange"
            >
              <el-option
                label="垂直交互模式"
                value="vertical"
              />
              <el-option
                label="漫游交互模式"
                value="free"
              />
            </el-select>
          </el-form-item>
        </div>
        
        
        <el-form-item label="贴图图片">
          <el-upload
            class="texture-uploader"
            :auto-upload="false"
            :show-file-list="false"
            :on-change="handleFileChange"
            accept="image/*"
          >
            <el-button type="primary">
              <i class="el-icon-upload" /> 选择图片
            </el-button>
          </el-upload>
          <div
            v-if="imageUrl"
            class="preview"
          >
            <img
              :src="imageUrl"
              alt="预览图片"
            >
          </div>
        </el-form-item>
        <el-button
          type="success"
          @click="generateCylinder"
        >
          <i class="el-icon-refresh" /> 生成圆柱体
        </el-button>
      </el-form>
      
      <div class="help-tips">
        <h3>操作提示</h3>
        <ul>
          <li><strong>垂直交互模式:</strong> 左键拖动可旋转圆柱体，上下移动可调整视角高度</li>
          <li><strong>漫游交互模式:</strong> 左键拖动可旋转圆柱体，右键拖动可旋转视角</li>
          <li>使用滚轮缩放模型大小</li>
          <li>漫游模式下使用 WASD 键移动摄像机</li>
          <li>Z 键顺时针旋转，C 键逆时针旋转</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, onBeforeUnmount, nextTick, computed, watch } from 'vue';
import type { UploadFile } from 'element-plus';
import { Renderer, type MarkerScreenPosition, type DepthMarker } from './webgl/Renderer';

export default defineComponent({
  name: 'WebGLCylinder',
  props: {
    initialHeight: {
      type: Number,
      default: 10,
    },
    initialDiameter: {
      type: Number,
      default: 3,
    },
    initialImage: {
      type: String,
      default: '',
    },
    depthData: {
      type: Array,
      default: () => [],
    },
    startDepth: {
      type: Number,
      default: 0,
    },
    endDepth: {
      type: Number,
      default: 0,
    }
  },
  setup(props: any) {
    // 状态数据
    const height = ref(props.initialHeight);
    const diameter = ref(props.initialDiameter);
    const imageUrl = ref(props.initialImage);
    const interactionMode = ref('vertical');
    const imageFile = ref<File | null>(null);
    const glCanvas = ref<HTMLCanvasElement | null>(null);
    
    // 渲染器实例
    let renderer: Renderer | null = null;
    
    // 用户设置的渲染点数量，null表示使用默认值
    const userRenderCount = ref<number | null>(null);
    
    // 实际的渲染点数量（响应式计算）
    const localRenderCount = computed({
      get: () => {
        if (userRenderCount.value !== null) {
          return userRenderCount.value; // 用户设置过值，使用用户设置的值
        }
        // 用户没有设置过值，使用默认值
        const dataLength = props.depthData.length;
        if (dataLength === 0) {
          return 10; // 没有数据时保持10
        }
        return Math.min(10, dataLength); // 有数据时取10和数据长度的最小值
      },
      set: (value: number) => {
        userRenderCount.value = value;
      }
    });
    
    // 存储标记点的屏幕坐标
    const markerScreenPositions = ref<Array<MarkerScreenPosition>>([]);
    
    // 最大渲染点数量
    const maxRenderCount = computed(() => {
      // 当没有数据时，返回一个合理的默认最大值，避免限制用户输入
      // 当有数据时，最大值就是实际数据的长度
      return props.depthData.length > 0 ? props.depthData.length : 20;
    });
    
    // 处理渲染点数量变化
    const handleRenderCountChange = (value: number) => {
      localRenderCount.value = value;
      updateDepthMarkers();
    };
    
    // 从数据点中提取最重要的值作为显示
    const extractImportantValue = (dataPoint: any): string => {
      if (!dataPoint) return '';
      
      if (dataPoint.lithology) return `岩性:${dataPoint.lithology}`;
      if (dataPoint.value !== undefined) return `值:${dataPoint.value}`;
      
      const numericFields = ['density', 'porosity', 'permeability', 'resistivity', 'gamma'];
      for (const field of numericFields) {
        if (dataPoint[field] !== undefined) {
          return `${field}:${dataPoint[field]}`;
        }
      }
      
      return '数据点';
    };
    
    // 计算可见标记点 - 根据渲染点数量处理数据
    const visibleMarkers = computed(() => {
      if (!props.depthData || props.depthData.length === 0) {
        return [];
      }
      
      const { startDepth, endDepth } = props;
      const totalDepth = endDepth - startDepth || 1;
      
      if (totalDepth <= 0) {
        return [];
      }
      
      // 提取深度范围内的有效数据
      const validData = props.depthData.filter((item: any) => 
        item.dpth >= startDepth && item.dpth <= endDepth
      );
      
      // 按深度排序
      validData.sort((a: any, b: any) => a.dpth - b.dpth);
      
      if (validData.length === 0) {
        return [];
      }
      
      const renderCount = Math.min(localRenderCount.value, validData.length);
      
      if (renderCount === 1) {
        // 设置为1时：展示最中间的点
        const middleIndex = Math.floor(validData.length / 2);
        const dataPoint = validData[middleIndex];
        const relativePosition = (dataPoint.dpth - startDepth) / totalDepth;
        
        return [{
          ...dataPoint,
          displayValue: extractImportantValue(dataPoint),
          relativePosition
        }];
      } else {
        // 将全部数据分为renderCount段，分别取每段的中间点
        const result = [];
        const segmentSize = validData.length / renderCount;
        
        for (let i = 0; i < renderCount; i++) {
          const segmentStart = Math.floor(i * segmentSize);
          const segmentEnd = Math.floor((i + 1) * segmentSize);
          const segmentMiddle = Math.floor((segmentStart + segmentEnd) / 2);
          
          // 确保不超出数组边界
          const index = Math.min(segmentMiddle, validData.length - 1);
          const dataPoint = validData[index];
          
          if (dataPoint) {
            const relativePosition = (dataPoint.dpth - startDepth) / totalDepth;
            
            result.push({
              ...dataPoint,
              displayValue: extractImportantValue(dataPoint),
              relativePosition
            });
          }
        }
        
        return result;
      }
    });
    
    // 更新深度标记
    const updateDepthMarkers = () => {
      if (renderer) {
        const markers = visibleMarkers.value as unknown as DepthMarker[];
        renderer.setMarkers(markers);
      }
    };
    
    // 更新标记点屏幕位置的回调函数
    const updateMarkerPositions = (positions: MarkerScreenPosition[]) => {
      markerScreenPositions.value = positions;
    };

    // 生成圆柱体
    const generateCylinder = () => {
      if (!glCanvas.value || !imageUrl.value) {
        console.error('Canvas未初始化或未选择贴图图片');
        return;
      }

      const image = new Image();
      image.onload = () => {
        if (!renderer) return;
        
        renderer.generateCylinder(height.value, diameter.value / 2, image);
        
        // 更新标记点
        updateDepthMarkers();
      };
      image.src = imageUrl.value;
    };

    // 处理文件上传
    const handleFileChange = (file: UploadFile) => {
      if (file && file.raw) {
        imageFile.value = file.raw;
        const reader = new FileReader();
        reader.onload = (e) => {
          if (e.target && e.target.result) {
            imageUrl.value = e.target.result as string;
          }
        };
        reader.readAsDataURL(file.raw);
      }
    };

    // 变更交互模式
    const onModeChange = (mode: string) => {
      if (renderer) {
        renderer.setInteractionMode(mode);
      }
    };

    // 窗口大小变化事件处理
    const handleResize = () => {
      if (renderer) {
        renderer.resize();
      }
    };

    // 监听深度数据变化
    watch(() => props.depthData, () => {
      updateDepthMarkers();
    }, { deep: true });
    
    // 监听深度范围变化
    watch([() => props.startDepth, () => props.endDepth], () => {
      updateDepthMarkers();
    });

    // 组件挂载后初始化渲染器
    onMounted(() => {
      nextTick(() => {
        if (glCanvas.value) {
          // 初始化渲染器
          renderer = new Renderer({
            canvas: glCanvas.value,
            onUpdateMarkerPositions: updateMarkerPositions,
          });
          
          // 设置初始交互模式
          renderer.setInteractionMode(interactionMode.value);
          
          // 添加窗口大小变化监听
          window.addEventListener('resize', handleResize);
          
          // 如果有初始图片，则生成圆柱体
          if (props.initialImage) {
            imageUrl.value = props.initialImage;
            generateCylinder();
          }
        }
      });
    });

    // 组件销毁前清理资源
    onBeforeUnmount(() => {
      // 移除窗口大小变化监听
      window.removeEventListener('resize', handleResize);
      
      // 释放渲染器资源
      if (renderer) {
        renderer.dispose();
        renderer = null;
      }
    });

    return {
      height,
      diameter,
      imageUrl,
      interactionMode,
      glCanvas,
      handleFileChange,
      generateCylinder,
      onModeChange,
      visibleMarkers,
      localRenderCount,
      handleRenderCountChange,
      maxRenderCount,
      markerScreenPositions
    };
  }
});
</script>

<style scoped>
.webgl-container {
  display: flex;
  height: 100%;
  width: 100%;
  min-height: 400px;
  border-radius: 12px;
  overflow: hidden;
}

.canvas-container {
  flex: 4;
  background: radial-gradient(circle at center, #1a1a2e 0%, #000 100%);
  position: relative;
  box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.5);
  transition: all 0.3s ease;
}

.canvas-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  display: flex;
  justify-content: center;
  overflow: hidden;
}

.mode-indicator {
  position: absolute;
  top: 16px;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 14px;
  backdrop-filter: blur(4px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  opacity: 0.8;
  transition: all 0.3s ease;
}

.mode-indicator:hover {
  opacity: 1;
  transform: translateY(2px);
}

.control-panel {
  flex: 1;
  padding: 24px;
  background: #f8f9fa;
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.08);
  min-width: 280px;
  max-width: 320px;
  border-radius: 0 12px 12px 0;
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow-y: auto;
}

.panel-title {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  padding-bottom: 12px;
  border-bottom: 1px solid #eee;
  text-align: center;
}

canvas {
  width: 100%;
  height: 100%;
  cursor: grab;
}

canvas:active {
  cursor: grabbing;
}

.preview {
  margin-top: 12px;
  width: 100%;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.preview:hover {
  transform: scale(1.02);
}

.preview img {
  max-width: 100%;
  max-height: 120px;
  object-fit: cover;
}

.help-tips {
  margin-top: 20px;
  padding: 16px;
  background: rgba(0, 113, 227, 0.05);
  border-radius: 8px;
  border-left: 4px solid var(--el-color-primary, #409eff);
  transition: all 0.3s ease;
}

.help-tips:hover {
  background: rgba(0, 113, 227, 0.08);
  transform: translateY(-2px);
}

.help-tips h3 {
  margin-bottom: 12px;
  font-weight: 600;
  color: var(--el-color-primary, #409eff);
  font-size: 16px;
  display: flex;
  align-items: center;
}

.help-tips h3::before {
  content: '';
  display: inline-block;
  width: 18px;
  height: 18px;
  background: var(--el-color-primary, #409eff);
  mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm-1-11v6h2v-6h-2zm0-4v2h2V7h-2z'/%3E%3C/svg%3E");
  margin-right: 8px;
}

.help-tips ul {
  list-style-type: none;
  padding-left: 8px;
}

.help-tips li {
  margin-bottom: 10px;
  color: #555;
  line-height: 1.6;
  position: relative;
  padding-left: 20px;
}

.help-tips li::before {
  content: '•';
  position: absolute;
  left: 0;
  color: var(--el-color-primary, #409eff);
  font-weight: bold;
  font-size: 18px;
}

.help-tips li strong {
  color: #333;
  font-weight: 600;
}

.el-form {
  --el-form-label-font-size: 14px;
}

.params-row {
  display: flex;
  gap: 10px;
  margin-bottom: 8px;
}

.param-item {
  flex: 1;
  min-width: 0; /* 防止内容溢出 */
}

.param-item .el-input-number,
.param-item .el-select {
  width: 100%;
}

.el-form-item {
  margin-bottom: 16px;
  transition: all 0.3s ease;
}

.el-form-item:hover {
  transform: translateX(2px);
}

.el-button {
  width: 100%;
  margin-bottom: 16px;
  transition: all 0.3s ease;
  border-radius: 6px;
  font-weight: 500;
}

.el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.texture-uploader {
  display: flex;
  justify-content: center;
}

.el-select {
  width: 100%;
}

.depth-marker {
  position: absolute;
  pointer-events: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 10;
}

.depth-text {
  color: #fff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  white-space: nowrap;
}
</style> 
<template>
  <el-card
    class="drilling-data-card"
    shadow="hover"
  >
    <template #header>
      <div class="sub-card-header">
        <span>钻进数据</span>
        <div class="chart-controls">
          <span class="interval-label">图表类型:</span>
          <el-select
            v-model="chartType"
            placeholder="选择图表类型"
            size="small"
            style="width: 120px; margin-left: 10px"
            @change="handleChartTypeChange"
          >
            <el-option
              v-for="item in chartTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
      </div>
    </template>
    <div
      ref="drillingChart"
      class="drilling-chart"
    />
  </el-card>
</template>

<script>
import { ref, watch, onMounted, nextTick, onUnmounted } from 'vue'
import * as echarts from 'echarts'

export default {
  name: 'DrillingDataCard',
  props: {
    drillingData: {
      type: Array,
      default: () => []
    }
  },
  setup(props) {
    const drillingChart = ref(null)
    let chartInstance = null
    const chartType = ref('line') // 默认显示折线图
    const chartTypeOptions = [
      { label: '折线图', value: 'line' },
      { label: '柱状图', value: 'bar' },
      { label: '面积图', value: 'area' },
      { label: '散点图', value: 'scatter' }
    ]

    // 处理图表类型变更
    const handleChartTypeChange = () => {
      // 重新初始化图表
      nextTick(() => {
        initChart()
      })
    }

    // 初始化图表
    const initChart = () => {
      if (chartInstance) {
        chartInstance.dispose()
      }

      // 确保DOM元素存在再初始化
      if (!drillingChart.value) {
        console.warn('钻进数据图表DOM元素不存在')
        return
      }

      drillingChart.value.style.height = '600px'
      chartInstance = echarts.init(drillingChart.value)

      // 处理数据，为图表做准备
      const processedData = processChartData()
      
      // 设置图表选项
      const option = createChartOption(processedData)

      chartInstance.setOption(option)
      
      // 添加点击事件
      chartInstance.on('click', (params) => {
        // 点击事件处理，可以展示详细信息
        console.log('点击了数据点：', params)
      })
    }

    // 处理数据为图表所需格式
    const processChartData = () => {
      if (!props.drillingData || props.drillingData.length === 0) {
        return {
          dpth: [],
          rtnTq: [],
          frcstKn: [],
          rtnSpd: [],
          advncSpd: []
        }
      }

      // 按照钻孔深度排序
      const sortedData = [...props.drillingData].sort((a, b) => {
        const depthA = parseFloat(a.dpth || 0)
        const depthB = parseFloat(b.dpth || 0)
        return depthA - depthB
      })

      // 提取各个参数的数据
      const dpth = sortedData.map(item => item.dpth)
      const rtnTq = sortedData.map(item => item.rtnTq)
      const frcstKn = sortedData.map(item => item.frcstKn)
      const rtnSpd = sortedData.map(item => item.rtnSpd)
      const advncSpd = sortedData.map(item => item.advncSpd)

      return {
        dpth,
        rtnTq,
        frcstKn,
        rtnSpd,
        advncSpd
      }
    }

    // 创建适配当前图表类型的选项
    const createChartOption = (data) => {
      const { dpth, rtnTq, frcstKn, rtnSpd, advncSpd } = data

      // 根据图表类型创建不同的选项
      let series = []
      const currentType = chartType.value

      // 折线图或区域图
      if (currentType === 'line' || currentType === 'area') {
        series = [
          {
            name: '旋转扭矩',
            type: 'line',
            symbol: 'circle',
            symbolSize: 5,
            sampling: 'average',
            areaStyle: currentType === 'area' ? {
              opacity: 0.4,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(255, 158, 68, 0.6)' },
                { offset: 1, color: 'rgba(255, 158, 68, 0.1)' }
              ])
            } : undefined,
            itemStyle: {
              color: '#FF9E44'
            },
            lineStyle: {
              width: 2
            },
            emphasis: {
              focus: 'series',
              itemStyle: {
                borderWidth: 2,
                shadowBlur: 10
              }
            },
            data: rtnTq
          },
          {
            name: '推进力',
            type: 'line',
            symbol: 'circle',
            symbolSize: 5,
            sampling: 'average',
            areaStyle: currentType === 'area' ? {
              opacity: 0.4,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(80, 145, 255, 0.6)' },
                { offset: 1, color: 'rgba(80, 145, 255, 0.1)' }
              ])
            } : undefined,
            itemStyle: {
              color: '#5091FF'
            },
            lineStyle: {
              width: 2
            },
            emphasis: {
              focus: 'series',
              itemStyle: {
                borderWidth: 2,
                shadowBlur: 10
              }
            },
            data: frcstKn
          },
          {
            name: '旋转速度',
            type: 'line',
            symbol: 'circle',
            symbolSize: 5,
            sampling: 'average',
            areaStyle: currentType === 'area' ? {
              opacity: 0.4,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(145, 204, 117, 0.6)' },
                { offset: 1, color: 'rgba(145, 204, 117, 0.1)' }
              ])
            } : undefined,
            itemStyle: {
              color: '#91CC75'
            },
            lineStyle: {
              width: 2
            },
            emphasis: {
              focus: 'series',
              itemStyle: {
                borderWidth: 2,
                shadowBlur: 10
              }
            },
            data: rtnSpd
          },
          {
            name: '钻进速度',
            type: 'line',
            symbol: 'circle',
            symbolSize: 5,
            sampling: 'average',
            areaStyle: currentType === 'area' ? {
              opacity: 0.4,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(238, 102, 102, 0.6)' },
                { offset: 1, color: 'rgba(238, 102, 102, 0.1)' }
              ])
            } : undefined,
            itemStyle: {
              color: '#EE6666'
            },
            lineStyle: {
              width: 2
            },
            emphasis: {
              focus: 'series',
              itemStyle: {
                borderWidth: 2,
                shadowBlur: 10
              }
            },
            data: advncSpd
          }
        ]
      }
      // 柱状图
      else if (currentType === 'bar') {
        series = [
          {
            name: '旋转扭矩',
            type: 'bar',
            barWidth: '60%',
            stack: 'total',
            itemStyle: {
              color: '#FF9E44',
              borderRadius: [4, 4, 0, 0]
            },
            emphasis: {
              focus: 'series'
            },
            data: rtnTq
          },
          {
            name: '推进力',
            type: 'bar',
            barWidth: '60%',
            stack: 'total',
            itemStyle: {
              color: '#5091FF'
            },
            emphasis: {
              focus: 'series'
            },
            data: frcstKn
          },
          {
            name: '旋转速度',
            type: 'bar',
            barWidth: '60%',
            stack: 'total',
            itemStyle: {
              color: '#91CC75'
            },
            emphasis: {
              focus: 'series'
            },
            data: rtnSpd
          },
          {
            name: '钻进速度',
            type: 'bar',
            barWidth: '60%',
            stack: 'total',
            itemStyle: {
              color: '#EE6666'
            },
            emphasis: {
              focus: 'series'
            },
            data: advncSpd
          }
        ]
      }
      // 散点图
      else if (currentType === 'scatter') {
        // 将数据重组为散点格式
        const rtnTqData = dpth.map((d, idx) => [parseFloat(d), parseFloat(rtnTq[idx] || 0)])
        const frcstKnData = dpth.map((d, idx) => [parseFloat(d), parseFloat(frcstKn[idx] || 0)])
        const rtnSpdData = dpth.map((d, idx) => [parseFloat(d), parseFloat(rtnSpd[idx] || 0)])
        const advncSpdData = dpth.map((d, idx) => [parseFloat(d), parseFloat(advncSpd[idx] || 0)])

        series = [
          {
            name: '旋转扭矩',
            type: 'scatter',
            symbolSize: 10,
            itemStyle: {
              color: '#FF9E44'
            },
            emphasis: {
              focus: 'series',
              itemStyle: {
                borderWidth: 2,
                shadowBlur: 10
              }
            },
            data: rtnTqData
          },
          {
            name: '推进力',
            type: 'scatter',
            symbolSize: 10,
            itemStyle: {
              color: '#5091FF'
            },
            emphasis: {
              focus: 'series',
              itemStyle: {
                borderWidth: 2,
                shadowBlur: 10
              }
            },
            data: frcstKnData
          },
          {
            name: '旋转速度',
            type: 'scatter',
            symbolSize: 10,
            itemStyle: {
              color: '#91CC75'
            },
            emphasis: {
              focus: 'series',
              itemStyle: {
                borderWidth: 2,
                shadowBlur: 10
              }
            },
            data: rtnSpdData
          },
          {
            name: '钻进速度',
            type: 'scatter',
            symbolSize: 10,
            itemStyle: {
              color: '#EE6666'
            },
            emphasis: {
              focus: 'series',
              itemStyle: {
                borderWidth: 2,
                shadowBlur: 10
              }
            },
            data: advncSpdData
          }
        ]
      }

      // 通用图表配置
      const option = {
        tooltip: {
          trigger: currentType === 'scatter' ? 'item' : 'axis',
          axisPointer: {
            type: 'cross'
          },
          formatter: function (params) {
            if (currentType === 'scatter') {
              return `${params.seriesName}<br/>
                     钻孔深度: ${params.value[0]} cm<br/>
                     ${params.seriesName}: ${params.value[1]}`
            } else {
              // 其他图表类型的提示框
              let result = `钻孔深度: ${dpth[params[0].dataIndex]} cm<br/>`
              params.forEach(param => {
                result += `${param.marker} ${param.seriesName}: ${param.value}<br/>`
              })
              return result
            }
          },
          backgroundColor: 'rgba(255,255,255,0.95)',
          borderColor: '#ddd',
          borderWidth: 1,
          textStyle: {
            color: '#333'
          },
          padding: 10,
          extraCssText: 'box-shadow: 0 0 8px rgba(0, 0, 0, 0.2); border-radius: 4px;'
        },
        legend: {
          data: ['旋转扭矩', '推进力', '旋转速度', '钻进速度'],
          top: 20,
          left: 'center',
          icon: 'circle',
          textStyle: {
            fontSize: 12
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: 80,
          containLabel: true
        },
        xAxis: {
          type: currentType === 'scatter' ? 'value' : 'category',
          name: '钻孔深度(cm)',
          nameLocation: 'middle',
          nameGap: 30,
          nameTextStyle: {
            fontWeight: 'bold'
          },
          data: currentType === 'scatter' ? null : dpth,
          axisLine: {
            show: true,
            lineStyle: {
              color: '#999'
            }
          },
          axisTick: {
            show: true,
            alignWithLabel: true
          },
          axisLabel: {
            formatter: currentType === 'scatter' ? '{value}' : '{value}',
            color: '#666'
          },
          boundaryGap: currentType === 'bar'
        },
        yAxis: {
          type: 'value',
          name: '数值',
          nameLocation: 'end',
          nameTextStyle: {
            fontWeight: 'bold'
          },
          splitNumber: 5,
          axisLine: {
            show: true,
            lineStyle: {
              color: '#999'
            }
          },
          axisTick: {
            show: true
          },
          axisLabel: {
            formatter: '{value}',
            color: '#666'
          },
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed',
              color: '#ddd'
            }
          }
        },
        dataZoom: [
          {
            type: 'slider',
            xAxisIndex: 0,
            filterMode: 'filter',
            height: 20,
            bottom: 10,
            start: 0,
            end: 100,
            handleIcon:
              'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7v-1.2h6.6V24.4z M13.3,22H6.7v-1.2h6.6V22z M13.3,19.6H6.7v-1.2h6.6V19.6z',
            handleSize: '80%',
            handleStyle: {
              color: '#fff',
              shadowBlur: 3,
              shadowColor: 'rgba(0, 0, 0, 0.6)',
              shadowOffsetX: 2,
              shadowOffsetY: 2
            }
          },
          {
            type: 'inside',
            xAxisIndex: 0,
            filterMode: 'filter',
            start: 0,
            end: 100
          }
        ],
        series: series
      }

      return option
    }

    // 监听数据变化
    watch(() => props.drillingData, () => {
      nextTick(initChart)
    }, { deep: true })

    // 监听图表类型变化
    watch(() => chartType.value, () => {
      nextTick(initChart)
    })

    // 组件挂载时初始化图表
    onMounted(() => {
      initChart()
      window.addEventListener('resize', handleResize)
    })

    // 组件卸载前清理
    onUnmounted(() => {
      window.removeEventListener('resize', handleResize)
      if (chartInstance) {
        chartInstance.dispose()
      }
    })

    // 处理窗口大小变化
    const handleResize = () => {
      if (chartInstance) {
        chartInstance.resize()
      }
    }

    return {
      drillingChart,
      chartType,
      chartTypeOptions,
      handleChartTypeChange
    }
  }
}
</script>

<style scoped>
.drilling-data-card {
  margin-bottom: 20px;
  position: relative;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08) !important;
  border-radius: 12px;
  border: none;
}

.drilling-data-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #409eff, #67c23a);
  z-index: 1;
}

.drilling-data-card :deep(.el-card__header) {
  padding: 18px 24px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  background: rgba(255, 255, 255, 0.6);
}

.sub-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.sub-card-header span:first-child {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  position: relative;
}

.sub-card-header span:first-child::after {
  content: "";
  position: absolute;
  bottom: -6px;
  left: 0;
  width: 40px;
  height: 3px;
  background-color: #409eff;
  border-radius: 3px;
}

.drilling-chart {
  width: 100%;
  height: 600px;
}

.chart-controls {
  display: flex;
  align-items: center;
}

.interval-label {
  font-size: 14px;
  color: #606266;
}

.drilling-data-card .chart-controls .el-select {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.drilling-data-card .chart-controls .el-select:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
</style> 
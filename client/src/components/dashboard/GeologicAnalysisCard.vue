<template>
  <el-card
    class="rock-property-card"
    shadow="hover"
  >
    <template #header>
      <div class="sub-card-header">
        地质分析
      </div>
    </template>
    <div
      ref="stratumAnalysis"
      class="stratum-analysis"
    >
      <div 
        v-for="(item, index) in geologicAnalysisReport" 
        :key="index" 
        class="stratum-item"
      >
        <div class="stratum-label">
          {{ item.name }}
        </div>
        <div class="stratum-value">
          {{ item.description }}
        </div>
        <div
          v-if="item.suggestion"
          class="stratum-suggestion"
        >
          {{ item.suggestion }}
        </div>
      </div>
    </div>
  </el-card>
</template>

<script lang="ts">
/**
 * 地质分析卡片组件
 * 显示地质分析报告和建议
 */

import { ref, onMounted, onUnmounted, nextTick } from 'vue'

// 地质分析报告项类型
interface GeologicAnalysisItem {
  name: string
  description: string
  suggestion?: string
  value?: number | string
  unit?: string
}

export default {
  name: 'GeologicAnalysisCard',
  props: {
    geologicAnalysisReport: {
      type: Array as () => GeologicAnalysisItem[],
      default: () => []
    }
  },
  setup() {
    const stratumAnalysis = ref<HTMLDivElement | null>(null)
    let lastCalculatedHeight = 0 // 记录上次计算的高度，避免无限循环
    let resizeTimer = null // 防抖定时器

    // 计算内容容器的最佳高度 - 参考 DataTreeChartCard、Drilling3DAnalysisCard、DrillCurveChartCard、RockStatsCard、RockPropertyCard 和 StrataDistributionCard 的实现
    const calculateContentHeight = () => {
      if (!stratumAnalysis.value) {
        return 450 // 默认高度
      }

      // 获取父容器的实际高度 - 使用 el-card 作为参考容器
      const cardBodyElement = stratumAnalysis.value.parentElement // el-card__body
      const cardElement = cardBodyElement?.parentElement // el-card

      // 使用 el-card 作为参考容器（第二层）
      const referenceElement = cardElement

      // 检查是否在flex布局中，如果是则使用默认高度
      const parentElement = cardElement?.parentElement // .report-cards
      const parentComputedStyle = parentElement ? window.getComputedStyle(parentElement) : null
      const isInFlexLayout = parentComputedStyle?.display === 'flex'

      // 1. 当父级高度没有设置时，或者在flex布局中时，使用默认高度
      if (!referenceElement || referenceElement.offsetHeight <= 100 || isInFlexLayout) {
        return 450 // 父级高度没有设置时的默认高度
      }

      // 2. 当父级高度设置了时，按照父级高度自动调整，不使用默认高度
      const parentHeight = referenceElement.offsetHeight

      // 3. 获取实际的header高度
      const headerElement = cardElement?.querySelector('.el-card__header')
      const actualHeaderHeight = headerElement ? headerElement.offsetHeight : 80

      // 3. 获取实际的padding（el-card__body的padding）
      const cardBodyStyles = window.getComputedStyle(cardBodyElement)
      const paddingTop = parseInt(cardBodyStyles.paddingTop) || 0
      const paddingBottom = parseInt(cardBodyStyles.paddingBottom) || 0
      const actualPadding = paddingTop + paddingBottom

      // 计算内容区域可用高度：完全按照父级容器调整
      const availableHeight = parentHeight - actualHeaderHeight - actualPadding

      // 如果计算出的高度太小（可能是DOM还没完全渲染），使用默认高度
      if (availableHeight < 300) {
        return 450 // 使用默认高度，而不是强制使用计算出的小值
      }

      return availableHeight
    }

    // 初始化内容容器
    const initContent = () => {
      if (!stratumAnalysis.value) {
        console.warn('内容容器DOM元素不存在')
        return
      }

      // 动态计算并设置内容高度
      const contentHeight = calculateContentHeight()
      lastCalculatedHeight = contentHeight // 记录初始高度
      stratumAnalysis.value.style.height = contentHeight + 'px'
    }

    // 存储所有定时器
    let allTimers = []

    // 处理窗口大小变化
    const handleResize = () => {
      // 清除之前的定时器，实现防抖
      if (resizeTimer) {
        clearTimeout(resizeTimer)
      }

      resizeTimer = setTimeout(() => {
        if (stratumAnalysis.value) {
          // 重新计算内容高度以适应容器变化
          const newHeight = calculateContentHeight()

          // 只有当高度变化超过10px时才更新，避免无限循环
          if (Math.abs(newHeight - lastCalculatedHeight) > 10) {
            lastCalculatedHeight = newHeight
            stratumAnalysis.value.style.height = newHeight + 'px'

            // 延迟执行resize，确保DOM更新完成
            const timer = setTimeout(() => {
              // 这里可以添加其他需要在高度更新后执行的逻辑
            }, 100)
            allTimers.push(timer)
          }
        }
      }, 150) // 150ms防抖延迟
    }

    // 组件挂载时初始化高度
    onMounted(() => {
      // 立即设置初始高度，确保默认高度生效
      nextTick(() => {
        if (stratumAnalysis.value) {
          stratumAnalysis.value.style.height = '450px'
        }
      })
      // 延迟初始化，确保DOM完全渲染和父容器尺寸确定
      const timer1 = setTimeout(() => {
        initContent()
      }, 100)
      allTimers.push(timer1)

      // 再次延迟初始化，处理可能的异步布局变化
      const timer2 = setTimeout(() => {
        if (stratumAnalysis.value) {
          const newHeight = calculateContentHeight()
          if (stratumAnalysis.value) {
            stratumAnalysis.value.style.height = newHeight + 'px'
          }
        }
      }, 500)
      allTimers.push(timer2)

      // 添加窗口resize监听
      window.addEventListener('resize', handleResize)

      // 添加ResizeObserver监听容器尺寸变化（用于模板模式）
      if (window.ResizeObserver) {
        const timer3 = setTimeout(() => {
          if (stratumAnalysis.value) {
            const resizeObserver = new ResizeObserver(() => {
              handleResize()
            })

            // 只观察外层容器，避免监听内容本身导致无限循环
            const cardElement = stratumAnalysis.value.parentElement?.parentElement // el-card
            const outerElement = cardElement?.parentElement // 外层容器

            // 优先监听外层容器，如果没有则监听card容器
            const targetElement = outerElement || cardElement

            if (targetElement) {
              resizeObserver.observe(targetElement)
            }

            // 在组件卸载时清理observer
            onUnmounted(() => {
              resizeObserver.disconnect()
            })
          }
        }, 200)
        allTimers.push(timer3)
      }
    })

    // 组件卸载前清理
    onUnmounted(() => {
      window.removeEventListener('resize', handleResize)

      // 清理防抖定时器
      if (resizeTimer) {
        clearTimeout(resizeTimer)
        resizeTimer = null
      }

      // 清理所有定时器
      allTimers.forEach(timer => {
        clearTimeout(timer)
      })
      allTimers = []
    })

    return {
      stratumAnalysis
    }
  }
}
</script>

<style scoped>
.rock-property-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08) !important;
  border-radius: 12px;
  border: none;
  position: relative;
  overflow: hidden;
  background: #fff;
}

.rock-property-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #409eff, #67c23a);
  z-index: 1;
}

.rock-property-card :deep(.el-card__header) {
  padding: 18px 24px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  background: rgba(255, 255, 255, 0.6);
}

.sub-card-header {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  position: relative;
  display: flex;
  align-items: center;
}

.sub-card-header::after {
  content: "";
  position: absolute;
  bottom: -6px;
  left: 0;
  width: 40px;
  height: 3px;
  background-color: #409eff;
  border-radius: 3px;
}

.stratum-analysis {
  overflow-y: auto;
  padding-right: 5px;
  flex: 1;
  width: 100%;
}

.stratum-item {
  margin-bottom: 15px;
}

.stratum-label {
  font-weight: 500;
  margin-bottom: 5px;
  color: #303133;
}

.stratum-value {
  font-size: 13px;
  color: #606266;
  line-height: 1.5;
}

.stratum-suggestion {
  font-size: 13px;
  color: #67C23A;
  line-height: 1.5;
  margin-top: 6px;
  font-style: italic;
}
</style> 
<template>
  <el-card
    class="tree-chart-card"
    shadow="hover"
  >
    <template #header>
      <div class="sub-card-header">
        <span>数据树状图</span>
        <div class="chart-controls">
          <span class="interval-label">深度区间范围:</span>
          <el-input-number
            v-model="depthIntervalSize"
            :min="10"
            :max="500"
            :step="10"
            size="small"
            style="width: 120px; margin-left: 10px"
            @change="handleIntervalChange"
          />
          <span class="interval-unit">cm</span>
        </div>
      </div>
    </template>
    <div
      ref="treeChartRef"
      class="tree-chart"
    />
  </el-card>
</template>

<script lang="ts">
/**
 * 数据树图表卡片组件
 * 显示钻进数据的树状结构分析
 */

import { ref, watch, onMounted, nextTick, onUnmounted } from 'vue'
import * as echarts from 'echarts'

// 钻进数据项类型
interface DrillingDataItem {
  collectionAt: string
  dpth?: number
  advncSpd?: number
  rtnTq?: number
  wtrPrsH?: number
  frcstKn?: number
  rtnSpd?: number
  [key: string]: unknown
}

// 树节点类型
interface TreeNode {
  name: string
  value: number
  children?: TreeNode[]
  itemStyle?: {
    color: string
  }
}

export default {
  name: 'DataTreeChartCard',
  props: {
    drillingData: {
      type: Array as () => DrillingDataItem[],
      default: () => []
    }
  },
  setup(props: { drillingData: DrillingDataItem[] }) {
    const treeChartRef = ref<HTMLDivElement | null>(null)
    let treeChartInstance: echarts.ECharts | null = null
    const depthIntervalSize = ref<number>(100) // 默认深度区间大小为100cm
    let lastCalculatedHeight = 0 // 记录上次计算的高度，避免无限循环
    let resizeTimer: NodeJS.Timeout | null = null // 防抖定时器

    // 计算图表容器的最佳高度 - 参考 DrillCurveChartCard、Drilling3DAnalysisCard 和 StrataDistributionCard 的实现
    const calculateChartHeight = () => {
      if (!treeChartRef.value) {
        return 550 // 默认高度
      }

      // 获取父容器的实际高度 - 使用 el-card 作为参考容器
      const cardBodyElement = treeChartRef.value.parentElement // el-card__body
      const cardElement = cardBodyElement?.parentElement // el-card

      // 使用 el-card 作为参考容器（第二层）
      const referenceElement = cardElement

      // 1. 当父级高度没有设置时，使用默认高度
      if (!referenceElement || referenceElement.offsetHeight <= 100) {
        return 550 // 父级高度没有设置时的默认高度
      }

      // 2. 当父级高度设置了时，按照父级高度自动调整，不使用默认高度
      const parentHeight = referenceElement.offsetHeight

      // 3. 获取实际的header高度
      const headerElement = cardElement?.querySelector('.el-card__header')
      const actualHeaderHeight = headerElement ? headerElement.offsetHeight : 80

      // 3. 获取实际的padding（el-card__body的padding）
      const cardBodyStyles = window.getComputedStyle(cardBodyElement)
      const paddingTop = parseInt(cardBodyStyles.paddingTop) || 0
      const paddingBottom = parseInt(cardBodyStyles.paddingBottom) || 0
      const actualPadding = paddingTop + paddingBottom

      // 计算图表区域可用高度：完全按照父级容器调整
      const availableHeight = parentHeight - actualHeaderHeight - actualPadding

      // 如果计算出的高度太小（可能是DOM还没完全渲染），使用默认高度
      if (availableHeight < 300) {
        return 550 // 使用默认高度，而不是强制使用计算出的小值
      }

      return availableHeight
    }

    // 处理深度区间大小变更
    const handleIntervalChange = () => {
      // 重新初始化树状图
      nextTick(() => {
        initChart()
      })
    }

    // 初始化树状图
    const initChart = () => {
      if (treeChartInstance) {
        treeChartInstance.dispose()
      }

      // 确保DOM元素存在再初始化
      if (!treeChartRef.value) {
        console.warn('钻进数据树状图DOM元素不存在')
        return
      }

      // 动态计算并设置图表高度
      const chartHeight = calculateChartHeight()
      lastCalculatedHeight = chartHeight // 记录初始高度
      treeChartRef.value.style.height = chartHeight + 'px'

      treeChartInstance = echarts.init(treeChartRef.value)

      // 处理数据，构建树状结构
      const treeData = processTreeData()

      // 设置树状图选项
      const option = {
        tooltip: {
          trigger: 'item',
          triggerOn: 'mousemove',
          formatter: function(params) {
            if (params.data.value) {
              return `${params.name}: ${params.data.value}条数据`
            }
            return params.name
          }
        },
        series: [
          {
            type: 'tree',
            data: [treeData],
            top: '1%',
            left: '7%',
            bottom: '1%',
            right: '20%',
            symbolSize: 10,
            itemStyle: {
              color: '#409EFF',
              borderColor: '#409EFF'
            },
            label: {
              position: 'left',
              verticalAlign: 'middle',
              align: 'right',
              fontSize: 12,
              color: '#333'
            },
            leaves: {
              label: {
                position: 'right',
                verticalAlign: 'middle',
                align: 'left'
              }
            },
            emphasis: {
              focus: 'descendant'
            },
            expandAndCollapse: true,
            animationDuration: 550,
            animationDurationUpdate: 750
          }
        ]
      }

      treeChartInstance.setOption(option)
    }

    // 处理数据，构建树状结构
    const processTreeData = () => {
      if (!props.drillingData || props.drillingData.length === 0) {
        return {
          name: '钻进数据',
          children: [{ name: '无数据' }]
        }
      }

      // 按孔号分组
      const holeGroups = {}
      props.drillingData.forEach(item => {
        const holeNum = item.hlNum || '未知孔号'
        if (!holeGroups[holeNum]) {
          holeGroups[holeNum] = []
        }
        holeGroups[holeNum].push(item)
      })

      // 构建树状结构
      const children = []
      for (const holeNum in holeGroups) {
        // 按钻进深度范围分组（每 depthIntervalSize 为一组）
        const depthGroups = {}
        holeGroups[holeNum].forEach(item => {
          const depth = item.dpth || 0
          // 如果是未知深度，单独分组
          if (depth === '未知深度') {
            if (!depthGroups['未知深度']) {
              depthGroups['未知深度'] = []
            }
            depthGroups['未知深度'].push(item)
            return
          }
          
          // 计算所属的深度范围
          const depthValue = parseInt(depth, 10)
          // 计算区间下限（取整到depthIntervalSize的倍数）
          const interval = depthIntervalSize.value
          const lowerBound = Math.floor(depthValue / interval) * interval
          // 计算区间上限
          const upperBound = lowerBound + interval
          // 使用区间范围作为键
          const rangeKey = `${lowerBound}-${upperBound}`
          
          if (!depthGroups[rangeKey]) {
            depthGroups[rangeKey] = []
          }
          depthGroups[rangeKey].push(item)
        })

        // 构建钻进深度子节点
        const depthChildren = []
        for (const depth in depthGroups) {
          // 按实际钻机深度分组，构建第三级节点
          const actualDepthChildren = []
          const depthItems = depthGroups[depth]
          
          // 按实际深度值创建一个Map，便于分组
          const actualDepthMap = new Map()
          depthItems.forEach(item => {
            const actualDepth = item.dpth || '未知'
            if (!actualDepthMap.has(actualDepth)) {
              actualDepthMap.set(actualDepth, [])
            }
            actualDepthMap.get(actualDepth).push(item)
          })
          
          // 为每个实际深度创建节点
          for (const [actualDepth, items] of actualDepthMap) {
            // 获取该深度对应的围岩等级
            // 假设每个深度只有一个围岩等级，取第一个数据的围岩等级
            const item = items[0]
            const grade = item.rockGradeDesc || '未知等级'
            
            // 添加实际深度节点，直接包含围岩等级信息
            actualDepthChildren.push({
              name: `钻进深度: ${actualDepth}cm (${grade})`,
              value: items.length, // 保存数据条数
              collapsed: actualDepthChildren.length > 0 // 第一个节点展开，其余折叠
            })
          }

          // 对于区间范围的标签使用范围表示，对于未知深度直接显示
          depthChildren.push({
            name: depth === '未知深度' ? `深度区间: 未知` : `深度区间: ${depth}cm`,
            children: actualDepthChildren,
            collapsed: depthChildren.length > 0 // 第一个节点展开，其余折叠
          })
        }

        children.push({
          name: `孔号: ${holeNum}`,
          children: depthChildren, 
          collapsed: children.length > 0 // 第一个节点展开，其余折叠
        })
      }

      return {
        name: '钻进数据',
        children: children
      }
    }

    // 监听数据变化
    watch(() => props.drillingData, () => {
      nextTick(initChart)
    }, { deep: true })

    // 监听深度区间大小变化
    watch(() => depthIntervalSize.value, () => {
      nextTick(initChart)
    })

    // 存储所有定时器
    let allTimers = []

    // 组件挂载时初始化图表
    onMounted(() => {
      // 延迟初始化，确保DOM完全渲染和父容器尺寸确定
      const timer1 = setTimeout(() => {
        initChart()
      }, 100)
      allTimers.push(timer1)

      // 再次延迟初始化，处理可能的异步布局变化
      const timer2 = setTimeout(() => {
        if (treeChartInstance) {
          const newHeight = calculateChartHeight()
          if (treeChartRef.value) {
            treeChartRef.value.style.height = newHeight + 'px'
            treeChartInstance.resize()
          }
        }
      }, 500)
      allTimers.push(timer2)

      // 添加窗口resize监听
      window.addEventListener('resize', handleResize)

      // 添加ResizeObserver监听容器尺寸变化（用于模板模式）
      if (window.ResizeObserver) {
        const timer3 = setTimeout(() => {
          if (treeChartRef.value) {
            const resizeObserver = new ResizeObserver(() => {
              handleResize()
            })

            // 只观察外层容器，避免监听图表本身导致无限循环
            const cardElement = treeChartRef.value.parentElement?.parentElement // el-card
            const outerElement = cardElement?.parentElement // 外层容器

            // 优先监听外层容器，如果没有则监听card容器
            const targetElement = outerElement || cardElement

            if (targetElement) {
              resizeObserver.observe(targetElement)
            }

            // 在组件卸载时清理observer
            onUnmounted(() => {
              resizeObserver.disconnect()
            })
          }
        }, 200)
        allTimers.push(timer3)
      }
    })

    // 组件卸载前清理
    onUnmounted(() => {
      window.removeEventListener('resize', handleResize)

      // 清理防抖定时器
      if (resizeTimer) {
        clearTimeout(resizeTimer)
        resizeTimer = null
      }

      // 清理所有定时器
      allTimers.forEach(timer => {
        clearTimeout(timer)
      })
      allTimers = []

      if (treeChartInstance) {
        treeChartInstance.dispose()
        treeChartInstance = null
      }
    })

    // 处理窗口大小变化
    const handleResize = () => {
      // 清除之前的定时器，实现防抖
      if (resizeTimer) {
        clearTimeout(resizeTimer)
      }

      resizeTimer = setTimeout(() => {
        if (treeChartInstance && treeChartRef.value) {
          // 重新计算图表高度以适应容器变化
          const newHeight = calculateChartHeight()

          // 只有当高度变化超过10px时才更新，避免无限循环
          if (Math.abs(newHeight - lastCalculatedHeight) > 10) {
            lastCalculatedHeight = newHeight
            treeChartRef.value.style.height = newHeight + 'px'

            // 延迟执行resize，确保DOM更新完成
            const timer = setTimeout(() => {
              if (treeChartInstance) {
                treeChartInstance.resize()
              }
            }, 100)
            allTimers.push(timer)
          }
        }
      }, 150) // 150ms防抖延迟
    }

    return {
      treeChartRef,
      depthIntervalSize,
      handleIntervalChange
    }
  }
}
</script>

<style scoped>
.tree-chart-card {
  margin-bottom: 20px;
  position: relative;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08) !important;
  border-radius: 12px;
  border: none;
}

.tree-chart-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #409eff, #67c23a);
  z-index: 1;
}

.tree-chart-card :deep(.el-card__header) {
  padding: 18px 24px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  background: rgba(255, 255, 255, 0.6);
}

.sub-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.sub-card-header span:first-child {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  position: relative;
}

.sub-card-header span:first-child::after {
  content: "";
  position: absolute;
  bottom: -6px;
  left: 0;
  width: 40px;
  height: 3px;
  background-color: #409eff;
  border-radius: 3px;
}

.tree-chart {
  width: 100%;
  height: 100%;
}

.chart-controls {
  display: flex;
  align-items: center;
}

.interval-label, 
.interval-unit {
  font-size: 14px;
  color: #606266;
}

.tree-chart-card .chart-controls .el-input-number {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.tree-chart-card .chart-controls .el-input-number:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
</style> 
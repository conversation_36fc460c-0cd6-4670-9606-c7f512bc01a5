<template>
  <el-card
    class="distribution-chart sub-card rock-property-card"
    shadow="hover"
  >
    <template #header>
      <div class="sub-card-header">
        <span>岩层分布</span>
      </div>
    </template>
    <div
      ref="distributionChart"
      class="distribution-chart"
    />

    <!-- 数值编辑对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="修改数据点"
      width="400px"
      :before-close="handleDialogClose"
    >
      <el-form
        :model="editForm"
        label-width="100px"
      >
        <el-form-item label="深度(cm)">
          <el-input
            v-model="editForm.depth"
            disabled
          />
        </el-form-item>
        <el-form-item
          label="钻进速度"
          required
        >
          <el-input-number
            v-model="editForm.value"
            :min="0"
            :max="200"
            :precision="2"
            :step="0.1"
            style="width: 100%"
          />
          <div class="unit-text">
            cm/min
          </div>
        </el-form-item>
        <el-form-item label="岩石类型">
          <el-tag :type="getRockTypeTagType(editForm.value)">
            {{ getRockTypeByValue(editForm.value) }}
          </el-tag>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleDialogClose">取消</el-button>
          <el-button
            type="primary"
            @click="handleSaveEdit"
          >确定</el-button>
        </span>
      </template>
    </el-dialog>
  </el-card>
</template>

<script>
import { ref, onMounted, watch, nextTick, onUnmounted, reactive } from 'vue'
import * as echarts from 'echarts'
import { ElMessage } from 'element-plus'

export default {
  name: 'StrataDistributionCard',
  props: {
    strataDistributionData: {
      type: Object,
      default: () => ({ data: [] })
    }
  },
  setup(props) {
    const distributionChart = ref(null)
    let distributionChartInstance = null
    let lastCalculatedHeight = 0 // 记录上次计算的高度，避免无限循环
    let resizeTimer = null // 防抖定时器

    // 编辑对话框相关状态
    const editDialogVisible = ref(false)
    const editForm = reactive({
      depth: 0,
      value: 0,
      originalValue: 0,
      dataIndex: -1
    })

    // 本地数据副本，用于编辑修改
    const localChartData = ref([])

    // 岩石强度阈值（从原始数据中提取，用于判断岩石类型）
    let rockThresholds = {
      极坚固: 25,
      很坚固: 50,
      坚固: 70,
      比较坚固: 90,
      中等坚固: 100,
      较软: 130,
      空洞: 150
    }

    // 根据数值获取岩石类型
    const getRockTypeByValue = (value) => {
      if (value <= rockThresholds.极坚固) return '极坚固'
      else if (value <= rockThresholds.很坚固) return '很坚固'
      else if (value <= rockThresholds.坚固) return '坚固'
      else if (value <= rockThresholds.比较坚固) return '比较坚固'
      else if (value <= rockThresholds.中等坚固) return '中等坚固'
      else if (value <= rockThresholds.较软) return '软岩'
      else return '空洞'
    }

    // 根据岩石类型获取标签颜色
    const getRockTypeTagType = (value) => {
      const rockType = getRockTypeByValue(value)
      const typeMap = {
        '极坚固': 'danger',
        '很坚固': 'warning',
        '坚固': 'primary',
        '比较坚固': 'success',
        '中等坚固': 'info',
        '软岩': '',
        '空洞': 'info'
      }
      return typeMap[rockType] || ''
    }

    // 处理点击事件
    const handleChartClick = (params) => {
      if (params.componentType === 'series' && params.seriesType === 'line') {
        const dataIndex = params.dataIndex
        const pointData = localChartData.value[dataIndex]

        if (pointData) {
          editForm.depth = pointData[0]
          editForm.value = pointData[1]
          editForm.originalValue = pointData[1]
          editForm.dataIndex = dataIndex
          editDialogVisible.value = true
        }
      }
    }

    // 处理对话框关闭
    const handleDialogClose = () => {
      editDialogVisible.value = false
      // 重置表单
      editForm.depth = 0
      editForm.value = 0
      editForm.originalValue = 0
      editForm.dataIndex = -1
    }

    // 保存编辑
    const handleSaveEdit = () => {
      if (editForm.dataIndex >= 0 && editForm.dataIndex < localChartData.value.length) {
        // 更新本地数据
        localChartData.value[editForm.dataIndex][1] = editForm.value

        // 只更新单个数据点
        updateSingleDataPoint(editForm.dataIndex, editForm.value)

        ElMessage.success('数据修改成功')
        handleDialogClose()
      }
    }

    // 计算图表容器的最佳高度 - 参考 Drilling3DAnalysisCard 和 DrillCurveChartCard 的实现
    const calculateChartHeight = () => {
      if (!distributionChart.value) {
        return 550 // 默认高度
      }

      // 获取父容器的实际高度 - 使用 el-card 作为参考容器
      const cardBodyElement = distributionChart.value.parentElement // el-card__body
      const cardElement = cardBodyElement?.parentElement // el-card

      // 使用 el-card 作为参考容器（第二层）
      const referenceElement = cardElement

      // 1. 当父级高度没有设置时，使用默认高度
      if (!referenceElement || referenceElement.offsetHeight <= 100) {
        return 550 // 父级高度没有设置时的默认高度
      }

      // 2. 当父级高度设置了时，按照父级高度自动调整，不使用默认高度
      const parentHeight = referenceElement.offsetHeight

      // 3. 获取实际的header高度
      const headerElement = cardElement?.querySelector('.el-card__header')
      const actualHeaderHeight = headerElement ? headerElement.offsetHeight : 80

      // 3. 获取实际的padding（el-card__body的padding）
      const cardBodyStyles = window.getComputedStyle(cardBodyElement)
      const paddingTop = parseInt(cardBodyStyles.paddingTop) || 0
      const paddingBottom = parseInt(cardBodyStyles.paddingBottom) || 0
      const actualPadding = paddingTop + paddingBottom

      // 计算图表区域可用高度：完全按照父级容器调整
      const availableHeight = parentHeight - actualHeaderHeight - actualPadding

      // 如果计算出的高度太小（可能是DOM还没完全渲染），使用默认高度
      if (availableHeight < 300) {
        return 550 // 使用默认高度，而不是强制使用计算出的小值
      }

      return availableHeight
    }

    // 更新单个数据点
    const updateSingleDataPoint = (dataIndex, newValue) => {
      if (!distributionChartInstance) return

      try {
        // 获取当前图表配置
        const option = distributionChartInstance.getOption()

        // 更新指定数据点
        if (option.series && option.series[0] && option.series[0].data) {
          option.series[0].data[dataIndex][1] = newValue

          // 只更新数据，不重新渲染整个图表
          distributionChartInstance.setOption({
            series: [{
              data: option.series[0].data
            }]
          }, false) // false 表示不合并配置，直接替换
        }
      } catch (error) {
        console.error('更新单个数据点失败:', error)
        // 如果单点更新失败，回退到全量更新
        updateChart()
      }
    }

    // 更新图表数据（保留作为备用方法）
    const updateChart = () => {
      if (!distributionChartInstance) return

      const option = distributionChartInstance.getOption()
      option.series[0].data = localChartData.value
      distributionChartInstance.setOption(option)
    }

    // 初始化岩层分布图表
    const initChart = () => {
      if (distributionChartInstance) {
        distributionChartInstance.dispose()
      }

      nextTick(() => {
        // 确保DOM元素存在再初始化
        if (!distributionChart.value) {
          console.warn('岩层分布图表DOM元素不存在')
          return
        }

        // 动态计算并设置图表高度
        const chartHeight = calculateChartHeight()
        lastCalculatedHeight = chartHeight // 记录初始高度
        distributionChart.value.style.height = chartHeight + 'px'

        distributionChartInstance = echarts.init(distributionChart.value)

        // 使用API返回的岩层分布数据
        const strataData = props.strataDistributionData || {};

        // 更新全局岩石强度阈值
        rockThresholds = {
          极坚固: typeof strataData.极坚固 === 'number' ? strataData.极坚固 : 25,
          很坚固: typeof strataData.很坚固 === 'number' ? strataData.很坚固 : 50,
          坚固: typeof strataData.坚固 === 'number' ? strataData.坚固 : 70,
          比较坚固: typeof strataData.比较坚固 === 'number' ? strataData.比较坚固 : 90,
          中等坚固: typeof strataData.中等坚固 === 'number' ? strataData.中等坚固 : 100,
          较软: typeof strataData.较软 === 'number' ? strataData.较软 : 130,
          空洞: typeof strataData.空洞 === 'number' ? strataData.空洞 : 150
        };

        // 这个对象将用于所有岩石强度判断，包括tooltip、y轴标签和visualMap配置
        // 它优先使用API返回的数据，如果数据缺失则使用默认值

        // 转换数据格式为图表需要的格式，并保存到本地数据副本
        const chartDataForDistribution = strataData && strataData.data && strataData.data.length > 0
          ? strataData.data.map(item => [item.depth, item.value])
          : []

        // 更新本地数据副本
        localChartData.value = JSON.parse(JSON.stringify(chartDataForDistribution))

        const option = {
          backgroundColor: 'transparent',
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross'
            },
            formatter: function (params) {
              const value = params[0].value[1]
              const rockHardness = getRockTypeByValue(value)

              return `深度: ${params[0].value[0]}m<br/>
                     ${params[0].marker} 钻进速度: ${value}cm/min<br/>
                     岩石类型: <b>${rockHardness}</b>`
            }
          },
          toolbox: {
            feature: {
              dataView: { readOnly: false },
              saveAsImage: {}
            }
          },
          legend: {
            data: ['钻进速度(cm/min)'],
            top: 10,
            left: 'center',
            icon: 'circle',
            textStyle: {
              fontSize: 12
            }
          },
          grid: {
            left: 30, // 增加左侧空间确保标签显示完整
            right: 150,
            top: 60,
            bottom: 60,
            containLabel: true
          },
          xAxis: {
            type: 'value',
            name: '钻孔深度(cm)',
            nameLocation: 'end',
            nameGap: 30,
            min: 0,
            max: function (value) {
              return Math.ceil(value.max * 1.1) // 最大值上浮10%
            },
            interval: 20,
            axisTick: {
              show: true,
              alignWithLabel: true
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: '#999'
              }
            },
            axisLabel: {
              formatter: '{value}',
              color: '#666',
              margin: 12
            },
            splitLine: {
              show: false,
              lineStyle: {
                type: 'dashed',
                color: '#E0E0E0'
              }
            }
          },
          yAxis: {
            type: 'value',
            name: '钻进速度(cm/min)',
            nameLocation: 'end',
            nameGap: 15,
            nameTextStyle: {
              padding: [0, 0, 0, -20], // 调整名称位置
              fontSize: 12,
              align: 'left',
              color: '#666'
            },
            min: 0,
            max: 150,
            axisLine: {
              show: true,
              lineStyle: {
                color: '#999'
              }
            },
            axisTick: {
              show: true
            },
            axisLabel: {
              formatter: function (value) {
                return getRockTypeByValue(value)
              }
            },
            splitLine: {
              show: false,
              lineStyle: {
                type: 'dashed',
                color: '#E0E0E0'
              }
            }
          },
          // 添加背景色区分不同岩石硬度区域 - 使用visualMap实现背景区分
          visualMap: {
            show: true,
            type: 'piecewise',
            dimension: 1, // 基于Y轴值进行映射
            orient: 'vertical',
            right: 10,
            top: 50,
            height: 450,
            pieces: [
              // 确保每个区间使用正确的阈值边界
              { min: 0, max: rockThresholds.极坚固, label: '极坚固', color: '#FF0100' },
              { min: rockThresholds.极坚固, max: rockThresholds.很坚固, label: '很坚固', color: '#FC7D02' },
              { min: rockThresholds.很坚固, max: rockThresholds.坚固, label: '坚固', color: '#FBDB1F' },
              { min: rockThresholds.坚固, max: rockThresholds.比较坚固, label: '比较坚固', color: '#93CE07' },
              { min: rockThresholds.比较坚固, max: rockThresholds.中等坚固, label: '中等坚固', color: '#E9D1FF' },
              { min: rockThresholds.中等坚固, max: rockThresholds.较软, label: '软岩', color: '#0006E1' },
              { min: rockThresholds.较软, max: rockThresholds.空洞, label: '空洞', color: '#999' }
            ],
            textStyle: {
                      fontSize: 14,
            color: '#333',
            fontWeight: 500
            },
            outOfRange: {
              color: '#FD0100'
            }
          },
          // 添加缩放工具，方便查看大量数据
          dataZoom: [
            {
              type: 'slider',
              xAxisIndex: 0,
              filterMode: 'filter',
              height: 20,
              start: 0,
              end: 100, // 默认显示所有数据
              handleIcon:
                'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7v-1.2h6.6V24.4z M13.3,22H6.7v-1.2h6.6V22z M13.3,19.6H6.7v-1.2h6.6V19.6z',
              handleSize: '80%',
              handleStyle: {
                color: '#fff',
                shadowBlur: 3,
                shadowColor: 'rgba(0, 0, 0, 0.6)',
                shadowOffsetX: 2,
                shadowOffsetY: 2
              }
            },
            {
              type: 'inside',
              xAxisIndex: 0,
              filterMode: 'filter',
              start: 0,
              end: 100
            }
          ],
          series: [
            {
              name: '钻进速度(cm/min)',
              type: 'line',
              smooth: true,
              sampling: 'lttb',
              symbol: 'circle',
              symbolSize: 2.5,
              showSymbol: true, // 显示数据点符号，便于点击
              lineStyle: {
                width: 1.5
              },
              emphasis: {
                focus: 'series',
                itemStyle: {
                  borderWidth: 2,
                  shadowBlur: 10
                },
                symbolSize: 8
              },
              data: localChartData.value,
              animationDuration: 1000,
              markPoint: {
                data: [
                  { type: 'max', name: 'Max' },
                  { type: 'min', name: 'Min' }
                ]
              },
              markLine: {
                silent: true,
                lineStyle: {
                  color: '#333',
                  type: 'dashed',
                  width: 1
                },
                data: [
                  { yAxis: rockThresholds.极坚固 },
                  { yAxis: rockThresholds.很坚固 },
                  { yAxis: rockThresholds.坚固 },
                  { yAxis: rockThresholds.比较坚固 },
                  { yAxis: rockThresholds.中等坚固 },
                  { yAxis: rockThresholds.较软 }
                ]
              }
            }
          ]
        }

        distributionChartInstance.setOption(option)

        // 添加点击事件监听
        distributionChartInstance.on('click', handleChartClick)

        // 触发一次 resize 事件以确保渲染完整
        setTimeout(() => {
          if (distributionChartInstance) {
            distributionChartInstance.resize()
          }
        }, 200)

        // 额外添加一个延迟的resize，防止初始渲染问题
        setTimeout(() => {
          if (distributionChartInstance) {
            distributionChartInstance.resize()
          }
        }, 500)
      })
    }

    // 监听数据变化
    watch(() => props.strataDistributionData, () => {
      nextTick(initChart)
    }, { deep: true })

    // 组件挂载时初始化图表
    onMounted(() => {
      // 延迟初始化，确保DOM完全渲染和父容器尺寸确定
      setTimeout(() => {
        initChart()
      }, 100)

      // 再次延迟初始化，处理可能的异步布局变化
      setTimeout(() => {
        if (distributionChartInstance) {
          const newHeight = calculateChartHeight()
          if (distributionChart.value) {
            distributionChart.value.style.height = newHeight + 'px'
            distributionChartInstance.resize()
          }
        }
      }, 500)

      // 添加窗口resize监听
      window.addEventListener('resize', handleResize)

      // 添加ResizeObserver监听容器尺寸变化（用于模板模式）
      if (window.ResizeObserver) {
        setTimeout(() => {
          if (distributionChart.value) {
            const resizeObserver = new ResizeObserver(() => {
              handleResize()
            })

            // 只观察外层容器，避免监听图表本身导致无限循环
            const cardElement = distributionChart.value.parentElement?.parentElement // el-card
            const outerElement = cardElement?.parentElement // 外层容器

            // 优先监听外层容器，如果没有则监听card容器
            const targetElement = outerElement || cardElement

            if (targetElement) {
              resizeObserver.observe(targetElement)
            }

            // 在组件卸载时清理observer
            onUnmounted(() => {
              resizeObserver.disconnect()
            })
          }
        }, 200)
      }
    })

    // 组件卸载前清理
    onUnmounted(() => {
      window.removeEventListener('resize', handleResize)

      // 清理防抖定时器
      if (resizeTimer) {
        clearTimeout(resizeTimer)
        resizeTimer = null
      }

      if (distributionChartInstance) {
        distributionChartInstance.dispose()
        distributionChartInstance = null
      }
    })

    // 处理窗口大小变化
    const handleResize = () => {
      // 清除之前的定时器，实现防抖
      if (resizeTimer) {
        clearTimeout(resizeTimer)
      }

      resizeTimer = setTimeout(() => {
        if (distributionChartInstance && distributionChart.value) {
          // 重新计算图表高度以适应容器变化
          const newHeight = calculateChartHeight()

          // 只有当高度变化超过10px时才更新，避免无限循环
          if (Math.abs(newHeight - lastCalculatedHeight) > 10) {
            lastCalculatedHeight = newHeight
            distributionChart.value.style.height = newHeight + 'px'

            // 延迟执行resize，确保DOM更新完成
            setTimeout(() => {
              if (distributionChartInstance) {
                distributionChartInstance.resize()
              }
            }, 100)
          }
        }
      }, 150) // 150ms防抖延迟
    }

    return {
      distributionChart,
      editDialogVisible,
      editForm,
      getRockTypeByValue,
      getRockTypeTagType,
      handleDialogClose,
      handleSaveEdit,
      updateSingleDataPoint
    }
  }
}
</script>

<style scoped>
.sub-card {
  margin-bottom: 20px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08) !important;
  border-radius: 12px;
  border: none;
  position: relative;
  overflow: hidden;
  background: #fff;
}

.sub-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #409eff, #67c23a);
  z-index: 1;
}

.rock-property-card :deep(.el-card__header) {
  padding: 18px 24px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  background: rgba(255, 255, 255, 0.6);
}

.sub-card-header {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  position: relative;
}

.sub-card-header span:first-child {
  position: relative;
}

.sub-card-header span:first-child::after {
  content: "";
  position: absolute;
  bottom: -6px;
  left: 0;
  width: 40px;
  height: 3px;
  background-color: #409eff;
  border-radius: 3px;
}

.distribution-chart {
  width: 100%;
}

/* 编辑对话框样式 */
.unit-text {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style> 
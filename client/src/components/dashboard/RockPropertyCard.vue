<template>
  <el-card
    class="rock-property-card"
    shadow="hover"
  >
    <template #header>
      <div class="sub-card-header">
        岩石性质
      </div>
    </template>
    <div class="wear-chart-container">
      <div
        ref="wearChart"
        class="wear-chart"
      />
    </div>
  </el-card>
</template>

<script>
import { ref, onMounted, watch, nextTick, onUnmounted } from 'vue'
import * as echarts from 'echarts'

export default {
  name: 'RockPropertyCard',
  props: {
    rockyNatureData: {
      type: Array,
      default: () => []
    }
  },
  emits: [],
  setup(props) {
    const wearChart = ref(null)
    let wearChartInstance = null
    let lastCalculatedHeight = 0 // 记录上次计算的高度，避免无限循环
    let resizeTimer = null // 防抖定时器
    let allTimers = [] // 存储所有定时器

    // 计算图表容器的最佳高度 - 参考 DrillCurveChartCard、Drilling3DAnalysisCard、StrataDistributionCard 和 DataTreeChartCard 的实现
    const calculateChartHeight = () => {
      if (!wearChart.value) {
        return 450 // 默认高度
      }

      // 获取父容器的实际高度 - 使用 el-card 作为参考容器
      const cardBodyElement = wearChart.value.parentElement?.parentElement // el-card__body (跳过 wear-chart-container)
      const cardElement = cardBodyElement?.parentElement // el-card

      // 使用 el-card 作为参考容器（第二层）
      const referenceElement = cardElement

      // 1. 当父级高度没有设置时，使用默认高度
      if (!referenceElement || referenceElement.offsetHeight <= 100) {
        return 450 // 父级高度没有设置时的默认高度
      }

      // 2. 当父级高度设置了时，按照父级高度自动调整，不使用默认高度
      const parentHeight = referenceElement.offsetHeight

      // 3. 获取实际的header高度
      const headerElement = cardElement?.querySelector('.el-card__header')
      const actualHeaderHeight = headerElement ? headerElement.offsetHeight : 80

      // 3. 获取实际的padding（el-card__body的padding）
      const cardBodyStyles = window.getComputedStyle(cardBodyElement)
      const paddingTop = parseInt(cardBodyStyles.paddingTop) || 0
      const paddingBottom = parseInt(cardBodyStyles.paddingBottom) || 0
      const actualPadding = paddingTop + paddingBottom

      // 计算图表区域可用高度：完全按照父级容器调整
      const availableHeight = parentHeight - actualHeaderHeight - actualPadding

      // 如果计算出的高度太小（可能是DOM还没完全渲染），使用默认高度
      if (availableHeight < 300) {
        return 450 // 使用默认高度，而不是强制使用计算出的小值
      }

      return availableHeight
    }

    // 初始化磨损度图表
    const initChart = () => {
      if (wearChartInstance) {
        wearChartInstance.dispose()
      }

      // 确保DOM元素存在再初始化
      if (!wearChart.value) {
        console.warn('磨损度图表DOM元素不存在')
        return
      }

      // 动态计算并设置图表高度
      const chartHeight = calculateChartHeight()
      lastCalculatedHeight = chartHeight // 记录初始高度
      wearChart.value.style.height = chartHeight + 'px'

      wearChartInstance = echarts.init(wearChart.value)
      
      // 使用传入的岩石性质数据
      const data = props.rockyNatureData && props.rockyNatureData.length > 0
        ? props.rockyNatureData.map(item => ({
            value: item.value,
            name: item.name,
            itemStyle: { color: item.name === '极坚固' ? '#5470C6' : 
                              item.name === '很坚固' ? '#91CC75' : 
                              item.name === '坚固' ? '#FAC858' : 
                              item.name === '比较坚固' ? '#EE6666' : 
                              item.name === '中等坚固' ? '#73C0DE' : '#3BA272'}
          }))
        : [
          { value: 0, name: '无数据', itemStyle: { color: '#C0C0C0' } }
        ]

      const maxIndex = data.reduce(
        (maxIdx, item, idx, arr) => (item.value > arr[maxIdx].value ? idx : maxIdx),
        0
      )

      const option = {
        legend: {
          orient: 'vertical', // 保持垂直排列
          bottom: '10%', // 调整到底部但留出一些空间
          left: 'center', // 水平居中
          itemWidth: 12,
          itemHeight: 12,
          itemGap: 5, // 图例项之间的间距
          textStyle: {
            color: '#606266',
            fontSize: 12 // 减小字体大小
          },
          formatter: function (name) {
            let value = data.find(item => item.name === name).value
            return name + ' ' + value + '%'
          }
        },
        series: [
          {
            type: 'pie',
            radius: ['40%', '60%'], // 减小饼图半径
            center: ['50%', '35%'], // 将饼图中心点上移
            avoidLabelOverlap: false,
            label: {
              show: false
            },
            emphasis: {
              label: {
                show: false
              }
            },
            labelLine: {
              show: false
            },
            data: data,
            itemStyle: {
              borderRadius: 5,
              borderColor: '#fff',
              borderWidth: 1
            }
          }
        ],
        backgroundColor: 'transparent',
        graphic: {
          elements: [
            {
              type: 'text',
              left: 'center',
              top: '25%', // 调整文字位置
              style: {
                text: data[maxIndex].name,
                textAlign: 'center',
                fill: '#909399',
                fontSize: 14
              }
            },
            {
              type: 'text',
              left: 'center',
              top: '33%', // 调整文字位置
              style: {
                text: data[maxIndex].value + '%',
                textAlign: 'center',
                fill: '#303133',
                fontSize: 24,
                fontWeight: 'bold'
              }
            }
          ]
        }
      }

      wearChartInstance.setOption(option)

      // 监听鼠标事件来更新中心文字 - 确保 top 值与 graphic 配置一致
      wearChartInstance.on('mouseover', { seriesIndex: 0 }, params => {
        wearChartInstance.setOption({
          graphic: {
            elements: [
              {
                type: 'text',
                left: 'center',
                top: '25%', // 与上方保持一致
                style: {
                  text: params.data.name,
                  textAlign: 'center',
                  fill: '#909399',
                  fontSize: 14
                }
              },
              {
                type: 'text',
                left: 'center',
                top: '33%', // 与上方保持一致
                style: {
                  text: params.data.value + '%',
                  textAlign: 'center',
                  fill: '#303133',
                  fontSize: 24,
                  fontWeight: 'bold'
                }
              }
            ]
          }
        })
      })

      // 鼠标移出时恢复显示最大值 - 确保 top 值与 graphic 配置一致
      wearChartInstance.on('mouseout', { seriesIndex: 0 }, () => {
        wearChartInstance.setOption({
          graphic: {
            elements: [
              {
                type: 'text',
                left: 'center',
                top: '25%', // 与上方保持一致
                style: {
                  text: data[maxIndex].name,
                  textAlign: 'center',
                  fill: '#909399',
                  fontSize: 14
                }
              },
              {
                type: 'text',
                left: 'center',
                top: '33%', // 与上方保持一致
                style: {
                  text: data[maxIndex].value + '%',
                  textAlign: 'center',
                  fill: '#303133',
                  fontSize: 24,
                  fontWeight: 'bold'
                }
              }
            ]
          }
        })
      })
    }

    // 监听数据变化
    watch(() => props.rockyNatureData, () => {
      nextTick(initChart)
    }, { deep: true })

    // 组件挂载时初始化图表
    onMounted(() => {
      // 立即设置初始高度，确保默认高度生效
      nextTick(() => {
        if (wearChart.value) {
          wearChart.value.style.height = '450px'
        }
      })

      // 延迟初始化，确保DOM完全渲染和父容器尺寸确定
      const timer1 = setTimeout(() => {
        initChart()
      }, 100)
      allTimers.push(timer1)

      // 再次延迟初始化，处理可能的异步布局变化
      const timer2 = setTimeout(() => {
        if (wearChartInstance) {
          const newHeight = calculateChartHeight()
          if (wearChart.value) {
            wearChart.value.style.height = newHeight + 'px'
            wearChartInstance.resize()
          }
        }
      }, 500)
      allTimers.push(timer2)

      // 添加窗口resize监听
      window.addEventListener('resize', handleResize)

      // 添加ResizeObserver监听容器尺寸变化（用于模板模式）
      if (window.ResizeObserver) {
        const timer3 = setTimeout(() => {
          if (wearChart.value) {
            const resizeObserver = new ResizeObserver(() => {
              handleResize()
            })

            // 只观察外层容器，避免监听图表本身导致无限循环
            const cardElement = wearChart.value.parentElement?.parentElement?.parentElement // el-card (跳过 wear-chart-container)
            const outerElement = cardElement?.parentElement // 外层容器

            // 优先监听外层容器，如果没有则监听card容器
            const targetElement = outerElement || cardElement

            if (targetElement) {
              resizeObserver.observe(targetElement)
            }

            // 在组件卸载时清理observer
            onUnmounted(() => {
              resizeObserver.disconnect()
            })
          }
        }, 200)
        allTimers.push(timer3)
      }
    })

    // 组件卸载前清理
    onUnmounted(() => {
      window.removeEventListener('resize', handleResize)

      // 清理防抖定时器
      if (resizeTimer) {
        clearTimeout(resizeTimer)
        resizeTimer = null
      }

      // 清理所有定时器
      allTimers.forEach(timer => {
        clearTimeout(timer)
      })
      allTimers = []

      if (wearChartInstance) {
        wearChartInstance.dispose()
        wearChartInstance = null
      }
    })

    // 处理窗口大小变化
    const handleResize = () => {
      // 清除之前的定时器，实现防抖
      if (resizeTimer) {
        clearTimeout(resizeTimer)
      }

      resizeTimer = setTimeout(() => {
        if (wearChartInstance && wearChart.value) {
          // 重新计算图表高度以适应容器变化
          const newHeight = calculateChartHeight()

          // 只有当高度变化超过10px时才更新，避免无限循环
          if (Math.abs(newHeight - lastCalculatedHeight) > 10) {
            lastCalculatedHeight = newHeight
            wearChart.value.style.height = newHeight + 'px'

            // 延迟执行resize，确保DOM更新完成
            const timer = setTimeout(() => {
              if (wearChartInstance) {
                wearChartInstance.resize()
              }
            }, 100)
            allTimers.push(timer)
          }
        }
      }, 150) // 150ms防抖延迟
    }

    return {
      wearChart
    }
  }
}
</script>

<style scoped>
.rock-property-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08) !important;
  border-radius: 12px;
  border: none;
  position: relative;
  overflow: hidden;
  background: #fff;
}

.rock-property-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #409eff, #67c23a);
  z-index: 1;
}

.rock-property-card :deep(.el-card__header) {
  padding: 18px 24px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  background: rgba(255, 255, 255, 0.6);
}

.sub-card-header {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  position: relative;
  display: flex;
  align-items: center;
}

.sub-card-header::after {
  content: "";
  position: absolute;
  bottom: -6px;
  left: 0;
  width: 40px;
  height: 3px;
  background-color: #409eff;
  border-radius: 3px;
}

.wear-chart-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.wear-chart {
  width: 100%;
}
</style> 
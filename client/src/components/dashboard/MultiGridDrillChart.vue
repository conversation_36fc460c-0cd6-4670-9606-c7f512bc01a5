<template>
  <div class="multi-grid-drill-chart">
    <div
      ref="chartRef"
      class="chart-container"
    />
  </div>
</template>

<script setup lang="ts">
/**
 * 多网格钻进图表组件
 * 支持实时数据更新和多种钻进参数的可视化
 */

import { ref, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

// 钻进数据项类型
interface DrillingDataItem {
  collectionAt: string
  dpth?: number
  advncSpd?: number
  rtnTq?: number
  wtrPrsH?: number
  frcstKn?: number
  rtnSpd?: number
  isStuckPoint?: boolean
  isMutationPoint?: boolean
  [key: string]: unknown
}

// 定义props - 支持传入单个数据点或数组列表
interface Props {
  latestData?: DrillingDataItem | null    // 单个最新数据点
  drillingData?: DrillingDataItem[]       // 数组列表（用于初始化或批量更新）
}

const props = withDefaults(defineProps<Props>(), {
  latestData: () => null,
  drillingData: () => []
})

// 图表相关
const chartRef = ref<HTMLDivElement>()
const chartInstance = ref<echarts.ECharts>()

// 内部维护的历史数据列表
const internalData = ref<DrillingDataItem[]>([])

// 添加新数据点到内部数据列表
const addDataPoint = (newData: DrillingDataItem | null) => {
  if (!newData) return
  // 添加新数据点
  internalData.value.push(newData)

  // 保持最近50个数据点
  if (internalData.value.length > 50) {
    internalData.value = internalData.value.slice(-50)
  }
}

// 批量设置数据列表
const setDataList = (dataList: DrillingDataItem[]) => {
  if (!Array.isArray(dataList)) return

  // 直接替换内部数据，保持最近50个数据点
  internalData.value = dataList.slice(-50)
}

// 参数配置 - 匹配原始组件的5个参数，使用统一绿色
const paramConfigs = [
  { name: '钻进速度 m/min', unit: 'm/min', field: 'advncSpd', color: '#389E0D' },
  { name: '旋转扭矩 kN·m', unit: 'kN·m', field: 'rtnTq', color: '#389E0D' },
  { name: '水压力 bar', unit: 'bar', field: 'wtrPrsH', color: '#389E0D' },
  { name: '旋转速度 r/min', unit: 'r/min', field: 'rtnSpd', color: '#389E0D' },
  { name: '推进力 kN', unit: 'kN', field: 'frcstKn', color: '#389E0D' }
]

// 初始化图表实例（只在组件挂载时调用）
const initChartInstance = () => {
  if (!chartRef.value) return

  // 销毁现有图表实例
  if (chartInstance.value) {
    chartInstance.value.dispose()
  }

  // 创建新的图表实例
  chartInstance.value = echarts.init(chartRef.value)
}

// 更新图表数据（保持图表实例，只更新配置）
const updateChart = () => {
  if (!chartInstance.value) return

  // 处理数据 - 使用内部维护的历史数据
  const data = internalData.value
  const timeData: string[] = []
  const paramData: { [key: string]: number[] } = {}

  // 初始化参数数据数组
  paramConfigs.forEach(config => {
    paramData[config.field] = []
  })

  // 处理每个数据点
  data.forEach((item: any) => {
    // 使用采集时间作为X轴
    const timeValue = item.collectionAt || item.Collection_At || new Date().toISOString()
    timeData.push(timeValue)

    // 处理各个参数字段 - 兼容不同的字段名格式
    paramConfigs.forEach(config => {
      // 支持驼峰和下划线两种格式，优先使用实际MQTT数据中的字段名
      let value = 0
      if (config.field === 'advncSpd') {
        value = Number(item.Advnc_Spd || item.advncSpd) || 0
      } else if (config.field === 'rtnTq') {
        value = Number(item.Rtn_Tq || item.rtnTq) || 0
      } else if (config.field === 'wtrPrsH') {
        value = Number(item.Wtr_Prs_H || item.wtrPrsH) || 0
      } else if (config.field === 'rtnSpd') {
        value = Number(item.Rtn_Spd || item.rtnSpd) || 0
      } else if (config.field === 'frcstKn') {
        value = Number(item.Frcst_kN || item.frcstKn) || 0
      }
      // 存储为简单数值数组，与DrillCurveChartCard.vue保持一致
      paramData[config.field].push(value)
    })
  })

  // 计算网格布局 - 匹配原始组件的配置
  const gridCount = paramConfigs.length
  const topMargin = 100  // 顶部边距，为标题和图例留出空间
  const bottomMargin = 30  // 底部边距，为数据缩放控制器留出空间
  const gridGap = 30  // 网格之间的间距
  const totalHeight = 750  // 默认总高度，匹配原始组件
  const availableHeight = totalHeight - topMargin - bottomMargin
  const gridHeight = Math.floor((availableHeight - (gridCount - 1) * gridGap) / gridCount)

  // 创建网格、轴和系列配置
  const grids: any[] = []
  const xAxes: any[] = []
  const yAxes: any[] = []
  const series: any[] = []

  // 收集卡钻和突进点的索引
  const stuckPointIndices = new Set<number>()
  const upMutationPointIndices = new Set<number>()
  const downMutationPointIndices = new Set<number>()

  // 遍历数据，找出卡钻和突进点的索引
  data.forEach((item: any, index: number) => {
    if (item.isStuckPoint) {
      stuckPointIndices.add(index)
    }
    if (item.isUpMutation) {
      upMutationPointIndices.add(index)
    }
    if (item.isDownMutation) {
      downMutationPointIndices.add(index)
    }
  })

  // 为每个参数创建独立的网格
  paramConfigs.forEach((config, index) => {
    const gridTop = topMargin + index * (gridHeight + gridGap)

    // 网格配置 - 匹配原始组件
    grids.push({
      top: gridTop,
      height: gridHeight,
      left: 60,  // 留出Y轴标签的空间
      right: 20
    })

    // X轴配置 - 参考DrillCurveChartCard.vue，使用category类型
    xAxes.push({
      type: 'category',
      gridIndex: index,
      position: 'bottom',
      data: timeData, // 使用时间数据作为category数据
      axisLabel: index === gridCount - 1 ? {
        color: '#333', // 深色标签，适合白色背景
        fontSize: 11,
        formatter: (value: any) => {
          const date = new Date(value)
          return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`
        }
      } : { show: false },
      axisLine: {
        show: index === gridCount - 1,
        lineStyle: { color: '#333' }
      },
      axisTick: {
        show: index === gridCount - 1,
        lineStyle: { color: '#333' }
      },
      splitLine: { show: false },
      axisPointer: {
        label: {
          backgroundColor: '#ccc',
          borderColor: '#aaa',
          borderWidth: 1,
          shadowBlur: 0,
          shadowOffsetX: 0,
          shadowOffsetY: 0,
          formatter: (function(currentConfig, currentData, xAxisData) {
            return function(params: any) {
              let dataIndex = -1;

              if (params.seriesData && params.seriesData.length > 0) {
                for (let i = 0; i < params.seriesData.length; i++) {
                  const seriesItem = params.seriesData[i];
                  if (seriesItem.dataIndex !== undefined && seriesItem.dataIndex >= 0) {
                    dataIndex = seriesItem.dataIndex;
                    break;
                  }
                }
              }

              if (dataIndex === -1) {
                if (params.dataIndex !== undefined && params.dataIndex >= 0 && params.dataIndex < xAxisData.length) {
                  dataIndex = params.dataIndex;
                } else {
                  const currentXValue = params.value;
                  for (let i = 0; i < xAxisData.length; i++) {
                    if (String(xAxisData[i]) === String(currentXValue)) {
                      dataIndex = i;
                      break;
                    }
                  }
                }
              }

              if (dataIndex === -1 || dataIndex >= xAxisData.length) {
                return '';
              }

              const value = currentData[dataIndex];
              const formattedValue = value !== null && value !== undefined ?
                (typeof value === 'number' ? value.toFixed(2) : value) :
                '无数据';

              return `${currentConfig.name}: ${formattedValue}`;
            }
          })(config, paramData[config.field], timeData)
        }
      }
    })

    // Y轴配置 - 匹配原始组件样式（白色背景）
    yAxes.push({
      type: 'value',
      gridIndex: index,
      name: config.name,
      nameLocation: 'middle',
      nameGap: 45,
      nameTextStyle: {
        color: config.color, // 使用参数颜色作为Y轴标题颜色
        fontSize: 12,
        fontWeight: 'bold',
        padding: [0, 0, 0, 0]
      },
      axisLabel: {
        color: config.color, // 使用参数颜色作为Y轴标签颜色
        fontSize: 11,
        formatter: '{value}'
      },
      axisLine: {
        show: true,
        lineStyle: { color: config.color } // 使用参数颜色作为Y轴线颜色
      },
      axisTick: {
        show: true,
        lineStyle: { color: config.color }
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(233, 233, 233, 0.5)', // 匹配原组件的网格线颜色
          type: 'dashed'
        }
      }
    })

    // 创建标记点数据
    const markPointData: any[] = []

    // 只在第一个网格（最上面的图表）添加标记点，避免重复显示
    if (index === 0) {
      // 添加卡钻点标记
      stuckPointIndices.forEach(pointIndex => {
        const yValue = paramData[config.field][pointIndex]
        if (yValue !== null && yValue !== undefined) {
          markPointData.push({
            xAxis: pointIndex,
            yAxis: yValue,
            name: '卡钻点',
            value: '卡',
            itemStyle: {
              color: '#FF4D4F',
              borderColor: '#FFFFFF',
              borderWidth: 2
            },
            label: {
              show: true,
              position: 'inside',
              formatter: '卡',
              color: '#FFFFFF',
              fontWeight: 'bold',
              fontSize: 12
            }
          })
        }
      })

      // 添加上突进点标记
      upMutationPointIndices.forEach(pointIndex => {
        const yValue = paramData[config.field][pointIndex]
        if (yValue !== null && yValue !== undefined) {
          markPointData.push({
            xAxis: pointIndex,
            yAxis: yValue,
            name: '上突进点',
            value: '↑',
            itemStyle: {
              color: '#FF4D4F',
              borderColor: '#FFFFFF',
              borderWidth: 2
            },
            label: {
              show: true,
              position: 'inside',
              formatter: '↑',
              color: '#FFFFFF',
              fontWeight: '900',
              fontSize: 16
            }
          })
        }
      })

      // 添加下突进点标记
      downMutationPointIndices.forEach(pointIndex => {
        const yValue = paramData[config.field][pointIndex]
        if (yValue !== null && yValue !== undefined) {
          markPointData.push({
            xAxis: pointIndex,
            yAxis: yValue,
            name: '下突进点',
            value: '↓',
            itemStyle: {
              color: '#FF4D4F',
              borderColor: '#FFFFFF',
              borderWidth: 2
            },
            label: {
              show: true,
              position: 'inside',
              formatter: '↓',
              color: '#FFFFFF',
              fontWeight: '900',
              fontSize: 16
            }
          })
        }
      })
    }

    // 系列配置 - 参考DrillCurveChartCard.vue，使用简单数值数组
    const seriesConfig: any = {
      name: config.name,
      type: 'line',
      xAxisIndex: index,
      yAxisIndex: index,
      data: paramData[config.field], // 现在是简单的数值数组
      showSymbol: false,
      connectNulls: true,
      lineStyle: {
        color: config.color,
        width: 2
      },
      itemStyle: {
        color: config.color
      },
      // 添加面积填充，匹配原始组件
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0,
            color: config.color + '40' // 40%透明度
          }, {
            offset: 1,
            color: config.color + '10' // 10%透明度
          }]
        }
      },
      smooth: false
    }

    // 如果有标记点数据，添加到系列配置中
    if (markPointData.length > 0) {
      seriesConfig.markPoint = {
        data: markPointData,
        symbol: 'pin', // 使用图钉样式
        symbolSize: [35, 45], // 标记点大小 [宽, 高]
        itemStyle: {
          shadowBlur: 8,
          shadowColor: 'rgba(255, 77, 79, 0.5)',
          shadowOffsetY: 2
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 12,
            shadowColor: 'rgba(255, 77, 79, 0.8)'
          }
        },
        z: 10 // 设置较高的层级，确保在其他系列之上
      }
    }

    series.push(seriesConfig)

    // 为卡钻点和突进点添加红色高亮系列
    if (stuckPointIndices.size > 0 || upMutationPointIndices.size > 0 || downMutationPointIndices.size > 0) {
      // 创建高亮点数据 - 卡钻点和突进点都显示为红色
      const highlightData = paramData[config.field].map((value: number, dataIndex: number) => {
        if (stuckPointIndices.has(dataIndex) || upMutationPointIndices.has(dataIndex) || downMutationPointIndices.has(dataIndex)) {
          return value
        }
        return null
      })

      // 添加红色高亮系列 - 参考DrillCurveChartCard.vue的设计
      series.push({
        name: `${config.name} (高亮)`, // 使用不同名称，不被图例控制
        type: 'line',
        xAxisIndex: index,
        yAxisIndex: index,
        data: highlightData,
        showSymbol: true,
        symbolSize: 8,
        connectNulls: false,
        lineStyle: {
          color: '#FF4D4F',
          width: 3
        },
        itemStyle: {
          color: '#FF4D4F',
          borderColor: '#FFFFFF',
          borderWidth: 2
        },
        z: 5, // 设置较高的层级，确保在普通曲线之上
        silent: false // 确保可以交互
      })
    }
  })

  // 图表配置 - 匹配原始组件（白色背景）
  const option = {
    // 不设置backgroundColor，使用默认白色背景，匹配原组件
    animation: false,
    title: {
      text: '钻进深度曲线',
      left: 'center',
      top: 10,
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#303133'
      }
    },
    legend: {
      data: paramConfigs.map(config => config.name),
      top: 40,
      left: 'center',
      textStyle: {
        fontSize: 12,
        color: '#606266'
      },
      // 确保图例交互功能启用
      selectedMode: true, // 启用图例选择模式
      inactiveColor: '#ccc' // 设置未选中时的颜色
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        animation: false
      },
      triggerOn: 'mousemove|click',
      confine: true,
      enterable: true,
      extraCssText: 'max-width: 250px;',
      // position: ['86%', 0], // 临时移除固定位置
      formatter: function(params: any) {
        if (!Array.isArray(params)) {
          params = [params];
        }

        const xValue = params[0]?.axisValue;
        if (!xValue) return '';

        const date = new Date(xValue);
        const timeStr = date.getHours().toString().padStart(2, '0') + ':' +
                      date.getMinutes().toString().padStart(2, '0') + ':' +
                      date.getSeconds().toString().padStart(2, '0');
        return `采集时间: ${timeStr}`;
      }
    },
    axisPointer: {
      link: {xAxisIndex: 'all'}
    },
    grid: grids,
    xAxis: xAxes,
    yAxis: yAxes,
    series: series
  }

  // 设置图表配置 - 使用merge模式保持图例状态
  chartInstance.value.setOption(option, false, true)
}

// 监听最新数据变化
watch(() => props.latestData, (newData: any) => {
  if (newData) {
    // 添加新数据点到内部列表
    addDataPoint(newData)
    // 更新图表数据（保持图例状态）
    nextTick(() => {
      updateChart()
    })
  }
}, { deep: true })

// 监听数组数据变化（用于批量更新或初始化）
watch(() => props.drillingData, (newDataList: any[]) => {
  if (newDataList && newDataList.length > 0) {
    // 批量设置数据列表
    setDataList(newDataList)
    // 更新图表数据（保持图例状态）
    nextTick(() => {
      updateChart()
    })
  }
}, { deep: true, immediate: true })

// 组件挂载
onMounted(() => {
  nextTick(() => {
    // 初始化图表实例
    initChartInstance()
    // 更新图表数据
    updateChart()
  })
})

// 组件卸载
onBeforeUnmount(() => {
  if (chartInstance.value) {
    chartInstance.value.dispose()
  }
})


</script>

<style scoped>
.multi-grid-drill-chart {
  width: 100%;
  height: 750px; /* 匹配原始组件的默认高度 */
  background: transparent; /* 使用透明背景，让父容器控制背景 */
  padding: 0; /* 移除内边距，让图表占满容器 */
}

.chart-container {
  width: 100%;
  height: 100%;
  /* 确保图表在白色背景下正确显示 */
  color: #303133;
}
</style>

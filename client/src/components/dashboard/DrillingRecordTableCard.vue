<template>
  <el-card
    class="drilling-record-card sub-card rock-property-card"
    shadow="hover"
  >
    <template #header>
      <div class="sub-card-header">
        <span>钻探记录表</span>
        <div class="header-controls">
          <el-select
            v-model="depthStep"
            placeholder="选择步长"
            size="small"
            style="width: 120px"
            clearable
            @change="handleDepthStepChange"
          >
            <el-option
              label="2cm"
              :value="2"
            />
            <el-option
              label="5cm"
              :value="5"
            />
            <el-option
              label="10cm"
              :value="10"
            />
          </el-select>
          <el-button
            size="small"
            type="primary"
            :icon="Download"
            :loading="downloadLoading"
            @click="downloadTableImage"
          >
            下载图片
          </el-button>
        </div>
      </div>
    </template>
    
    <!-- 数据为空时的提示 -->
    <div
      v-if="!drillingRecordData || drillingRecordData.length === 0"
      class="empty-state"
    >
      <el-empty description="暂无钻探记录数据" />
    </div>

    <div
      v-else
      class="record-table-container"
    >
      <!-- 表头信息 -->
      <div class="record-header">
        <div class="header-title">
          {{ projectInfo.tunnelName }}超前地质钻探记录表
        </div>
        
        <div class="header-info">
          <div class="info-row">
            <div class="info-item">
              <span class="label">工程名称</span>
              <span class="value">{{ projectInfo.tunnelName }}</span>
            </div>
            <div class="info-item">
              <span class="label">施做里程</span>
              <div class="value editable-field">
                <el-input
                  v-model="mileage"
                  placeholder="请输入施做里程"
                  size="small"
                />
              </div>
            </div>
            <div class="info-item">
              <span class="label">施工单位</span>
              <div class="value editable-field">
                <el-input
                  v-model="constructionUnit"
                  placeholder="请输入施工单位"
                  size="small"
                />
              </div>
            </div>
          </div>

          <div class="info-row">
            <div class="info-item">
              <span class="label">钻机类型</span>
              <span class="value">{{ projectInfo.deviceSn || '未指定' }}</span>
            </div>
            <div class="info-item">
              <span class="label">钻孔编号</span>
              <span class="value">{{ projectInfo.hlNum || '-' }}#</span>
            </div>
            <div class="info-item">
              <span class="label">孔倾角</span>
              <span class="value">{{ projectInfo.hlAng || '-' }}°</span>
            </div>
          </div>
          
          <div class="info-row">
            <div class="info-item">
              <span class="label">设计深度</span>
              <div class="value editable-field">
                <el-input
                  v-model="designDepth"
                  placeholder="请输入设计深度"
                  size="small"
                />
              </div>
            </div>
            <div class="info-item">
              <span class="label">实际深度</span>
              <span class="value">{{ projectInfo.actualDepth || '66' }}</span>
            </div>
          </div>
          
          <div class="info-row">
            <div class="info-item">
              <span class="label">开工日期</span>
              <div class="value editable-field">
                <el-input
                  v-model="startDate"
                  placeholder="请输入开工日期"
                  size="small"
                />
              </div>
            </div>
            <div class="info-item">
              <span class="label">完工日期</span>
              <div class="value editable-field">
                <el-input
                  v-model="endDate"
                  placeholder="请输入完工日期"
                  size="small"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 钻孔布置示意图和签名区域 - 移动到这里 -->
      <div class="additional-info-section">
        <!-- 钻孔布置示意图 -->
        <div class="diagram-row">
          <div class="diagram-section">
            <span class="diagram-label">钻孔布置示意图:</span>
            <div class="diagram-area">
              <div class="tunnel-diagram">
                <div class="tunnel-outline">
                  <!-- 12个方向的孔位标记 -->
                  <div
                    v-for="position in 12"
                    :key="position"
                    class="hole-position"
                    :class="{ 'active': isHoleActive(position) }"
                    :style="getHolePositionStyle(position)"
                  >
                    <div class="hole-dot" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 签名区域 -->
        <div class="signatures-row">
          <div class="signature-section">
            <div class="signature-item">
              <span class="signature-label">记录员:</span>
              <div class="signature-value editable-field">
                <el-input
                  v-model="recorder"
                  placeholder="请输入记录员姓名"
                  size="small"
                />
              </div>
            </div>
            <div class="signature-item">
              <span class="signature-label">地质工程师:</span>
              <div class="signature-value editable-field">
                <el-input
                  v-model="geologist"
                  placeholder="请输入地质工程师姓名"
                  size="small"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 数据表格 -->
      <div class="data-table-wrapper">
        <table class="record-table">
          <thead>
            <tr>
              <th
                rowspan="2"
                class="sequence-col"
              >
                序号
              </th>
              <th
                colspan="2"
                class="time-col"
              >
                作业时间
              </th>
              <th
                rowspan="2"
                class="depth-col"
              >
                钻进深度(cm)
              </th>
              <th
                rowspan="2"
                class="speed-col"
              >
                钻进速度(m/分)
              </th>
              <th
                rowspan="2"
                class="torque-col"
              >
                旋转扭矩(Nm)
              </th>
              <th
                rowspan="2"
                class="pressure-col"
              >
                水压力(bar)
              </th>
              <th
                rowspan="2"
                class="force-col"
              >
                推进力(kN)
              </th>
              <th
                rowspan="2"
                class="rotation-col"
              >
                旋转速度(rpm)
              </th>
              <th
                rowspan="2"
                class="water-col"
              >
                含水情况
              </th>
              <th
                rowspan="2"
                class="rock-col"
              >
                岩质强度
              </th>
              <th
                rowspan="2"
                class="description-col"
              >
                钻进特征及地质情况描述
              </th>
            </tr>
            <tr>
              <th class="start-time-col">
                开始
              </th>
              <th class="end-time-col">
                结束
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="(record, index) in processedRecords"
              :key="index"
              :class="{ 'stuck-event': record.isStuckPoint, 'mutation-event': record.isMutationPoint }"
            >
              <td class="sequence-col">
                {{ index + 1 }}
              </td>
              <td class="start-time-col">
                {{ formatTime(record.collectionAt) }}
              </td>
              <td class="end-time-col">
                {{ formatTime(getNextTime(record, index)) }}
              </td>
              <td class="depth-col">
                {{ formatNumber(record.dpth) }}
              </td>
              <td class="speed-col">
                {{ formatNumber(record.advncSpd, 2) }}
              </td>
              <td class="torque-col">
                {{ formatNumber(record.rtnTq, 1) }}
              </td>
              <td class="pressure-col">
                {{ formatNumber(record.wtrPrsH, 1) }}
              </td>
              <td class="force-col">
                {{ formatNumber(record.frcstKn, 1) }}
              </td>
              <td class="rotation-col">
                {{ formatNumber(record.rtnSpd, 0) }}
              </td>
              <td class="water-col">
                <!-- 含水情况 -->
                {{ record.waterCondition || '-' }}
              </td>
              <td class="rock-col">
                <!-- 岩质岩性 -->
                {{ record.rockStrengthDesc|| '未知岩性' }}
              </td>
              <td class="description-col">
                <!-- 钻进特征及地质情况描述 -->
                {{ record.geologicalDesc || '无软弱 无夹层 钻出正常' }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>



      <!-- 数据统计信息 -->
      <div class="data-summary">
        <span>共 {{ totalRecords }} 条记录</span>
        <span v-if="depthStep">（按 {{ depthStep }}cm 步长过滤）</span>
      </div>
    </div>
  </el-card>
</template>

<script>
import { computed, ref, watch } from 'vue'
import { Download } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import html2canvas from 'html2canvas'

export default {
  name: 'DrillingRecordTableCard',
  props: {
    drillingRecordData: {
      type: Array,
      default: () => [],
      validator: (value) => {
        // 验证数组中的每个元素是否包含必要字段
        if (!Array.isArray(value)) return false
        if (value.length === 0) return true // 空数组是有效的

        // 基础必需字段
        const requiredFields = ['collectionAt', 'deviceSn', 'dpth', 'advncSpd', 'rtnTq', 'wtrPrsH', 'frcstKn', 'rtnSpd']

        return value.every(item => {
          if (!item || typeof item !== 'object') return false

          // 检查必需字段
          const hasRequiredFields = requiredFields.every(field => field in item)
          if (!hasRequiredFields) return false

          // 检查记录表字段类型（如果存在）
          if ('isStuckPoint' in item && typeof item.isStuckPoint !== 'boolean') return false
          if ('isMutationPoint' in item && typeof item.isMutationPoint !== 'boolean') return false
          if ('waterCondition' in item && typeof item.waterCondition !== 'string') return false
          if ('rockGradeDesc' in item && typeof item.rockGradeDesc !== 'string') return false
          if ('geologicalDesc' in item && typeof item.geologicalDesc !== 'string') return false

          // 检查数值字段的合理性
          if ('dpth' in item && (isNaN(Number(item.dpth)) || Number(item.dpth) < 0)) return false
          if ('advncSpd' in item && (isNaN(Number(item.advncSpd)) || Number(item.advncSpd) < 0)) return false
          if ('rtnTq' in item && (isNaN(Number(item.rtnTq)) || Number(item.rtnTq) < 0)) return false
          if ('wtrPrsH' in item && (isNaN(Number(item.wtrPrsH)) || Number(item.wtrPrsH) < 0)) return false
          if ('frcstKn' in item && (isNaN(Number(item.frcstKn)) || Number(item.frcstKn) < 0)) return false
          if ('rtnSpd' in item && (isNaN(Number(item.rtnSpd)) || Number(item.rtnSpd) < 0)) return false

          return true
        })
      }
    },

  },
  setup(props) {
    // 步长过滤状态
    const depthStep = ref(null) // 选择的步长，默认为null（不过滤）

    // 可编辑字段状态 - 仅用于页面显示，不传递到外部，默认为空
    const constructionUnit = ref('') // 施工单位
    const mileage = ref('K1+') // 施做里程
    const designDepth = ref('') // 设计深度
    const startDate = ref('') // 开工日期
    const endDate = ref('') // 完工日期
    const recorder = ref('') // 记录员
    const geologist = ref('') // 地质工程师

    // 优化后的数据处理逻辑 - 先定义，避免循环依赖
    const processedRecords = computed(() => {
      const data = props.drillingRecordData || []
      if (!data.length) return []

      let filteredData = data

      // 如果有步长过滤，应用过滤逻辑
      if (depthStep.value) {
        const step = depthStep.value
        const depthMap = new Map()

        // 按深度分组，每个步长区间保留最新的数据
        data.forEach(item => {
          const depth = parseFloat(item.dpth) || 0
          const stepIndex = Math.floor(depth / step)
          const stepKey = stepIndex * step

          if (!depthMap.has(stepKey) ||
              new Date(item.collectionAt) > new Date(depthMap.get(stepKey).collectionAt)) {
            depthMap.set(stepKey, item)
          }
        })

        filteredData = Array.from(depthMap.values())
      }

      // 按时间排序（只排序一次）
      return filteredData.sort((a, b) => {
        const timeA = new Date(a.collectionAt).getTime()
        const timeB = new Date(b.collectionAt).getTime()
        return timeA - timeB
      })
    })

    // 总记录数
    const totalRecords = computed(() => processedRecords.value.length)

    // 计算项目信息 - 基于 processedRecords 的结果
    const projectInfo = computed(() => {
      const data = processedRecords.value
      if (!data || data.length === 0) {
        return {
          tunnelName: '',
          deviceSn: '',
          hlNum: '',
          hlAng: '',
          designDepth: '',
          actualDepth: '',
          startDate: '',
          endDate: ''
        }
      }

      const firstItem = data[0]
      const lastItem = data[data.length - 1]

      // 计算实际深度（cm转m）
      const firstDepth = parseFloat(firstItem.dpth || 0)
      const lastDepth = parseFloat(lastItem.dpth || 0)
      const actualDepthM = ((lastDepth - firstDepth) / 100).toFixed(1)

      return {
        tunnelName: firstItem.tunnelName || '',
        deviceSn: firstItem.deviceSn || '',
        hlNum: firstItem.hlNum || '',
        hlAng: firstItem.hlAng || '',
        designDepth: firstItem.designDepth || '', // 使用数据中的设计深度，如果没有则为空
        actualDepth: actualDepthM,
        startDate: formatDateTime(firstItem.collectionAt, 'date'),
        endDate: formatDateTime(lastItem.collectionAt, 'date')
      }
    })

    // 通用日期时间格式化函数
    const formatDateTime = (dateStr, format = 'time') => {
      if (!dateStr || typeof dateStr !== 'string') return ''
      try {
        const date = new Date(dateStr)
        if (isNaN(date.getTime())) return ''

        // 检查日期是否在合理范围内（1900-2100年）
        const year = date.getFullYear()
        if (year < 1900 || year > 2100) return ''

        if (format === 'time') {
          const hours = date.getHours()
          const minutes = date.getMinutes()
          const seconds = date.getSeconds()
          return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
        } else if (format === 'date') {
          const year = date.getFullYear()
          const month = date.getMonth() + 1
          const day = date.getDate()
          return `${year}.${month.toString().padStart(2, '0')}.${day.toString().padStart(2, '0')}`
        }
        return ''
      } catch (error) {
        console.warn('日期时间格式化错误:', dateStr, error)
        return ''
      }
    }

    // 格式化时间显示（精确到秒）
    const formatTime = (timeStr) => formatDateTime(timeStr, 'time')

    // 安全的数值格式化函数
    const formatNumber = (value, decimals = 1) => {
      if (value === null || value === undefined || value === '') return '-'

      const num = Number(value)
      if (isNaN(num)) return '-'

      // 检查数值是否在合理范围内
      if (num < -999999 || num > 999999) return '-'

      return num.toFixed(decimals)
    }

    // 获取下一个时间点 - 改进逻辑
    const getNextTime = (record, index) => {
      const nextRecord = processedRecords.value[index + 1]
      if (nextRecord) {
        return nextRecord.collectionAt
      }

      // 如果没有下一条记录，估算结束时间（当前时间 + 平均间隔）
      const records = processedRecords.value
      if (records.length > 1) {
        const currentTime = new Date(record.collectionAt).getTime()
        const prevTime = new Date(records[index - 1]?.collectionAt || record.collectionAt).getTime()
        const interval = currentTime - prevTime
        return new Date(currentTime + interval).toISOString()
      }

      // 如果只有一条记录，假设持续1分钟
      const currentTime = new Date(record.collectionAt).getTime()
      return new Date(currentTime + 60000).toISOString()
    }
    // 处理步长变化
    const handleDepthStepChange = (val) => {
      depthStep.value = val
    }
    // 获取孔位在圆形上的位置样式
    const getHolePositionStyle = (position) => {
      // 将1-12点位置转换为角度（1点为30度，12点为0度，顺时针）
      // position 1 对应 1点方向（30度），position 12 对应 12点方向（0度）
      let angle
      if (position === 12) {
        angle = 0 // 12点在顶部（0度）
      } else {
        angle = position * 30 // 1点=30度，2点=60度，...，11点=330度
      }

      // 转换为数学坐标系（减90度，因为CSS中0度是右侧，我们要12点在顶部）
      const adjustedAngle = angle - 90
      const radian = (adjustedAngle * Math.PI) / 180
      const radius = 45 // 圆的半径百分比

      // 计算x, y坐标（以圆心为原点）
      const x = 50 + radius * Math.cos(radian) // 50%为圆心x坐标
      const y = 50 + radius * Math.sin(radian) // 50%为圆心y坐标

      return {
        left: `${x}%`,
        top: `${y}%`,
        transform: 'translate(-50%, -50%)'
      }
    }

    // 缓存孔编号解析结果，避免重复计算
    const activeHoleNumber = computed(() => {
      const hlNum = projectInfo.value.hlNum
      if (!hlNum) return null

      // 支持多种格式：H1, h1, 1, 01 等
      const match = String(hlNum).match(/(\d+)/)
      return match ? parseInt(match[1]) : null
    })

    // 判断孔位是否激活（实心显示）- 优化性能
    const isHoleActive = (position) => {
      return activeHoleNumber.value === position
    }

    // 下载相关状态
    const downloadLoading = ref(false)

    // 监听数据变化，自动设置开始和结束日期
    watch(processedRecords, (newRecords) => {
      if (newRecords && newRecords.length > 0) {
        const firstRecord = newRecords[0]
        const lastRecord = newRecords[newRecords.length - 1]

        // 自动设置开始日期（第一个点的collectionAt）
        startDate.value = formatDateTime(firstRecord.collectionAt, 'date')

        // 自动设置结束日期（最后一个点的collectionAt）
        endDate.value = formatDateTime(lastRecord.collectionAt, 'date')
      } else {
        // 如果没有数据，清空日期
        startDate.value = ''
        endDate.value = ''
      }
    }, { immediate: true }) // immediate: true 确保组件初始化时也会执行

    // 下载表格图片功能
    const downloadTableImage = async () => {
      // 防止重复调用
      if (downloadLoading.value) return

      // 检查是否有数据
      if (!processedRecords.value || processedRecords.value.length === 0) {
        ElMessage.warning('暂无数据可下载')
        return
      }

      try {
        downloadLoading.value = true
        ElMessage.info('正在生成图片，请稍候...')

        // 获取表格容器元素
        const tableContainer = document.querySelector('.record-table-container')
        if (!tableContainer) {
          ElMessage.error('未找到表格容器')
          return
        }

        // 临时移除可编辑字段的样式效果
        const editableFields = tableContainer.querySelectorAll('.editable-field .el-input__wrapper')
        const originalStyles = []

        editableFields.forEach((field, index) => {
          // 保存原始样式
          originalStyles[index] = {
            border: field.style.border,
            borderRadius: field.style.borderRadius,
            background: field.style.background,
            backgroundColor: field.style.backgroundColor,
            boxShadow: field.style.boxShadow
          }

          // 设置为无样式效果
          field.style.border = 'none'
          field.style.borderRadius = '0'
          field.style.background = 'transparent'
          field.style.backgroundColor = 'transparent'
          field.style.boxShadow = 'none'
        })

        // 等待样式应用
        await new Promise(resolve => setTimeout(resolve, 100))

        // 配置html2canvas选项 - 使用最简单的配置
        const options = {
          useCORS: true,
          allowTaint: true,
          scale: 1, // 降低scale避免渲染问题
          backgroundColor: '#ffffff',
          logging: false, // 关闭日志
          // 移除可能导致问题的配置
        }

        // 生成canvas
        const canvas = await html2canvas(tableContainer, options)

        // 恢复可编辑字段的原始样式
        editableFields.forEach((field, index) => {
          const original = originalStyles[index]
          field.style.border = original.border
          field.style.borderRadius = original.borderRadius
          field.style.background = original.background
          field.style.backgroundColor = original.backgroundColor
          field.style.boxShadow = original.boxShadow
        })

        // 检查canvas是否有内容
        if (canvas.width === 0 || canvas.height === 0) {
          ElMessage.error('生成的图片为空，请重试')
          return
        }

        console.log('Canvas尺寸:', canvas.width, 'x', canvas.height)

        // 转换为图片数据
        const imgData = canvas.toDataURL('image/png', 1.0)

        // 检查图片数据
        if (!imgData || imgData === 'data:,') {
          ElMessage.error('图片数据生成失败，请重试')
          return
        }

        // 创建下载链接
        const link = document.createElement('a')
        link.href = imgData

        // 生成文件名 - 清理文件名中的非法字符
        const rawProjectName = projectInfo.value.tunnelName || '钻探记录'
        const projectName = rawProjectName
          .replace(/[<>:"/\\|?*]/g, '') // 移除文件名非法字符
          .replace(/[^\w\u4e00-\u9fa5\-_.]/g, '') // 只保留字母、数字、中文、连字符、下划线、点
          .trim()
          .substring(0, 50) // 限制长度
          || '钻探记录' // 如果清理后为空，使用默认名称
        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:.]/g, '-')
        link.download = `${projectName}_钻探记录表_${timestamp}.png`

        // 触发下载
        link.style.display = 'none'
        document.body.appendChild(link)
        link.click()

        // 延迟清理，确保下载完成
        setTimeout(() => {
          try {
            if (document.body.contains(link)) {
              document.body.removeChild(link)
            }
            // 释放blob URL内存
            URL.revokeObjectURL(link.href)
          } catch (cleanupError) {
            console.warn('清理下载链接时出错:', cleanupError)
          }
        }, 1000)

        ElMessage.success('图片下载成功')
      } catch (error) {
        console.error('下载图片失败:', error)
        ElMessage.error(`下载图片失败: ${error.message}`)

        // 确保在错误情况下也恢复样式
        const editableFields = document.querySelectorAll('.record-table-container .editable-field .el-input__wrapper')
        editableFields.forEach((field) => {
          field.style.border = ''
          field.style.borderRadius = ''
          field.style.background = ''
          field.style.backgroundColor = ''
          field.style.boxShadow = ''
        })
      } finally {
        downloadLoading.value = false
      }
    }

    return {
      depthStep,
      totalRecords,
      processedRecords,
      projectInfo,
      formatTime,
      formatNumber,
      getNextTime,
      handleDepthStepChange,
      getHolePositionStyle,
      isHoleActive,
      // 下载功能
      downloadLoading,
      downloadTableImage,
      Download,
      // 可编辑字段 - 仅用于页面显示
      constructionUnit,
      mileage,
      designDepth,
      startDate,
      endDate,
      recorder,
      geologist
    }
  }
}
</script>

<style scoped>
.drilling-record-card {
  margin-bottom: 20px;
  position: relative;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08) !important;
  border-radius: 12px;
  border: none;
}

.drilling-record-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #409eff, #67c23a);
  z-index: 1;
}

.drilling-record-card :deep(.el-card__header) {
  padding: 18px 24px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  background: rgba(255, 255, 255, 0.6);
}

.sub-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.sub-card-header span:first-child {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  position: relative;
}

.sub-card-header span:first-child::after {
  content: "";
  position: absolute;
  bottom: -6px;
  left: 0;
  width: 40px;
  height: 3px;
  background-color: #409eff;
  border-radius: 3px;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.drilling-record-card :deep(.el-card__body) {
  padding: 20px;
  overflow: hidden;
  max-width: 100%;
}

.record-table-container {
  width: 100%;
  border: 2px solid #000;
  background: #fff;
  font-family: 'SimSun', serif;
  display: inline-block; /* 支持内容自适应宽度 */
  min-width: fit-content; /* 确保内容不被压缩 */
}

/* 表头信息样式 */
.record-header {
  border-bottom: 2px solid #000;
}

.header-title {
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  padding: 15px;
  border-bottom: 1px solid #000;
  background: #f8f9fa;
}

.header-info {
  padding: 10px;
}

.info-row {
  display: flex;
  border-bottom: 1px solid #000;
  min-height: 40px;
}

.info-row:last-child {
  border-bottom: none;
}

.info-item {
  display: flex;
  align-items: center;
  border-right: 1px solid #000;
  padding: 8px 12px;
  flex: 1;
}

.info-item:last-child {
  border-right: none;
}

.info-item .label {
  font-weight: bold;
  margin-right: 8px;
  white-space: nowrap;
}

.info-item .value {
  flex: 1;
  text-align: center;
  min-height: 20px;
  padding: 2px 4px;
}

.info-item .value.editable-field {
  padding: 0;
}

.info-item .value.editable-field :deep(.el-input) {
  text-align: center;
}

.info-item .value.editable-field :deep(.el-input__wrapper) {
  border: 1px dashed #ccc;
  border-radius: 4px;
  background: #fafafa;
  transition: all 0.3s ease;
}

.info-item .value.editable-field :deep(.el-input__wrapper:hover) {
  border-color: #409eff;
  background: #fff;
}

.info-item .value.editable-field :deep(.el-input__wrapper.is-focus) {
  border-color: #409eff;
  border-style: solid;
  background: #fff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

.info-item .value.editable-field :deep(.el-input__inner) {
  text-align: center;
  font-size: 14px;
  color: #303133;
}

.coordinate-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.coord-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.coord-row span {
  font-weight: bold;
}

.coord-value {
  border-bottom: 1px solid #ccc;
  min-width: 40px;
  text-align: center;
  padding: 2px 4px;
}

/* 数据表格样式 */
.data-table-wrapper {
  overflow-x: auto;
}

.record-table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  font-size: 14px;
}

.record-table th,
.record-table td {
  border: 1px solid #000;
  padding: 8px 4px;
  text-align: center;
  vertical-align: middle;
}

.record-table th {
  background-color: #f8f9fa;
  font-weight: bold;
}

/* 列宽控制 */
.sequence-col { width: 60px; }
.start-time-col, .end-time-col { width: 80px; }
.depth-col { width: 100px; }
.speed-col { width: 80px; }
.torque-col { width: 90px; }
.pressure-col { width: 80px; }
.force-col { width: 80px; }
.rotation-col { width: 90px; }
.water-col { width: 80px; }
.rock-col { width: 100px; }
.description-col { width: 200px; }

/* 事件行背景色样式 */
.record-table tr.stuck-event {
  background: #ffebee !important;
}

.record-table tr.stuck-event td {
  background: #ffebee !important;
}

.record-table tr.mutation-event {
  background: #fff3e0 !important;
}

.record-table tr.mutation-event td {
  background: #fff3e0 !important;
}

/* 数据统计信息样式 */
.data-summary {
  padding: 10px 20px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  font-size: 14px;
  color: #666;
  text-align: center;
}

/* 附加信息区域样式 - 移动到表格上方 */
.additional-info-section {
  border-top: 1px solid #000;
  border-bottom: 2px solid #000;
}

.diagram-row {
  border-bottom: 1px solid #000;
  padding: 10px;
  min-height: 80px;
}

.signatures-row {
  padding: 10px;
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.diagram-section {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.diagram-label {
  font-weight: bold;
  margin-bottom: 10px;
}

.diagram-area {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tunnel-diagram {
  position: relative;
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tunnel-outline {
  width: 100px;
  height: 100px;
  border: 2px solid #000;
  border-radius: 50%;
  position: relative;
  background: #f8f9fa;
}

.hole-position {
  position: absolute;
  width: 8px;
  height: 8px;
}

.hole-dot {
  width: 100%;
  height: 100%;
  border: 1px solid #000;
  border-radius: 50%;
  background: #fff;
  transition: background-color 0.3s ease;
}

.hole-position.active .hole-dot {
  background: #000;
}

.signature-section {
  display: flex;
  flex-direction: row;
  gap: 60px;
  width: 100%;
  justify-content: center;
}

.signature-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.signature-label {
  font-weight: bold;
  white-space: nowrap;
}

.signature-value {
  border-bottom: 1px solid #000;
  min-width: 80px;
}

.signature-value.editable-field {
  border-bottom: none;
  min-width: 120px;
}

.signature-value.editable-field :deep(.el-input__wrapper) {
  border: none;
  border-bottom: 1px solid #000;
  border-radius: 0;
  background: transparent;
  box-shadow: none;
  transition: all 0.3s ease;
}

.signature-value.editable-field :deep(.el-input__wrapper:hover) {
  border-bottom-color: #409eff;
  background: rgba(64, 158, 255, 0.05);
}

.signature-value.editable-field :deep(.el-input__wrapper.is-focus) {
  border-bottom-color: #409eff;
  border-bottom-width: 2px;
  background: rgba(64, 158, 255, 0.05);
  box-shadow: none;
}

.signature-value.editable-field :deep(.el-input__inner) {
  text-align: center;
  font-size: 14px;
  color: #303133;
  padding: 2px 4px;
}

/* 空状态样式 */
.empty-state {
  padding: 40px 20px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .info-row {
    flex-direction: column;
  }

  .info-item {
    border-right: none;
    border-bottom: 1px solid #000;
  }

  .signature-section {
    flex-direction: column;
    gap: 20px;
  }
}
</style>

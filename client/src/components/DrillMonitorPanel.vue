<template>
  <div class="page-container">
    <div class="page-header">
      <!-- 左侧图标和项目名称 -->
      <div class="header-left">
        <img
          src="@/assets/images/logo-no-word.png"
          alt="Logo"
          class="header-logo"
        >
        <span class="project-name">{{ telemetryData?.Tunnel_Name || '项目名称' }}</span>
      </div>

      <!-- 右侧运行时长 -->
      <div class="run-time-container">
        <span class="run-time">运行时长：</span>
        <span class="run-time-value">{{ telemetryData?.Hgh_Wrk !== undefined && telemetryData?.Lw_Wrk !== undefined ? (((telemetryData.Hgh_Wrk & 0xFFFF) << 16 | (telemetryData.Lw_Wrk & 0xFFFF)) / 10).toFixed(1) + "小时" : '0.0小时' }}</span>
        <span style="margin-left: 20px;">{{ telemetryData?.Collection_At }}</span>
        <!-- <span class="run-time-value">{{ telemetryData?.Hgh_Wrk !== undefined && telemetryData?.Lw_Wrk !== undefined ? ((telemetryData.Hgh_Wrk & 0xFF) << 8 | (telemetryData.Lw_Wrk & 0xFF)) / 10 + "小时" : '0小时' }}</span>  -->
      </div>
    </div>

    <div class="page-content">
      <div class="head-items">
        <div class="flex-col">
          <span class="value">{{ telemetryData?.Tunnel_Dpth || '-' }}</span>
          <span class="title">隧道标段</span>
        </div>
        <div class="flex-col">
          <span class="value">{{ telemetryData?.HI_Num || '-' }}</span>
          <span class="title">钻孔编号</span>
        </div>
        <div class="flex-col">
          <span class="value">{{ telemetryData?.HI_Ang ? telemetryData.HI_Ang + '°' : '-°' }}</span>
          <span class="title">倾角度数</span>
        </div>
        <div class="flex-col">
          <span class="value">{{ telemetryData?.Dpth ? telemetryData.Dpth + 'cm' : '-' }}</span>
          <span class="title">当前孔深</span>
        </div>
        <div class="flex-col">
          <span class="value">{{ telemetryData?.Clct_Sts !== undefined ? formatCollectionStatus(telemetryData.Clct_Sts) : '-' }}</span>
          <span class="title">采集状态</span>
        </div>
      </div>

      <!-- 告警 -->
      <div class="warn-items">
        <div class="flex-row">
          <span class="title">地质预警</span>
          <div
            class="warn-dot"
            :style="{ backgroundColor: geologyWarnOn ? '#FF0000' : '#00FF00' }"
          />
        </div>
        <div class="flex-row">
          <span class="title">设备告警</span>
          <div
            class="warn-dot"
            :style="{ backgroundColor: deviceWarnOn ? '#FF0000' : '#00FF00' }"
          />
        </div>
      </div>

      <!-- 左侧信息区 -->
      <div class="left-items">
        <div class="left-item">
          <span class="title">岩石强度</span>
          <span
            class="title"
            style="padding-left: 10px;"
          >{{ telemetryData?.rockStrengthDesc || '-' }}</span>
        </div>
        <div class="left-item">
          <span class="title">工作模式</span>
          <span
            class="title"
            style="padding-left: 10px;"
          >{{ telemetryData?.Mode !== undefined ? formatWorkMode(telemetryData.Mode) : '-' }}</span>
        </div>
        <div class="left-item">
          <span class="title">钻头直径</span>
          <span
            class="title"
            style="padding-left: 10px;"
          >{{ telemetryData?.Diameter ? telemetryData.Diameter + 'mm' : '-' }}</span>
        </div>
      </div>

      <!-- 图片 -->
      <div class="image-container">
        <img
          :src="progressImage"
          class="progress-image"
          alt="drilling-progress"
        >
        <div class="progress-text">
          {{ Math.round(currentProgress) }}%
        </div>
      </div>

      <!-- 表盘 -->
      <div class="dial-container">
        <div class="dial-item">
          <span class="dial-title">旋转扭矩</span>
          <div
            ref="torqueGauge"
            class="gauge"
          />
        </div>
        <div class="dial-item">
          <span class="dial-title">推进力</span>
          <div
            ref="thrusterGauge"
            class="gauge"
          />
        </div>
        <div class="dial-item">
          <span class="dial-title">旋转速度</span>
          <div
            ref="rotationSpeedGauge"
            class="gauge"
          />
        </div>
        <div class="dial-item">
          <span class="dial-title">钻进速度</span>
          <div
            ref="drillSpeedGauge"
            class="gauge"
          />
        </div>
        <div class="dial-item">
          <div class="dial-title dial-title-container">
            <span>低压水压</span>
            <span>高压水压</span>
          </div>
          <div class="gauge water-pressure-container">
            <!-- 使用单个echarts创建合并的水压表盘 -->
            <div
              ref="waterPressureGauge"
              style="width: 150px; height: 150px;"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { ref, onMounted, onBeforeUnmount, watch, defineComponent, PropType, toRefs } from 'vue';
import * as echarts from 'echarts';
import { formatWorkMode, formatCollectionStatus } from '@/utils/utils';
import { max, min } from 'lodash-es';

// 定义TelemetryData类型
export interface TelemetryData {
  // 基础字段
  Collection_At?: string;   // 采集时间
  Tunnel_Name?: string;     // 隧道名称，项目名称
  Heart?: number;           // 心跳
  Mode?: number;            // 工作模式
  Work_Mode?: string;       // 工作模式，转换为中文
  Strk_Pct?: number;        // 进度百分比
  '18B03'?: number;         // 未知
  Wrn_Cd?: number;          // 告警代码
  Dpth?: number;            // 当前孔深CM
  Tunnel_Dpth?: number;     // 隧道深度，标段号
  HI_Num?: number;          // 钻孔编号
  HI_Ang?: number;          // 孔倾角
  Diameter?: number;        // 钻头直径MM
  Clct_Type?: number;       // 采集状态

  
  // 图表相关字段
  Rtn_Tq?: number;          // 旋转扭矩(kN·m)
  Frcst_kN?: number;        // 推进力(kN)
  Frcst_Prs?: number;       // 推进压力(MPa)
  Rtn_Spd?: number;         // 转速(r/min)
  Advnc_Spd?: number;       // 钻速(m/min)
  Wtr_Prs_L?: number;       // 低压水压(MPa)
  Wtr_Prs_H?: number;       // 高压水压(MPa)
  
  // 其他字段
  Hgh_Wrk?: number;         // 工作时长高位
  Lw_Wrk?: number;          // 工作时长低位

  rockStrengthDesc?: string;  // 岩石强度描述
  ROCK_STRENGTH_LEVEL?: number; // 岩石强度数值
  ROCK_GRADE_DESC?: string;     // 围岩等级描述
  ROCK_GRADE_LEVEL?: number;    // 围岩等级数值
  EQUIPMENT_WARNING?: number;   // 设备预警
  GEOLOGICAL_WARNING?: number;  // 地质预警

  // 允许其他字段
  [key: string]: any;
}

export default defineComponent({
  name: 'DrillMonitorPanel',
  props: {
    telemetryData: {
      type: Object as PropType<TelemetryData | undefined>,
      default: undefined
    }
  },
  setup(props: { telemetryData: TelemetryData | undefined }) {
    // 使用toRefs，确保模板中可以直接访问telemetryData
    const { telemetryData } = toRefs(props);

    const geologyWarnOn = ref<boolean>(false);
    const deviceWarnOn = ref<boolean>(false);

    // 进度相关
    const currentProgress = ref<number>(0); // 实际显示的进度值
    const targetProgress = ref<number>(0);  // 目标进度值
    const progressImage = ref<string>('');
    let animationFrameId: number | null = null;
    let animationStartTime = 0;
    let startProgress = 0;
    const ANIMATION_DURATION = 1000; // 动画持续时间(ms)，设为1秒

    // 获取进度图片路径
    function getDrillProgessPic(progress: number): string {
      // 确保进度值在有效范围内
      const validProgress = Math.max(0, Math.min(100, Math.round(progress)));
      return `/assets/images/drills/pic-${validProgress}.png`;
    }

    // 预加载下一张图片
    const imageCache: Record<number, HTMLImageElement> = {};
    function preloadNextImage(currentProgress: number, targetProgress: number): void {
      const step = currentProgress < targetProgress ? 1 : -1;
      const nextProgress = Math.round(currentProgress) + step;
      
      if (nextProgress >= 0 && nextProgress <= 100 && nextProgress !== Math.round(currentProgress)) {
        const imgPath = getDrillProgessPic(nextProgress);
        if (!imageCache[nextProgress]) {
          imageCache[nextProgress] = new Image();
          imageCache[nextProgress].src = imgPath;
        }
      }
    }

    // 缓动函数：平滑的加速和减速
    function easeInOutCubic(t: number): number {
      return t < 0.5
        ? 4 * t * t * t
        : 1 - Math.pow(-2 * t + 2, 3) / 2;
    }

    // 基于时间的平滑更新进度值函数
    function animateProgress(timestamp: number): void {
      if (!animationStartTime) {
        animationStartTime = timestamp;
      }
      
      // 计算已经经过的时间
      const elapsedTime = timestamp - animationStartTime;
      
      // 计算动画进度(0-1)
      const progress = Math.min(elapsedTime / ANIMATION_DURATION, 1);
      
      // 使用缓动函数使动画更平滑
      const easedProgress = easeInOutCubic(progress);
      
      // 根据缓动进度计算当前值
      const newCurrentProgress = startProgress + (targetProgress.value - startProgress) * easedProgress;
      const prevProgress = currentProgress.value;
      currentProgress.value = newCurrentProgress;
      
      // 只在整数变化时更新图片，减少不必要的DOM更新
      if (Math.round(prevProgress) !== Math.round(currentProgress.value)) {
        progressImage.value = getDrillProgessPic(Math.round(currentProgress.value));
        // 预加载下一张图片
        preloadNextImage(currentProgress.value, targetProgress.value);
      }
      
      // 如果动画未完成，继续下一帧
      if (progress < 1) {
        animationFrameId = requestAnimationFrame(animateProgress);
      } else {
        // 动画完成，确保值精确匹配目标值
        currentProgress.value = targetProgress.value;
        progressImage.value = getDrillProgessPic(Math.round(targetProgress.value));
        
        // 重置动画状态
        animationFrameId = null;
        animationStartTime = 0;
      }
    }

    // 更新目标进度值并开始动画
    function updateProgressWithAnimation(newProgress: number): void {
      // 如果值相同，不需要动画
      if (targetProgress.value === newProgress) return;
      
      // 保存动画起始值
      startProgress = currentProgress.value;
      
      // 更新目标进度
      targetProgress.value = newProgress;
      
      // 预加载目标图片
      const targetImgPath = getDrillProgessPic(Math.round(newProgress));
      if (!imageCache[Math.round(newProgress)]) {
        imageCache[Math.round(newProgress)] = new Image();
        imageCache[Math.round(newProgress)].src = targetImgPath;
      }
      
      // 取消可能正在进行的动画
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
        animationFrameId = null;
        animationStartTime = 0;
      }
      
      // 开始新动画
      animationFrameId = requestAnimationFrame(animateProgress);
    }

    const torqueGauge = ref<HTMLElement | null>(null);
    let torqueInst: echarts.ECharts | null = null;
    const thrusterGauge = ref<HTMLElement | null>(null);
    let thrusterInst: echarts.ECharts | null = null;
    const rotationSpeedGauge = ref<HTMLElement | null>(null);
    let rotationSpeedInst: echarts.ECharts | null = null;
    const drillSpeedGauge = ref<HTMLElement | null>(null);
    let drillSpeedInst: echarts.ECharts | null = null;
    const waterPressureGauge = ref<HTMLElement | null>(null);
    let waterPressureInst: echarts.ECharts | null = null;

    // 修改watch以更新图表和进度动画
    watch(() => telemetryData.value, (newData: TelemetryData | undefined) => {
      if (!newData) return;
      
      try {
        // 更新告警状态
        if (newData.GEOLOGICAL_WARNING !== undefined) {
          geologyWarnOn.value = newData.GEOLOGICAL_WARNING > 0;
        }
        if (newData.isStuckPoint !== undefined || newData.isMutationPoint !== undefined) {
          deviceWarnOn.value = newData.isStuckPoint || newData.isMutationPoint;
        }
      
        // 更新图片 - 使用进度动画
        if (newData.Strk_Pct !== undefined) {
          // 使用动画平滑过渡到新的进度值
          updateProgressWithAnimation(newData.Strk_Pct);
        }

        // 表盘更新
        if (torqueInst && newData.Rtn_Tq !== undefined) {
          torqueInst.setOption({
            series: [{ data: [{ value: newData.Rtn_Tq, name: 'kN·m' }] }]
          });
        }

        if (thrusterInst && newData.Frcst_kN !== undefined) {
          thrusterInst.setOption({
            series: [{ data: [{ value: newData.Frcst_kN, name: 'kN' }] }]
          });
        }

        if (rotationSpeedInst && newData.Rtn_Spd !== undefined) {
          rotationSpeedInst.setOption({
            series: [{ data: [{ value: newData.Rtn_Spd, name: 'r/min' }] }]
          });
        }

        if (drillSpeedInst && newData.Advnc_Spd !== undefined) {
          drillSpeedInst.setOption({
            series: [{ data: [{ value: newData.Advnc_Spd, name: 'm/min' }] }]
          });
        }

        // 更新水压表盘
        if (waterPressureInst && newData.Wtr_Prs_L !== undefined && newData.Wtr_Prs_H !== undefined) {
          try {
            // 确保数据在有效范围内
            const lowPressValue = newData.Wtr_Prs_L;
            const highPressValue = newData.Wtr_Prs_H;
            
            waterPressureInst.setOption({
              series: [
                {
                  data: [{ 
                    value: lowPressValue,
                    name: ''
                  }]
                },
                {
                  data: [{ 
                    value: highPressValue,
                    name: '0'
                  }]
                }
              ]
            });
          } catch (err) {
            console.error('更新水压表盘失败:', err);
          }
        }
      } catch (error) {
        console.error('处理遥测数据时出错:', error);
      }
    }, { deep: true, immediate: true });

    const initCharts = (): void => {
      try {
        // 确保DOM元素存在
        if (!torqueGauge.value || !thrusterGauge.value || 
            !rotationSpeedGauge.value || !drillSpeedGauge.value ||
            !waterPressureGauge.value) {
          console.warn('图表DOM元素未找到，等待DOM更新后重试');
          // 延迟一帧后重新尝试初始化
          setTimeout(initCharts, 100);
          return;
        }
        
        // 初始化图表实例
        torqueInst = echarts.init(torqueGauge.value);
        thrusterInst = echarts.init(thrusterGauge.value);
        rotationSpeedInst = echarts.init(rotationSpeedGauge.value);
        drillSpeedInst = echarts.init(drillSpeedGauge.value);
        waterPressureInst = echarts.init(waterPressureGauge.value);
        
        // 创建图表配置选项
        const genOption = (min: number, max: number, splitNum: number, unit: string, defVal: number) => {
          return {
            backgroundColor: '#1C1C1E',
            series: [
              {
                type: 'gauge',
                startAngle: 225,
                endAngle: 315,
                center: ['50%', '50%'],
                radius: '85%',
                min: min,
                max: max,
                splitNumber: splitNum, // 分成几段
                axisLine: {
                  lineStyle: {
                    width: 1,
                    color: [
                      [0.8, '#FFFFFF'],
                      [1, '#FD0000']
                    ]
                  }
                },
                pointer: {
                  width: 2,
                  length: 40,
                  itemStyle: {
                    color: 'red'
                  }
                },
                axisTick: {
                  length: 2,
                  distance: 2,
                  lineStyle: {
                    color: '#FFFFFF',
                    width: 0.5
                  }
                },
                splitLine: {
                  length: 4,
                  distance: 2,
                  lineStyle: {
                    color: '#FFFFFF',
                    width: 1
                  }
                },
                axisLabel: {
                  color: '#FFFFFF',
                  fontSize: 8,
                  distance: 3,
                  // rotate: 'tangential',
                  formatter: function (value: number) {
                    return value;
                  }
                },
                title: {
                  offsetCenter: [0, '90%'],
                  fontSize: 14,
                  color: '#FFFFFF'
                },
                detail: {
                  fontSize: 14,
                  offsetCenter: [0, '60%'],
                  valueAnimation: true,
                  formatter: function (value: number) {
                    return value;
                  },
                  color: 'inherit'
                },
                data: [
                  {
                    value: defVal,
                    name: unit
                  }
                ]
              }
            ]
          };
        };

        // 设置图表选项
        torqueInst.setOption(genOption(0, 15, 5, 'kN·m', 0));
        thrusterInst.setOption(genOption(0, 100, 5, 'kN', 0));
        rotationSpeedInst.setOption(genOption(0, 600, 6, 'rpm', 0));
        drillSpeedInst.setOption(genOption(0, 1, 5, 'm/min', 0));
        
        // 为水压表盘设置新的配置选项
        const waterPressureOption = {
          backgroundColor: '#1C1C1E',
          series: [
            {
              // 左侧低压水压
              name: '低压水压',
              type: 'gauge',
              startAngle: -130,
              endAngle: 130,
              center: ['50%', '50%'],
              radius: '85%',
              min: 0,
              max: 250,
              progress: {
                show: true,
                width: 13,
                roundCap: true,
                itemStyle: {
                  color: '#ff443a' // 红色区域表示低压
                }
              },
              pointer: {
                show: false // 不显示指针
              },
              axisLine: {
                roundCap: true,
                lineStyle: {
                  width: 13,
                  color: [
                    [1, 'rgba(255, 255, 255, 0.9)'] // 背景色为非常微弱的白色
                  ]
                }
              },
              axisTick: {
                show: false // 不显示刻度线
              },
              splitLine: {
                show: false // 不显示分隔线
              },
              axisLabel: {
                show: true,
                distance: 2,
                fontSize: 6,
                color: '#fff',
                formatter: function(value: number) {
                  if (value === 0) return 'L';
                  if (value === 250) return 'H';
                  return '';
                }
              },
              detail: {
                show: true,
                valueAnimation: true,
                formatter: function(value: number) {
                  return value.toFixed(1);
                },
                color: '#FFF',
                fontSize: 13,
                offsetCenter: [-17, 45],
                backgroundColor: 'transparent'
              },
              title: {
                show: true,
                offsetCenter: [-25, 95],
                fontSize: 14,
                color: '#fff',
                formatter: 'bar'
              }
            },
            {
              // 右侧高压水压
              name: '高压水压',
              type: 'gauge',
              startAngle: -50, 
              endAngle: 50,
              center: ['50%', '50%'],
              radius: '85%',
              min: 0,
              max: 250,
              splitNumber: 4,
              clockwise: false,
              progress: {
                show: true,
                width: 13,
                roundCap: true,
                itemStyle: {
                  color: '#43cf7c' // 绿色区域表示高压
                }
              },
              pointer: {
                show: false // 不显示指针
              },
              axisLine: {
                roundCap: true,
                lineStyle: {
                  width: 13,
                  color: [
                    [1, 'rgba(255, 255, 255, 0.9)'] // 背景色为非常微弱的白色
                  ]
                }
              },
              axisTick: {
                show: false // 不显示刻度线
              },
              splitLine: {
                show: false // 不显示分隔线
              },
              axisLabel: {
                show: true,
                color: '#fff',
                distance: 2,
                fontSize: 6,
              },
              detail: {
                show: true,
                valueAnimation: true,
                formatter: function(value: number) {
                  return value.toFixed(0);
                },
                color: '#FFF',
                fontSize: 13,
                offsetCenter: [17, 45],
                backgroundColor: 'transparent'
              },
              title: {
                show: true,
                offsetCenter: [25, 95],
                fontSize: 14,
                color: '#fff',
                formatter: 'bar'
              }
            }
          ]
        };

        // 应用水压表盘配置
        waterPressureInst.setOption(waterPressureOption);
        
      } catch (error) {
        console.error('初始化图表错误:', error);
      }
    };
    
    // 创建一个调整图表大小的函数，可以在resize事件和组件卸载时复用
    const handleResize = () => {
      torqueInst?.resize();
      thrusterInst?.resize(); 
      rotationSpeedInst?.resize();
      drillSpeedInst?.resize();
      waterPressureInst?.resize();
    };

    onMounted(() => {
      initCharts();
      
      // 初始化图片
      progressImage.value = getDrillProgessPic(0);
      
      // 初始化进度动画
      updateProgressWithAnimation(0);
      
      // 预加载开始阶段的图片
      for (let i = 0; i <= 10; i++) {
        const imgPath = getDrillProgessPic(i);
        if (!imageCache[i]) {
          imageCache[i] = new Image();
          imageCache[i].src = imgPath;
        }
      }
      
      // 监听窗口大小变化，自动调整图表大小
      window.addEventListener('resize', handleResize);
    });

    onBeforeUnmount(() => {
      // 移除resize事件监听
      window.removeEventListener('resize', handleResize);
      
      // 清理实例
      torqueInst?.dispose();
      thrusterInst?.dispose();
      rotationSpeedInst?.dispose();
      drillSpeedInst?.dispose();
      waterPressureInst?.dispose();
      
      // 清除可能正在进行的动画
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
      }
    });

    return {
      geologyWarnOn,
      deviceWarnOn,
      progressImage,
      torqueGauge,
      thrusterGauge,
      rotationSpeedGauge,
      drillSpeedGauge,
      waterPressureGauge,
      currentProgress,
      formatWorkMode,
      formatCollectionStatus
    };
  }
});
</script>


<style scoped>

.page-container {
  width: 100%;
  height: 100%;
  position: relative;
  background: rgba(33, 39, 45, 1);
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 700px;
  max-height: 700px;
  overflow: visible;
  padding-right: 30px; /* 增加右侧空间 */
}

.page-header {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  padding: 30px 30px;
  height: 40px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  color: #FFFFFF;
  z-index: 20;
}

.header-left {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 12px;
}

.header-logo {
  width: 64px;
  height: 64px;
  object-fit: contain;
}

.project-name {
  font-size: 32px;
  font-weight: 600;
  color: #e77918;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 300px;
}

.run-time-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.page-content {
  width: 100%;
  height: 100%;
  position: relative;
  top: 0;
  left: 0;
  padding-bottom: 20px; /* 增加底部空间 */
}

/* 添加分隔线 */
.dial-container::before {
  content: '';
  position: absolute;
  top: -20px;
  left: 20px;
  right: 20px;
  height: 1px;
  background-color: rgba(255, 255, 255, 0.1);
}

.title {
  font-size: 24px;
  color: #838383;
}

.value {
  font-size: 36px;
  color: #FFFFFF;
}

.flex-col {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.flex-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.head-items {
  position: absolute;
  top: 60px;
  left: 20px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 80px;
  z-index: 10;
}

.warn-items {
  position: absolute;
  top: 60px;
  right: 101px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.warn-dot {
  margin-left: 100px;
  width: 44px;
  height: 44px;
  border-radius: 50%;
  border: 1px solid #FFFFFF;
}

.left-items {
  position: absolute;
  top: 180px;
  left: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 30px;
}

.left-item {
  width: 448px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.image-container {
  position: absolute;
  top: 180px;
  left: 490px;
  width: 800px;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: visible;
  perspective: 1000px;
  position: relative;
}

.progress-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transform: scale(1);
  transform-origin: center center;
  will-change: transform;
  image-rendering: -webkit-optimize-contrast; /* 提高图片渲染质量 */
}

.progress-text {
  position: absolute;
  bottom: 0px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 18px;
  color: #FFFFFF;
  text-shadow: 0 0 3px rgba(0, 0, 0, 0.5);
  z-index: 3;
}

.dial-container {
  position: absolute;
  top: 430px;
  left: 0;
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: space-between;
  padding: 0 20px;
}

.dial-item {
  height: 220px;
  display: flex;
  flex-direction: column;
  margin-right: 10px;
}

/* 最后一个dial-item不需要右边距 */
.dial-item:last-child {
  margin-right: 0;
}

.gauge {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  overflow: hidden;
  margin-top: 10px;
}

.dial-title {
  font-size: 18px;
  color: #FFFFFF;
}

.dial-title-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.water-pressure-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 150px;
  height: 150px;
  margin-top: 10px;
  gap: 4px; /* 添加间距 */
}

.water-pressure-low {
  width: 115px;
  height: 115px;
  border-radius: 50%;
  background-color: #FFFFFF;
}

</style>

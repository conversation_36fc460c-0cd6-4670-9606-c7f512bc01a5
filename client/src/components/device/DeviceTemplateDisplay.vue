<template>
  <!-- 设备模版展示区域 -->
  <div class="device-template-display">
    <!-- 加载状态 -->
    <div
      v-if="loading"
      class="loading-container"
    >
      <el-skeleton
        :rows="8"
        animated
      />
    </div>

    <!-- 提示选择条件 -->
    <div
      v-else-if="!reportData"
      class="no-file-selected"
    >
      <el-empty
        description="请选择文件或设置查询条件以查看模版数据"
        :image-size="200"
      >
        <div class="search-options">
          <span>请在页面顶部选择文件或设置查询条件</span>
        </div>
      </el-empty>
    </div>

    <!-- 显示无数据状态 -->
    <div
      v-else-if="allComponents.length > 0 && !hasAnyData"
      class="no-data-container"
    >
      <el-empty
        description="暂无分析数据"
        :image-size="200"
      >
        <div class="no-data-tips">
          <p>当前查询条件下没有找到相关数据</p>
          <p>请尝试：</p>
          <ul>
            <li>选择其他文件进行分析</li>
            <li>调整时间范围或孔号条件</li>
            <li>检查设备是否有数据上传</li>
          </ul>
        </div>
      </el-empty>
    </div>

    <!-- 有模版数据且有搜索结果时显示 - 直接渲染实际组件 -->
    <div
      v-else-if="allComponents.length > 0 && hasAnyData"
      class="template-content"
    >
      <!-- 组件容器 - 使用相对定位来支持绝对定位的组件 -->
      <div
        ref="containerRef"
        class="template-components-container"
        :style="{ minHeight: getContainerHeight() + 'px' }"
      >
        <!-- 动态渲染各个组件 -->
        <component
          :is="comp.component"
          v-for="(comp, index) in allComponents"
          :key="`${comp.component}-${comp.id || comp.name || index}-${deviceId}`"
          :style="getComponentPositionStyle(comp)"
          class="template-component"
          v-bind="getComponentProps(comp)"
          @stuck-toggle="handleStuckToggle"
          @mutation-toggle="handleMutationToggle"
        />
      </div>
    </div>



    <!-- 无模版数据时显示 -->
    <div
      v-else
      class="no-template"
    >
      <el-empty
        description="该设备暂未绑定任何模版"
        :image-size="200"
      >
        <div class="empty-actions">
          <el-button
            type="primary"
            @click="handleGoToTemplateList"
          >
            查看模版列表
          </el-button>
          <el-button
            type="success"
            @click="handleRefresh"
          >
            刷新数据
          </el-button>
        </div>
      </el-empty>
    </div>

    <!-- 错误状态 -->
    <div
      v-if="error"
      class="error-container"
    >
      <el-alert
        :title="error"
        type="error"
        show-icon
        :closable="false"
      >
        <template #default>
          <el-button
            type="primary"
            size="small"
            @click="handleRetry"
          >
            重试
          </el-button>
        </template>
      </el-alert>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, watch, computed, nextTick } from 'vue'
import { getDeviceEnabledTemplates } from '@/api/deviceTemplateConfig'

// 导入所有可能用到的 dashboard 组件
import RockPropertyCard from '@/components/dashboard/RockPropertyCard.vue'
import RockStatsCard from '@/components/dashboard/RockStatsCard.vue'
import GeologicAnalysisCard from '@/components/dashboard/GeologicAnalysisCard.vue'
import StrataDistributionCard from '@/components/dashboard/StrataDistributionCard.vue'
import Drilling3DAnalysisCard from '@/components/dashboard/Drilling3DAnalysisCard.vue'
import DataTreeChartCard from '@/components/dashboard/DataTreeChartCard.vue'
import DrillCurveChartCard from '@/components/dashboard/DrillCurveChartCard.vue'
import ConditionMatrixCard from '@/components/dashboard/ConditionMatrixCard.vue'
import DrillingDataTableCard from '@/components/dashboard/DrillingDataTableCard.vue'
import DrillingRecordTableCard from '@/components/dashboard/DrillingRecordTableCard.vue'
import DrillingDepthTimeSeriesCard from '@/components/dashboard/DrillingDepthTimeSeriesCard.vue'

export default {
  name: 'DeviceTemplateDisplay',
  components: {
    RockPropertyCard,
    RockStatsCard,
    GeologicAnalysisCard,
    StrataDistributionCard,
    Drilling3DAnalysisCard,
    DataTreeChartCard,
    DrillCurveChartCard,
    ConditionMatrixCard,
    DrillingDataTableCard,
    DrillingRecordTableCard,
    DrillingDepthTimeSeriesCard
  },
  props: {
    deviceId: {
      type: Number,
      required: true
    },
    loading: {
      type: Boolean,
      default: false
    },
    // 添加一个prop来监听组件的可见性状态
    visible: {
      type: Boolean,
      default: true
    },

    // 报告数据
    reportData: {
      type: Object,
      default: () => ({
        rockyNatureData: [],
        perimeterRockData: [],
        geologicAnalysisReport: [],
        strataDistributionData: { data: [], 极坚固: 25, 很坚固: 50, 坚固: 70, 比较坚固: 90, 中等坚固: 100, 较软: 130, 空洞: 150 },
        conditionMatrixData: []
      })
    },


  },
  emits: ['update:loading', 'stuck-toggle', 'mutation-toggle'],
  setup(props, { emit }) {
    // 响应式数据
const enabledTemplates = ref([])
const error = ref('')
const internalLoading = ref(false)

// 容器引用和缩放相关
const containerRef = ref(null)
const containerWidth = ref(800) // 当前容器宽度
const templateOriginalWidth = ref(800) // 模版原始宽度

// 计算属性 - 合并内部和外部loading状态
const loading = computed({
  get: () => props.loading || internalLoading.value,
  set: (value) => {
    internalLoading.value = value
    emit('update:loading', value)
  }
})



// 计算属性 - 获取所有模版的组件
const allComponents = computed(() => {
  const components = []

  enabledTemplates.value.forEach((template) => {
    const templateComponents = getTemplateComponents(template)
    components.push(...templateComponents)
  })

  return components
})

/**
 * 检查是否有任何数据
 * @returns {boolean} 是否有数据
 */
const hasAnyData = computed(() => {
  if (!props.reportData) {
    return false
  }

  // 检查各种数据源是否有内容
  const dataChecks = [
    // 岩石性质数据
    props.reportData.generateRockyNaturePieChartData &&
    Array.isArray(props.reportData.generateRockyNaturePieChartData) &&
    props.reportData.generateRockyNaturePieChartData.length > 0,

    // 围岩统计数据
    props.reportData.generatePerimeterRockStatisticsBarChartData &&
    Array.isArray(props.reportData.generatePerimeterRockStatisticsBarChartData) &&
    props.reportData.generatePerimeterRockStatisticsBarChartData.length > 0,

    // 地质分析报告
    props.reportData.generateGeologicAnalysisReport &&
    Array.isArray(props.reportData.generateGeologicAnalysisReport) &&
    props.reportData.generateGeologicAnalysisReport.length > 0,

    // 岩层分布数据
    props.reportData.generateStrataDistributionLineChartData &&
    props.reportData.generateStrataDistributionLineChartData.data &&
    Array.isArray(props.reportData.generateStrataDistributionLineChartData.data) &&
    props.reportData.generateStrataDistributionLineChartData.data.length > 0,

    // 钻进数据
    props.reportData.generateDrillingData &&
    Array.isArray(props.reportData.generateDrillingData) &&
    props.reportData.generateDrillingData.length > 0,

    // 钻机报告数据
    props.reportData.generateDrillingRecordData && 
    Array.isArray(props.reportData.generateDrillingRecordData) &&
    props.reportData.generateDrillingRecordData.length > 0,

    // 工况矩阵数据
    props.reportData.generateConditionMatrixChartData &&
    Array.isArray(props.reportData.generateConditionMatrixChartData) &&
    props.reportData.generateConditionMatrixChartData.length > 0,

    // 钻进深度时序数据
    props.reportData.generateDrillingDepthTimeSeriesData && 
    Array.isArray(props.reportData.generateDrillingDepthTimeSeriesData) &&
    props.reportData.generateDrillingDepthTimeSeriesData.length > 0
  ]

  // 只要有任何一个数据源有内容，就认为有数据
  return dataChecks.some(check => check === true)
})

/**
 * 获取设备启用的模版列表
 */
const fetchDeviceTemplates = async () => {
  if (!props.deviceId) {
    error.value = '设备ID不能为空'
    return
  }

  loading.value = true
  error.value = ''

  try {
    const response = await getDeviceEnabledTemplates(props.deviceId)

    if (response.success) {
      // 修复：从 response.data.list 获取数组数据
      enabledTemplates.value = response.data?.list || []

      // 更新模版原始宽度
      updateTemplateOriginalWidth()

      // 等待DOM更新后更新容器尺寸
      nextTick(() => {
        updateContainerSize()
      })
    } else {
      error.value = response.message || '获取设备模版失败'
      enabledTemplates.value = []
    }
  } catch (err) {
    console.error('获取设备模版错误:', err)
    error.value = '获取设备模版失败，请稍后重试'
    enabledTemplates.value = []
  } finally {
    loading.value = false
  }
}

/**
 * 解析模版配置中的组件 - 参考模版列表页面的实现
 */
const getTemplateComponents = (templateConfig) => {
  try {
    // 优先使用 configObj，如果没有则使用 config
    let config
    if (templateConfig.configObj) {
      config = templateConfig.configObj
    } else if (templateConfig.config) {
      config = typeof templateConfig.config === 'string'
        ? JSON.parse(templateConfig.config)
        : templateConfig.config
    } else {
      return []
    }

    let components = config.components || []

    // 标准化组件数据结构，确保正确解析位置和尺寸
    components = components.map((comp, index) => {
      // 解析组件的config字段获取缩略图
      let thumbnail = null
      try {
        if (comp.config && typeof comp.config === 'string') {
          const configObj = JSON.parse(comp.config)
          thumbnail = configObj.thumbnail
        }
      } catch (error) {
        console.warn('解析组件config失败:', error)
      }

      const standardized = {
        id: comp.id || `comp-${index}`,
        name: comp.name || comp.title || comp.label || `组件${index + 1}`,
        type: comp.type || comp.chartType || 'chart',
        component: comp.component || 'DrillingDataTableCard', // 确保有组件名称
        // 正确解析位置信息
        x: comp.position?.x ?? comp.x ?? 0,
        y: comp.position?.y ?? comp.y ?? 0,
        // 正确解析尺寸信息
        width: comp.size?.width ?? comp.width ?? 200,
        height: comp.size?.height ?? comp.height ?? 150,
        // 获取缩略图 - 优先从config中解析的thumbnail
        thumbnail: thumbnail || comp.thumbnail || comp.image || comp.chartImage || comp.preview
      }

      return standardized
    })

    return components
  } catch (error) {
    console.error('解析模版配置失败:', error)
    return []
  }
}

/**
 * 获取缩放比例 - 基于当前容器宽度与模版原始宽度的比例
 */
const getScaleRatio = () => {
  // 如果容器宽度为0或未设置，返回默认比例
  if (!containerWidth.value || containerWidth.value <= 0) {
    return 1
  }

  // 如果模版原始宽度为0或未设置，返回默认比例
  if (!templateOriginalWidth.value || templateOriginalWidth.value <= 0) {
    return 1
  }

  // 计算缩放比例：当前容器宽度 / 模版原始宽度
  // 减去容器的内边距（左右各20px）
  const availableWidth = containerWidth.value
  const ratio = availableWidth / templateOriginalWidth.value

  // 确保缩放比例在合理范围内（0.1 - 3.0）
  const clampedRatio = Math.max(0.1, Math.min(3.0, ratio))

  return clampedRatio
}

/**
 * 更新容器尺寸信息 - 简化版本，只在必要时重试
 */
const updateContainerSize = () => {
  if (!containerRef.value) {
    return
  }

  const rect = containerRef.value.getBoundingClientRect()

  // 如果获取到的宽度为0，说明元素可能还没有完全渲染，延迟一次重试
  if (rect.width === 0) {
    setTimeout(() => {
      const retryRect = containerRef.value?.getBoundingClientRect()
      if (retryRect && retryRect.width > 0) {
        containerWidth.value = retryRect.width
      }
    }, 100)
    return
  }

  containerWidth.value = rect.width
}

/**
 * 更新模版原始宽度 - 从模版配置中获取
 */
const updateTemplateOriginalWidth = () => {
  if (enabledTemplates.value.length === 0) {
    templateOriginalWidth.value = 800 // 默认宽度
    return
  }

  // 从第一个模版的配置中获取原始画布宽度
  const firstTemplate = enabledTemplates.value[0]
  try {
    let config
    if (firstTemplate.configObj) {
      config = firstTemplate.configObj
    } else if (firstTemplate.config) {
      config = typeof firstTemplate.config === 'string'
        ? JSON.parse(firstTemplate.config)
        : firstTemplate.config
    }

    if (config && config.canvas && config.canvas.width) {
      templateOriginalWidth.value = config.canvas.width
    } else {
      templateOriginalWidth.value = 800 // 默认宽度
    }
  } catch (error) {
    console.error('解析模版原始宽度失败:', error)
    templateOriginalWidth.value = 800 // 默认宽度
  }
}

/**
 * 计算容器高度 - 支持缩放，避免切换时的布局抖动
 */
const getContainerHeight = () => {
  // 设置一个稳定的最小高度，避免切换时的抖动
  const minHeight = 600

  if (allComponents.value.length === 0) {
    return minHeight
  }

  // 获取缩放比例
  const scale = getScaleRatio()

  // 计算所有组件的最大底部位置（缩放后）
  let maxBottom = 0
  allComponents.value.forEach(component => {
    const y = component.y || 0
    const height = component.height || 150
    const scaledBottom = (y + height) * scale
    maxBottom = Math.max(maxBottom, scaledBottom)
  })

  // 添加一些内边距，确保不小于最小高度
  return Math.max(maxBottom + 50, minHeight)
}

/**
 * 获取组件位置样式 - 支持响应式缩放
 */
const getComponentPositionStyle = (component) => {
  const x = component.x || 0
  const y = component.y || 0
  const width = component.width || 200
  const height = component.height || 150

  // 获取缩放比例
  const scale = getScaleRatio()

  return {
    position: 'absolute',
    left: (x * scale) + 'px',
    top: (y * scale) + 'px',
    width: (width * scale) + 'px',
    height: (height * scale) + 'px'
  }
}

/**
 * 获取组件属性 - 使用实际的数据
 */
const getComponentProps = (component) => {
  // 基础属性，包含设备ID
  const baseProps = {
    deviceId: props.deviceId
  }

  // 根据组件类型添加特定的属性，使用从父组件传递的实际数据
  switch (component.component) {
    case 'DrillingDataTableCard':
    case 'Drilling3DAnalysisCard':
    case 'DataTreeChartCard':
      // 这些组件需要钻进数据
      return {
        ...baseProps,
        drillingData: props.reportData?.generateDrillingData || []
      }
    case 'DrillingRecordTableCard':
      // 钻探记录表组件需要专门的记录数据
      return {
        ...baseProps,
        drillingRecordData: props.reportData?.generateDrillingRecordData || []
      }
    case 'DrillCurveChartCard':
      // 这些组件需要钻进数据和卡钻数据
      return {
        ...baseProps,
        drillingData: props.reportData?.generateDrillingData || [],
        filteringData: props.reportData?.generateDrillingCurveFilteringData || [],
        stuckData: props.reportData?.generateDetectStuckEventsData,
        mutationData: props.reportData?.generateDrillingSpeedMutationData,
        filterStuckData: props.reportData?.generateCurveFilterDetectStuckEventsData,
        filterMutationData: props.reportData?.generateCurveFilterDrillingSpeedMutationData
      }
    case 'ConditionMatrixCard':
      return {
        ...baseProps,
        conditionMatrixData: props.reportData?.generateConditionMatrixChartData || [],
        drillingData: props.reportData?.generateDrillingData || []
      }
    case 'RockPropertyCard':
      return {
        ...baseProps,
        rockyNatureData: props.reportData?.generateRockyNaturePieChartData || []
      }
    case 'RockStatsCard':
      return {
        ...baseProps,
        perimeterRockData: props.reportData?.generatePerimeterRockStatisticsBarChartData || []
      }
    case 'GeologicAnalysisCard':
      return {
        ...baseProps,
        geologicAnalysisReport: props.reportData?.generateGeologicAnalysisReport || []
      }
    case 'StrataDistributionCard':
      return {
        ...baseProps,
        strataDistributionData: props.reportData?.generateStrataDistributionLineChartData || { data: [], 极坚固: 25, 很坚固: 50, 坚固: 70, 比较坚固: 90, 中等坚固: 100, 较软: 130, 空洞: 150 }
      }
    case 'DrillingDepthTimeSeriesCard':
      return {
        ...baseProps,
        depthTimeSeriesData: props.reportData?.generateDrillingDepthTimeSeriesData || []
      }
    default:
      return baseProps
  }
}





/**
 * 处理卡钻开关事件 - 向父组件传递事件
 */
const handleStuckToggle = (event) => {
  // 直接向父组件传递事件，让父组件处理算法调用和数据更新
  emit('stuck-toggle', event)
}

/**
 * 处理突进开关事件 - 向父组件传递事件
 */
const handleMutationToggle = (event) => {
  // 直接向父组件传递事件，让父组件处理算法调用和数据更新
  emit('mutation-toggle', event)
}

/**
 * 触发所有图表组件的resize - 解决图表显示不完整的问题
 */
const triggerChartsResize = () => {
  // 延迟执行，确保DOM完全渲染
  const timer1 = setTimeout(() => {
    // 触发全局的resize事件，让所有ECharts实例重新计算尺寸
    window.dispatchEvent(new Event('resize'))

    // 额外延迟，确保图表组件有足够时间响应resize事件
    const timer2 = setTimeout(() => {
      // 再次触发resize事件，确保所有图表都能正确调整尺寸
      window.dispatchEvent(new Event('resize'))
    }, 200)
    chartResizeTimers.push(timer2)
  }, 100)
  chartResizeTimers.push(timer1)
}

/**
 * 跳转到模版列表
 */
const handleGoToTemplateList = () => {
  window.location.href = '/template'
}

/**
 * 刷新数据
 */
const handleRefresh = () => {
  fetchDeviceTemplates()
}

/**
 * 重试
 */
const handleRetry = () => {
  fetchDeviceTemplates()
}

// 监听设备ID变化
watch(
  () => props.deviceId,
  (newDeviceId) => {
    if (newDeviceId) {
      fetchDeviceTemplates()
    }
  },
  { immediate: true }
)

// 防抖函数，避免频繁调用
let updateSizeTimer = null
let chartResizeTimers = [] // 存储图表resize定时器

const debouncedUpdateSize = () => {
  clearTimeout(updateSizeTimer)
  updateSizeTimer = setTimeout(() => {
    updateContainerSize()
  }, 100)
}

// 监听组件可见性变化 - 当组件变为可见时更新容器尺寸
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      // 当组件变为可见时，延迟更新容器尺寸以确保DOM完全渲染
      nextTick(() => {
        debouncedUpdateSize()
        // 触发所有图表组件的resize，解决图表显示不完整的问题
        triggerChartsResize()
      })
    }
  }
)

// 监听模版数据变化，更新容器尺寸
watch(
  () => enabledTemplates.value,
  () => {
    nextTick(() => {
      debouncedUpdateSize()
      // 当模版数据变化时，也需要触发图表resize
      if (props.visible) {
        triggerChartsResize()
      }
    })
  },
  { deep: true }
)



// 监听报告数据变化，触发图表resize
watch(
  () => props.reportData,
  () => {
    if (props.visible && allComponents.value.length > 0) {
      nextTick(() => {
        triggerChartsResize()
      })
    }
  },
  { deep: true }
)

// 窗口大小变化处理 - 使用统一的防抖函数
const handleWindowResize = () => {
  debouncedUpdateSize()
}

// 组件挂载时获取数据
onMounted(() => {
  if (props.deviceId) {
    fetchDeviceTemplates()
  }

  // 初始化容器尺寸
  nextTick(() => {
    updateContainerSize()

    // 如果组件当前可见，延迟触发图表resize以确保图表正确显示
    if (props.visible) {
      const timer = setTimeout(() => {
        triggerChartsResize()
      }, 500) // 给图表组件更多时间完成初始化
      chartResizeTimers.push(timer)
    }
  })

  // 监听窗口大小变化
  window.addEventListener('resize', handleWindowResize)
})

// 组件卸载时清理事件监听
onUnmounted(() => {
  window.removeEventListener('resize', handleWindowResize)

  // 清理防抖定时器
  if (updateSizeTimer) {
    clearTimeout(updateSizeTimer)
    updateSizeTimer = null
  }

  // 清理所有图表resize定时器
  chartResizeTimers.forEach(timer => {
    clearTimeout(timer)
  })
  chartResizeTimers = []
})

    return {
      containerRef,
      enabledTemplates,
      error,
      allComponents,
      hasAnyData,
      getTemplateComponents,
      getContainerHeight,
      getComponentPositionStyle,
      getComponentProps,
      handleGoToTemplateList,
      handleRefresh,
      handleRetry,
      handleStuckToggle,
      handleMutationToggle
    }
  }
}
</script>

<style scoped>
/* 设备模版展示区域样式 */
.device-template-display {
  /* 使用calc计算宽度，占满父容器的完整宽度包括padding */
  width: calc(100% + 40px);
  /* 设置最小高度，与AnalysisReportDisplay保持一致，避免切换时的布局抖动 */
  min-height: 600px;
  /* 使用负边距占满父容器的padding空间，实现完整展示无留白 */
  margin: -20px;
}

/* 当使用v-show隐藏时，确保不占用布局空间 */
.device-template-display[style*="display: none"] {
  display: none !important;
}

/* 提示选择条件样式 */
.no-file-selected {
  padding: 60px 0;
  text-align: center;
}

.search-options {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-top: 16px;
}

/* 无数据状态样式 */
.no-data-container {
  padding: 60px 20px;
  text-align: center;
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  /* 抵消父容器的负边距影响，确保与AnalysisReportDisplay高度一致 */
  margin: 20px;
}

.no-data-tips {
  margin-top: 20px;
  color: #909399;
  font-size: 14px;
  line-height: 1.6;
}

.no-data-tips p {
  margin: 8px 0;
}

.no-data-tips ul {
  text-align: left;
  display: inline-block;
  margin: 12px 0;
  padding-left: 20px;
}

.no-data-tips li {
  margin: 6px 0;
  color: #606266;
}

/* 加载状态样式 */
.loading-container {
  /* 调整padding以适应负边距，保持原有的视觉效果 */
  padding: 60px 40px;
}

/* 模版内容样式 */
.template-content {
  /* 确保模版内容占满父元素的完整宽度 */
  width: 100%;
  /* 移除padding，避免与AnalysisReportDisplay切换时产生布局抖动 */
}



/* 模版组件容器样式 */
.template-components-container {
  position: relative;
  /* 确保容器占满父元素的完整宽度 */
  width: 100%;
  /* 恢复背景色，作为完整的模版展示区域 */
  background: #f8f9fa;
  /* 恢复圆角，提供更好的视觉效果 */
  border-radius: 8px;
  /* 恢复内边距，为模版组件提供合适的间距 */
  padding: 20px;
  overflow: auto;
  /* 设置稳定的最小高度，避免切换时的布局抖动 */
  min-height: 600px;
  /* 移除过渡效果，避免切换时的动画抖动 */
}

/* 模版组件样式 */
.template-component {
  border: 1px solid var(--el-border-color-light);
  border-radius: 6px;
  background: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  /* 移除过渡效果，避免切换时的动画干扰 */
}

.template-component:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  /* 移除transform，避免切换时的布局变化 */
}



/* 无模版状态样式 */
.no-template {
  /* 调整padding以适应负边距，保持原有的视觉效果 */
  padding: 80px 20px;
  text-align: center;
}

.empty-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-top: 16px;
}

/* 错误状态样式 */
.error-container {
  /* 调整margin以适应负边距，保持原有的视觉效果 */
  margin: 40px 20px;
}
</style>

<template>
  <!-- 分析报告展示区域 -->
  <div class="analysis-report-display">
    <!-- 显示加载状态 -->
    <div
      v-if="loading"
      class="loading-container"
    >
      <el-skeleton
        :rows="18"
        animated
      />
    </div>

    <!-- 显示无数据状态 -->
    <div
      v-else-if="!hasAnyData"
      class="no-data-container"
    >
      <el-empty
        description="暂无分析数据"
        :image-size="200"
      >
        <div class="no-data-tips">
          <p>当前查询条件下没有找到相关数据</p>
          <p>请尝试：</p>
          <ul>
            <li>选择其他文件进行分析</li>
            <li>调整时间范围或孔号条件</li>
            <li>检查设备是否有数据上传</li>
          </ul>
        </div>
      </el-empty>
    </div>

    <!-- 有数据时显示所有图表组件 -->
    <div v-else>
      <!-- 第一行：岩石性质、围岩统计、地质分析 -->
      <div class="report-cards">
        <!-- 使用拆分后的组件，根据配置决定是否显示 -->
        <RockPropertyCard
          v-if="isChartEnabled('RockPropertyCard')"
          :rocky-nature-data="reportData.generateRockyNaturePieChartData"
        />
        <RockStatsCard
          v-if="isChartEnabled('RockStatsCard')"
          :perimeter-rock-data="reportData.generatePerimeterRockStatisticsBarChartData"
        />
        <GeologicAnalysisCard
          v-if="isChartEnabled('GeologicAnalysisCard')"
          :geologic-analysis-report="reportData.generateGeologicAnalysisReport"
        />
      </div>

      <!-- 使用拆分后的岩层分布组件 -->
      <StrataDistributionCard
        v-if="isChartEnabled('StrataDistributionCard')"
        :strata-distribution-data="reportData.generateStrataDistributionLineChartData"
      />

      <!-- 使用拆分后的钻进数据曲线图组件 -->
      <DrillCurveChartCard
        v-if="isChartEnabled('DrillCurveChartCard')"
        :drilling-data="reportData.generateDrillingData"
        :filtering-data="reportData.generateDrillingCurveFilteringData"
        :stuck-data="reportData.generateDetectStuckEventsData"
        :mutation-data="reportData.generateDrillingSpeedMutationData"
        :filter-stuck-data="reportData.generateCurveFilterDetectStuckEventsData"
        :filter-mutation-data="reportData.generateCurveFilterDrillingSpeedMutationData"
        @stuck-toggle="$emit('stuck-toggle', $event)"
        @mutation-toggle="$emit('mutation-toggle', $event)"
      />

      <!-- 使用拆分后的3D可视化分析组件 -->
      <Drilling3DAnalysisCard
        v-if="isChartEnabled('Drilling3DAnalysisCard')"
        :drilling-data="reportData.generateDrillingData"
      />

      <!-- 使用拆分后的钻进数据树状图组件 -->
      <DataTreeChartCard
        v-if="isChartEnabled('DataTreeChartCard')"
        :drilling-data="reportData.generateDrillingData"
      />

      <!-- 使用拆分后的工况矩阵组件 -->
      <ConditionMatrixCard
        v-if="isChartEnabled('ConditionMatrixCard')"
        :condition-matrix-data="reportData.generateConditionMatrixChartData"
        :drilling-data="reportData.generateDrillingData"
      />

      <!-- 使用拆分后的钻进数据表格组件 -->
      <DrillingDataTableCard
        v-if="isChartEnabled('DrillingDataTableCard')"
        :drilling-data="reportData.generateDrillingData"
      />

      <!-- 使用拆分后的钻探记录表组件 -->
      <DrillingRecordTableCard
        v-if="isChartEnabled('DrillingRecordTableCard')"
        :drilling-record-data="reportData.generateDrillingRecordData"
      />

      <!-- 使用钻进深度时序分析组件 -->
      <DrillingDepthTimeSeriesCard
        v-if="isChartEnabled('DrillingDepthTimeSeriesCard')"
        :depth-time-series-data="reportData.generateDrillingDepthTimeSeriesData"
      />
    </div>
  </div>
</template>

<script>
// 导入Vue函数
import { computed } from 'vue'
// 导入拆分后的组件
import RockPropertyCard from '@/components/dashboard/RockPropertyCard.vue'
import RockStatsCard from '@/components/dashboard/RockStatsCard.vue'
import GeologicAnalysisCard from '@/components/dashboard/GeologicAnalysisCard.vue'
import StrataDistributionCard from '@/components/dashboard/StrataDistributionCard.vue'
import Drilling3DAnalysisCard from '@/components/dashboard/Drilling3DAnalysisCard.vue'
import DataTreeChartCard from '@/components/dashboard/DataTreeChartCard.vue'
import DrillCurveChartCard from '@/components/dashboard/DrillCurveChartCard.vue'
import ConditionMatrixCard from '@/components/dashboard/ConditionMatrixCard.vue'
import DrillingDataTableCard from '@/components/dashboard/DrillingDataTableCard.vue'
import DrillingRecordTableCard from '@/components/dashboard/DrillingRecordTableCard.vue'
import DrillingDepthTimeSeriesCard from '@/components/dashboard/DrillingDepthTimeSeriesCard.vue'

export default {
  name: 'AnalysisReportDisplay',
  components: {
    RockPropertyCard,
    RockStatsCard,
    GeologicAnalysisCard,
    StrataDistributionCard,
    Drilling3DAnalysisCard,
    DataTreeChartCard,
    DrillCurveChartCard,
    ConditionMatrixCard,
    DrillingDataTableCard,
    DrillingRecordTableCard,
    DrillingDepthTimeSeriesCard
  },
  props: {
    // 加载状态
    loading: {
      type: Boolean,
      default: false
    },

    // 报告数据
    reportData: {
      type: Object,
      default: () => ({
        rockyNatureData: [],
        perimeterRockData: [],
        geologicAnalysisReport: [],
        strataDistributionData: { data: [], 极坚固: 25, 很坚固: 50, 坚固: 70, 比较坚固: 90, 中等坚固: 100, 较软: 130, 空洞: 150 },
        conditionMatrixData: []
      })
    },

    // 可用图表配置
    availableCharts: {
      type: Array,
      default: () => []
    }
  },
  emits: ['stuck-toggle', 'mutation-toggle'],

  setup(props) {
    /**
     * 判断图表是否启用
     * @param {string} componentName - 组件名称
     * @returns {boolean} 是否启用
     */
    const isChartEnabled = (componentName) => {
      // 接口只返回已启用的图表组件，所以只需要检查组件是否在列表中
      // 如果配置列表为空，默认显示所有图表
      if (props.availableCharts.length === 0) {
        return true
      }

      return props.availableCharts.some(chart => chart.component === componentName)
    }

    /**
     * 检查是否有任何数据
     * @returns {boolean} 是否有数据
     */
    const hasAnyData = computed(() => {
      if (!props.reportData) {
        return false
      }

      // 检查各种数据源是否有内容
      const dataChecks = [
        // 岩石性质数据
        props.reportData.generateRockyNaturePieChartData &&
        Array.isArray(props.reportData.generateRockyNaturePieChartData) &&
        props.reportData.generateRockyNaturePieChartData.length > 0,

        // 围岩统计数据
        props.reportData.generatePerimeterRockStatisticsBarChartData &&
        Array.isArray(props.reportData.generatePerimeterRockStatisticsBarChartData) &&
        props.reportData.generatePerimeterRockStatisticsBarChartData.length > 0,

        // 地质分析报告
        props.reportData.generateGeologicAnalysisReport &&
        Array.isArray(props.reportData.generateGeologicAnalysisReport) &&
        props.reportData.generateGeologicAnalysisReport.length > 0,

        // 岩层分布数据
        props.reportData.generateStrataDistributionLineChartData &&
        props.reportData.generateStrataDistributionLineChartData.data &&
        Array.isArray(props.reportData.generateStrataDistributionLineChartData.data) &&
        props.reportData.generateStrataDistributionLineChartData.data.length > 0,

        // 钻进数据
        props.reportData.generateDrillingData &&
        Array.isArray(props.reportData.generateDrillingData) &&
        props.reportData.generateDrillingData.length > 0,

        // 钻机报告数据
        props.reportData.generateDrillingRecordData && 
        Array.isArray(props.reportData.generateDrillingRecordData) &&
        props.reportData.generateDrillingRecordData.length > 0,

        // 工况矩阵数据
        props.reportData.generateConditionMatrixChartData &&
        Array.isArray(props.reportData.generateConditionMatrixChartData) &&
        props.reportData.generateConditionMatrixChartData.length > 0,

        // 钻进深度时序数据
        props.reportData.generateDrillingDepthTimeSeriesData && 
        Array.isArray(props.reportData.generateDrillingDepthTimeSeriesData) &&
        props.reportData.generateDrillingDepthTimeSeriesData.length > 0
      ]

      // 只要有任何一个数据源有内容，就认为有数据
      return dataChecks.some(check => check === true)
    })

    return {
      isChartEnabled,
      hasAnyData
    }
  }
}
</script>

<style scoped>
/* 分析报告展示区域样式 */
.analysis-report-display {
  width: 100%;
  /* 设置最小高度，与DeviceTemplateDisplay保持一致，避免切换时的布局抖动 */
  min-height: 600px;
}

/* 当使用v-show隐藏时，确保不占用布局空间 */
.analysis-report-display[style*="display: none"] {
  display: none !important;
}



/* 加载状态样式 */
.loading-container {
  padding: 40px;
}

/* 报告卡片布局 */
.report-cards {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  flex-wrap: wrap; /* 允许卡片在必要时换行 */
}

.report-cards > .rock-property-card {
  flex: 1;
  min-width: 300px; /* 设置最小宽度防止卡片过窄 */
  margin-bottom: 20px; /* 在卡片换行时增加下边距 */
}

/* 无数据状态样式 */
.no-data-container {
  padding: 60px 20px;
  text-align: center;
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.no-data-tips {
  margin-top: 20px;
  color: #909399;
  font-size: 14px;
  line-height: 1.6;
}

.no-data-tips p {
  margin: 8px 0;
}

.no-data-tips ul {
  text-align: left;
  display: inline-block;
  margin: 12px 0;
  padding-left: 20px;
}

.no-data-tips li {
  margin: 6px 0;
  color: #606266;
}
</style>

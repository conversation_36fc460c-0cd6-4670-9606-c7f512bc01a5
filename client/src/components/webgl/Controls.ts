/**
 * WebGL控制模块
 * 处理用户输入和交互
 */
import { vec3, mat4 } from 'gl-matrix';
import { Camera } from './Camera';

export class Controls {
  camera: Camera;
  canvas: HTMLCanvasElement;
  mode: string;
  isDragging: boolean;
  lastMouseX: number;
  lastMouseY: number;
  rotation: vec3;
  scale: number;
  keyState: Record<string, boolean>;
  autoRotation: number;
  isMoving: boolean;
  needsUpdate: boolean;

  constructor(camera: Camera, canvas: HTMLCanvasElement) {
    this.camera = camera;
    this.canvas = canvas;
    this.mode = 'vertical';
    this.isDragging = false;
    this.lastMouseX = 0;
    this.lastMouseY = 0;
    this.rotation = vec3.create();
    this.scale = 1.0;
    this.keyState = {};
    this.autoRotation = 0;
    this.isMoving = false;
    this.needsUpdate = true;
    this.initEventListeners();
  }

  /**
   * 初始化事件监听器
   */
  initEventListeners(): void {
    this.canvas.addEventListener('mousedown', this.onMouseDown.bind(this));
    this.canvas.addEventListener('mousemove', this.onMouseMove.bind(this));
    this.canvas.addEventListener('mouseup', this.onMouseUp.bind(this));
    this.canvas.addEventListener('wheel', this.onWheel.bind(this));
    document.addEventListener('keydown', this.onKeyDown.bind(this));
    document.addEventListener('keyup', this.onKeyUp.bind(this));
    
    // 禁用右键菜单
    this.canvas.addEventListener('contextmenu', (e: Event) => e.preventDefault());
  }

  /**
   * 移除事件监听器
   */
  removeEventListeners(): void {
    this.canvas.removeEventListener('mousedown', this.onMouseDown.bind(this));
    this.canvas.removeEventListener('mousemove', this.onMouseMove.bind(this));
    this.canvas.removeEventListener('mouseup', this.onMouseUp.bind(this));
    this.canvas.removeEventListener('wheel', this.onWheel.bind(this));
    document.removeEventListener('keydown', this.onKeyDown.bind(this));
    document.removeEventListener('keyup', this.onKeyUp.bind(this));
  }

  /**
   * 设置交互模式
   * @param mode 模式类型 ('vertical' | 'free')
   */
  setMode(mode: string): void {
    this.mode = mode;
    if (mode === 'free') {
      this.resetToTopView();
    } else {
      this.reset();
    }
  }

  /**
   * 重置控制状态
   */
  reset(): void {
    this.rotation = vec3.create();
    this.scale = 1.0;
    this.camera.reset();
    this.keyState = {};
    this.needsUpdate = true;
  }

  /**
   * 重置为顶视图
   */
  resetToTopView(): void {
    this.scale = 1.0;
    this.keyState = {};
    this.rotation = vec3.fromValues(-Math.PI/2, 0, 0);
    this.camera.position = vec3.fromValues(0, 0, -15.0);
    this.camera.target = vec3.fromValues(0, 0, 0);
    this.camera.up = vec3.fromValues(0, 1, 0);
    this.camera.updateViewMatrix();
    this.needsUpdate = true;
  }

  /**
   * 鼠标按下事件处理
   * @param e 鼠标事件
   */
  onMouseDown(e: MouseEvent): void {
    this.isDragging = true;
    this.isMoving = true;
    this.lastMouseX = e.clientX;
    this.lastMouseY = e.clientY;
  }

  /**
   * 鼠标移动事件处理
   * @param e 鼠标事件
   */
  onMouseMove(e: MouseEvent): void {
    if (!this.isDragging) return;

    const deltaX = e.clientX - this.lastMouseX;
    const deltaY = e.clientY - this.lastMouseY;

    if (this.mode === 'vertical') {
      if (e.buttons === 1) { // 左键
        this.rotation[1] += deltaX * 0.01;
        this.camera.position[1] += deltaY * 0.01;
        this.camera.target[1] += deltaY * 0.01;
        this.camera.updateViewMatrix();
        this.needsUpdate = true;
      }
    } else if (this.mode === 'free') {
      if (e.buttons === 1) { // 左键 - 物体旋转
        this.rotation[1] += deltaX * 0.01;
        this.rotation[0] += deltaY * 0.01;
        this.needsUpdate = true;
      } else if (e.buttons === 2) { // 右键 - 相机视角旋转
        const yaw = deltaX * 0.01;
        const pitch = deltaY * 0.01;
        this.camera.rotate(yaw, pitch);
        this.needsUpdate = true;
      }
    }

    this.lastMouseX = e.clientX;
    this.lastMouseY = e.clientY;
  }

  /**
   * 鼠标释放事件处理
   */
  onMouseUp(): void {
    this.isDragging = false;
    
    // 设置一个短时间内仍保持移动状态，确保视角平滑过渡
    setTimeout(() => {
      this.isMoving = false;
    }, 100);
  }

  /**
   * 滚轮事件处理
   * @param e 滚轮事件
   */
  onWheel(e: WheelEvent): void {
    e.preventDefault();
    const delta = Math.sign(e.deltaY) * -0.1;
    this.scale = Math.max(0.5, Math.min(2.0, this.scale + delta));
    this.needsUpdate = true;
  }

  /**
   * 键盘按下事件处理
   * @param e 键盘事件
   */
  onKeyDown(e: KeyboardEvent): void {
    if (this.mode !== 'free') return;
    
    this.keyState[e.key.toLowerCase()] = true;
    this.isMoving = true;
    
    switch(e.key.toLowerCase()) {
      case 'z':
        this.autoRotation = 0.02;
        this.needsUpdate = true;
        break;
      case 'c':
        this.autoRotation = -0.02;
        this.needsUpdate = true;
        break;
    }
  }

  /**
   * 键盘释放事件处理
   * @param e 键盘事件
   */
  onKeyUp(e: KeyboardEvent): void {
    if (this.mode !== 'free') return;
    
    this.keyState[e.key.toLowerCase()] = false;
    
    if (e.key.toLowerCase() === 'z' || e.key.toLowerCase() === 'c') {
      this.autoRotation = 0;
    }
    
    // 检查是否所有键都已释放
    const anyKeyPressed = Object.values(this.keyState).some(value => value);
    if (!anyKeyPressed) {
      setTimeout(() => {
        this.isMoving = false;
      }, 100);
    }
  }

  /**
   * 更新控制状态
   * @returns 是否需要更新视图
   */
  update(): boolean {
    let shouldUpdate = this.needsUpdate;
    
    if (this.mode === 'free') {
      const moveSpeed = 0.1;
      if (this.keyState['w']) { 
        this.camera.moveForward(moveSpeed);
        shouldUpdate = true;
      }
      if (this.keyState['s']) {
        this.camera.moveForward(-moveSpeed);
        shouldUpdate = true;
      }
      if (this.keyState['a']) {
        this.camera.moveRight(-moveSpeed);
        shouldUpdate = true;
      }
      if (this.keyState['d']) {
        this.camera.moveRight(moveSpeed);
        shouldUpdate = true;
      }
      if (this.keyState['q']) {
        this.camera.moveUp(-moveSpeed);
        shouldUpdate = true;
      }
      if (this.keyState['e']) {
        this.camera.moveUp(moveSpeed);
        shouldUpdate = true;
      }

      if (this.autoRotation !== 0) {
        this.rotation[1] += this.autoRotation;
        shouldUpdate = true;
      }
    }
    
    // 重置需要更新状态
    this.needsUpdate = false;
    
    return shouldUpdate || this.isMoving;
  }

  /**
   * 获取模型变换矩阵
   */
  getModelMatrix(): mat4 {
    const modelMatrix = mat4.create();
    mat4.rotateX(modelMatrix, modelMatrix, this.rotation[0]);
    mat4.rotateY(modelMatrix, modelMatrix, this.rotation[1]);
    mat4.rotateZ(modelMatrix, modelMatrix, this.rotation[2]);
    mat4.scale(modelMatrix, modelMatrix, [this.scale, this.scale, this.scale]);
    return modelMatrix;
  }
} 
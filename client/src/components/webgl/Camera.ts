/**
 * WebGL相机模块
 * 处理相机位置、视角和移动
 */
import { mat4, vec3 } from 'gl-matrix';

export class Camera {
  position: vec3;
  target: vec3;
  up: vec3;
  viewMatrix: mat4;

  constructor() {
    this.position = vec3.fromValues(0, 0, -6.0);
    this.target = vec3.fromValues(0, 0, 0);
    this.up = vec3.fromValues(0, 1, 0);
    this.viewMatrix = mat4.create();
    this.updateViewMatrix();
  }

  /**
   * 更新相机视图矩阵
   */
  updateViewMatrix(): void {
    mat4.lookAt(this.viewMatrix, this.position, this.target, this.up);
  }

  /**
   * 向前移动相机
   * @param distance 移动距离
   */
  moveForward(distance: number): void {
    const direction = vec3.create();
    vec3.subtract(direction, this.target, this.position);
    vec3.normalize(direction, direction);
    vec3.scaleAndAdd(this.position, this.position, direction, distance);
    vec3.scaleAndAdd(this.target, this.target, direction, distance);
    this.updateViewMatrix();
  }

  /**
   * 向右移动相机
   * @param distance 移动距离
   */
  moveRight(distance: number): void {
    const direction = vec3.create();
    vec3.subtract(direction, this.target, this.position);
    const right = vec3.create();
    vec3.cross(right, direction, this.up);
    vec3.normalize(right, right);
    vec3.scaleAndAdd(this.position, this.position, right, distance);
    vec3.scaleAndAdd(this.target, this.target, right, distance);
    this.updateViewMatrix();
  }

  /**
   * 向上移动相机
   * @param distance 移动距离
   */
  moveUp(distance: number): void {
    const up = vec3.clone(this.up);
    vec3.scaleAndAdd(this.position, this.position, up, distance);
    vec3.scaleAndAdd(this.target, this.target, up, distance);
    this.updateViewMatrix();
  }

  /**
   * 重置相机到默认位置
   */
  reset(): void {
    this.position = vec3.fromValues(0, 0, -6.0);
    this.target = vec3.fromValues(0, 0, 0);
    this.up = vec3.fromValues(0, 1, 0);
    this.updateViewMatrix();
  }

  /**
   * 旋转相机视角
   * @param yaw 水平旋转角度（弧度）
   * @param pitch 垂直旋转角度（弧度）
   */
  rotate(yaw: number, pitch: number): void {
    const forward = vec3.subtract(vec3.create(), this.target, this.position);
    vec3.normalize(forward, forward);
    
    const right = vec3.cross(vec3.create(), forward, this.up);
    vec3.normalize(right, right);
    
    // 水平旋转（绕Y轴）
    const rotMatY = mat4.create();
    mat4.fromRotation(rotMatY, yaw, this.up);
    vec3.transformMat4(forward, forward, rotMatY);
    
    // 垂直旋转（绕右轴）
    const rotMatX = mat4.create();
    mat4.fromRotation(rotMatX, pitch, right);
    vec3.transformMat4(forward, forward, rotMatX);
    
    vec3.normalize(forward, forward);
    const distance = vec3.distance(this.position, this.target);
    vec3.scale(forward, forward, distance);
    vec3.add(this.target, this.position, forward);
    
    this.updateViewMatrix();
  }
} 
/**
 * WebGL渲染器模块
 * 处理WebGL上下文、渲染循环和资源管理
 */
import { mat4 } from 'gl-matrix';
import { Camera } from './Camera';
import { Controls } from './Controls';
import { Cylinder } from './Cylinder';
import { initShaderProgram } from './Shaders';

export interface RendererOptions {
  canvas: HTMLCanvasElement;
  onUpdateMarkerPositions?: (positions: MarkerScreenPosition[]) => void;
}

export interface MarkerScreenPosition {
  x: number;
  y: number;
  depth: number;
  visible: boolean;
}

export interface DepthMarker {
  dpth: number;
  relativePosition: number;
  [key: string]: any;
}

export class Renderer {
  private gl: WebGLRenderingContext | null = null;
  private programInfo: any = null;
  private camera: Camera | null = null;
  private controls: Controls | null = null;
  private cylinder: Cylinder | null = null;
  private texture: WebGLTexture | null = null;
  private animationFrameId: number | null = null;
  private canvas: HTMLCanvasElement;
  private markers: DepthMarker[] = [];
  private onUpdateMarkerPositions?: (positions: MarkerScreenPosition[]) => void;
  
  // 跟踪性能指标
  private frameCount = 0;
  private lastFpsUpdateTime = 0;
  private currentFps = 0;

  constructor(options: RendererOptions) {
    this.canvas = options.canvas;
    this.onUpdateMarkerPositions = options.onUpdateMarkerPositions;
    this.initGL();
  }

  /**
   * 初始化WebGL
   */
  private initGL(): boolean {
    this.canvas.width = this.canvas.clientWidth * 2;
    this.canvas.height = this.canvas.clientHeight * 2;
    
    try {
      // 尝试获取WebGL 2.0上下文，如果不支持则回退到WebGL 1.0
      this.gl = this.canvas.getContext('webgl2') as WebGLRenderingContext;
      if (!this.gl) {
        this.gl = this.canvas.getContext('webgl') as WebGLRenderingContext;
        console.log('使用WebGL 1.0上下文');
      } else {
        console.log('使用WebGL 2.0上下文');
      }
    } catch (e) {
      console.error('无法初始化WebGL，您的浏览器可能不支持。', e);
      return false;
    }

    if (!this.gl) {
      console.error('无法初始化WebGL，您的浏览器可能不支持。');
      return false;
    }

    // 初始化着色器程序
    this.programInfo = initShaderProgram(this.gl);
    if (!this.programInfo) {
      console.error('无法初始化着色器程序。');
      return false;
    }

    // 初始化相机和控制器
    this.camera = new Camera();
    this.controls = new Controls(this.camera, this.canvas);
    
    return true;
  }

  /**
   * 加载纹理
   * @param image 纹理图片
   */
  loadTexture(image: HTMLImageElement): WebGLTexture | null {
    if (!this.gl) return null;
    
    const textureObj = this.gl.createTexture();
    if (!textureObj) return null;
    
    this.gl.bindTexture(this.gl.TEXTURE_2D, textureObj);
    this.gl.texImage2D(this.gl.TEXTURE_2D, 0, this.gl.RGBA, this.gl.RGBA, this.gl.UNSIGNED_BYTE, image);
    
    // 检查图片的尺寸是否是2的幂
    if (this.isPowerOf2(image.width) && this.isPowerOf2(image.height)) {
      this.gl.generateMipmap(this.gl.TEXTURE_2D);
    } else {
      // 非2的幂纹理需要设置合适的参数
      this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_WRAP_S, this.gl.CLAMP_TO_EDGE);
      this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_WRAP_T, this.gl.CLAMP_TO_EDGE);
      this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_MIN_FILTER, this.gl.LINEAR);
    }
    
    return textureObj;
  }

  /**
   * 判断是否是2的幂
   */
  private isPowerOf2(value: number): boolean {
    return (value & (value - 1)) === 0;
  }

  /**
   * 生成圆柱体
   * @param height 高度
   * @param radius 半径
   * @param image 纹理图片
   */
  generateCylinder(height: number, radius: number, image: HTMLImageElement): void {
    if (!this.gl) return;
    
    // 生成纹理
    this.texture = this.loadTexture(image);
    
    // 如果已存在圆柱体，则更新几何形状
    if (this.cylinder) {
      this.cylinder.updateGeometry(height, radius);
    } else {
      // 否则创建新的圆柱体
      this.cylinder = new Cylinder(this.gl, height, radius);
    }
    
    // 如果控制器存在，重置控制器
    if (this.controls) {
      this.controls.reset();
    }
    
    // 开始渲染循环
    this.startRenderLoop();
  }

  /**
   * 设置交互模式
   * @param mode 交互模式
   */
  setInteractionMode(mode: string): void {
    if (this.controls) {
      this.controls.setMode(mode);
    }
  }

  /**
   * 设置深度标记
   * @param markers 标记数据
   */
  setMarkers(markers: DepthMarker[]): void {
    this.markers = markers;
  }

  /**
   * 开始渲染循环
   */
  private startRenderLoop(): void {
    // 停止当前的动画循环
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }
    
    // 重置性能计数器
    this.frameCount = 0;
    this.lastFpsUpdateTime = performance.now();
    
    // 开始新的渲染循环
    this.render();
  }

  /**
   * 渲染函数
   */
  private render(): void {
    if (!this.gl || !this.programInfo || !this.cylinder || !this.camera || !this.controls) return;

    // 检查是否需要更新
    const needsUpdate = this.controls.update();
    
    // 如果不需要更新且没有其他变动，跳过此帧渲染
    if (!needsUpdate && this.frameCount > 2) { // 至少渲染前几帧确保画面稳定
      this.animationFrameId = requestAnimationFrame(this.render.bind(this));
      return;
    }
    
    // 更新性能统计
    this.frameCount++;
    const now = performance.now();
    const elapsed = now - this.lastFpsUpdateTime;
    
    if (elapsed >= 1000) { // 每秒更新FPS
      this.currentFps = Math.round((this.frameCount * 1000) / elapsed);
      this.frameCount = 0;
      this.lastFpsUpdateTime = now;
      // console.log(`FPS: ${this.currentFps}`);
    }
    
    // 清除画布
    this.gl.clearColor(0.0, 0.0, 0.0, 1.0);
    this.gl.clearDepth(1.0);
    this.gl.enable(this.gl.DEPTH_TEST);
    this.gl.depthFunc(this.gl.LEQUAL);
    this.gl.clear(this.gl.COLOR_BUFFER_BIT | this.gl.DEPTH_BUFFER_BIT);

    // 设置透视投影
    const fieldOfView = 45 * Math.PI / 180;
    const aspect = this.canvas.clientWidth / this.canvas.clientHeight;
    const zNear = 0.1;
    const zFar = 100.0;
    const projectionMatrix = mat4.create();
    mat4.perspective(projectionMatrix, fieldOfView, aspect, zNear, zFar);
    
    // 获取模型矩阵
    const modelMatrix = this.controls.getModelMatrix();
    const modelViewMatrix = mat4.create();
    mat4.multiply(modelViewMatrix, this.camera.viewMatrix, modelMatrix);

    // 设置着色器程序
    this.gl.useProgram(this.programInfo.program);
    this.gl.uniformMatrix4fv(
      this.programInfo.uniformLocations.projectionMatrix,
      false,
      projectionMatrix
    );
    this.gl.uniformMatrix4fv(
      this.programInfo.uniformLocations.modelViewMatrix,
      false,
      modelViewMatrix
    );
    
    // 设置是否为标记
    this.gl.uniform1i(this.programInfo.uniformLocations.isMarker, 0);

    // 激活纹理
    if (this.texture) {
      this.gl.activeTexture(this.gl.TEXTURE0);
      this.gl.bindTexture(this.gl.TEXTURE_2D, this.texture);
      this.gl.uniform1i(this.programInfo.uniformLocations.uSampler, 0);
    }

    // 绘制圆柱体
    this.cylinder.draw(this.programInfo);
    
    // 更新标记点位置
    this.updateMarkerPositions(modelViewMatrix, projectionMatrix);
    
    // 继续渲染循环
    this.animationFrameId = requestAnimationFrame(this.render.bind(this));
  }

  /**
   * 更新标记点位置
   */
  private updateMarkerPositions(modelViewMatrix: mat4, projectionMatrix: mat4): void {
    if (!this.markers.length || !this.onUpdateMarkerPositions) return;
    
    const viewport: [number, number, number, number] = [
      0, 0, this.canvas.clientWidth, this.canvas.clientHeight
    ];
    
    const markerPositions: MarkerScreenPosition[] = [];
    
    this.markers.forEach((marker) => {
      if (marker.relativePosition === undefined) return;
      
      // 根据相对位置计算在圆柱体上的3D坐标
      const cylinder = this.cylinder!;
      const y = cylinder.height * (marker.relativePosition - 0.5);
      const radius = cylinder.radius;
      
      // 标记点在圆柱体正面
      const angle = 0;
      const x = radius * Math.cos(angle);
      const z = radius * Math.sin(angle);
      
      // 投影到屏幕
      const screenPos = this.projectToScreen([x, y, z], modelViewMatrix, projectionMatrix, viewport);
      
      markerPositions.push({
        x: screenPos.x,
        y: screenPos.y,
        depth: marker.dpth,
        visible: screenPos.visible
      });
    });
    
    this.onUpdateMarkerPositions(markerPositions);
  }

  /**
   * 将3D坐标投影到屏幕坐标
   */
  private projectToScreen(
    worldPos: [number, number, number], 
    modelViewMatrix: mat4, 
    projectionMatrix: mat4, 
    viewport: [number, number, number, number]
  ) {
    // 转换为齐次坐标
    const worldVec = [worldPos[0], worldPos[1], worldPos[2], 1.0];
    const clipPos = [0, 0, 0, 0];
    
    // 应用model-view变换和投影矩阵
    for (let i = 0; i < 4; i++) {
      clipPos[i] = 0;
      for (let j = 0; j < 4; j++) {
        clipPos[i] += modelViewMatrix[i + 4 * j] * worldVec[j];
      }
    }
    
    const projectedPos = [0, 0, 0, 0];
    for (let i = 0; i < 4; i++) {
      projectedPos[i] = 0;
      for (let j = 0; j < 4; j++) {
        projectedPos[i] += projectionMatrix[i + 4 * j] * clipPos[j];
      }
    }
    
    // 透视除法
    if (projectedPos[3] !== 0) {
      projectedPos[0] /= projectedPos[3];
      projectedPos[1] /= projectedPos[3];
      projectedPos[2] /= projectedPos[3];
    }
    
    // 转换到屏幕坐标
    const screenX = (projectedPos[0] * 0.5 + 0.5) * viewport[2] + viewport[0];
    const screenY = (1.0 - (projectedPos[1] * 0.5 + 0.5)) * viewport[3] + viewport[1];
    const depth = projectedPos[2];
    
    // 判断点是否在可视范围内
    const visible = depth > -1.0 && depth < 1.0 && 
                    screenX >= 0 && screenX <= viewport[2] && 
                    screenY >= 0 && screenY <= viewport[3];
    
    return {
      x: screenX,
      y: screenY,
      visible: visible
    };
  }

  /**
   * 调整大小
   */
  resize(): void {
    if (!this.gl || !this.canvas) return;
    
    // 调整canvas尺寸
    this.canvas.width = this.canvas.clientWidth * 2;
    this.canvas.height = this.canvas.clientHeight * 2;
    this.gl.viewport(0, 0, this.canvas.width, this.canvas.height);
    
    // 标记需要更新
    if (this.controls) {
      this.controls.needsUpdate = true;
    }
  }

  /**
   * 截取当前帧
   * @returns 图像数据URL
   */
  captureFrame(): string | null {
    return this.canvas.toDataURL('image/png');
  }

  /**
   * 清理资源
   */
  dispose(): void {
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }
    
    if (this.controls) {
      this.controls.removeEventListeners();
    }
    
    if (this.cylinder) {
      this.cylinder.dispose();
    }
    
    if (this.gl && this.texture) {
      this.gl.deleteTexture(this.texture);
    }
    
    if (this.gl && this.programInfo) {
      this.gl.deleteProgram(this.programInfo.program);
    }
    
    this.gl = null;
    this.programInfo = null;
    this.camera = null;
    this.controls = null;
    this.cylinder = null;
    this.texture = null;
  }
} 
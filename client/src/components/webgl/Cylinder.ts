/**
 * WebGL圆柱体模型模块
 * 处理圆柱体几何生成和渲染
 */

export class Cylinder {
  gl: WebGLRenderingContext;
  height: number;
  radius: number;
  segments: number;
  positions: number[];
  indices: number[];
  textureCoords: number[];
  normals: number[]; // 新增法线数据
  positionBuffer: WebGLBuffer | null;
  textureCoordBuffer: WebGLBuffer | null;
  indexBuffer: WebGLBuffer | null;
  normalBuffer: WebGLBuffer | null; // 法线缓冲区

  constructor(gl: WebGLRenderingContext, height: number, radius: number, segments = 36) {
    this.gl = gl;
    this.height = height;
    this.radius = radius;
    this.segments = segments;
    this.positions = [];
    this.indices = [];
    this.textureCoords = [];
    this.normals = [];
    this.positionBuffer = null;
    this.textureCoordBuffer = null;
    this.indexBuffer = null;
    this.normalBuffer = null;

    this.initBuffers();
  }

  /**
   * 初始化缓冲区
   */
  initBuffers(): void {
    const gl = this.gl;
    
    // 生成顶点、法线和纹理坐标
    for (let i = 0; i <= this.segments; i++) {
      const theta = (i * Math.PI * 2) / this.segments;
      const x = this.radius * Math.cos(theta);
      const z = this.radius * Math.sin(theta);

      // 顶部和底部圆的顶点
      this.positions.push(x, this.height / 2, z);
      this.positions.push(x, -this.height / 2, z);

      // 法线 - 指向圆柱体表面外侧
      const nx = Math.cos(theta);
      const nz = Math.sin(theta);
      this.normals.push(nx, 0, nz);
      this.normals.push(nx, 0, nz);

      // 纹理坐标
      const u = i / this.segments;
      this.textureCoords.push(u, 0);
      this.textureCoords.push(u, 1);
    }

    // 生成索引
    for (let i = 0; i < this.segments * 2; i += 2) {
      this.indices.push(i, i + 1, i + 2);
      this.indices.push(i + 1, i + 3, i + 2);
    }

    // 创建并绑定顶点缓冲区
    this.positionBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, this.positionBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(this.positions), gl.STATIC_DRAW);

    // 创建并绑定法线缓冲区
    this.normalBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, this.normalBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(this.normals), gl.STATIC_DRAW);

    // 创建并绑定纹理坐标缓冲区
    this.textureCoordBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, this.textureCoordBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(this.textureCoords), gl.STATIC_DRAW);

    // 创建并绑定索引缓冲区
    this.indexBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, this.indexBuffer);
    gl.bufferData(gl.ELEMENT_ARRAY_BUFFER, new Uint16Array(this.indices), gl.STATIC_DRAW);
  }

  /**
   * 绘制圆柱体
   * @param programInfo 着色器程序信息
   */
  draw(programInfo: any): void {
    const gl = this.gl;

    // 绑定顶点位置缓冲区
    gl.bindBuffer(gl.ARRAY_BUFFER, this.positionBuffer);
    gl.vertexAttribPointer(
      programInfo.attribLocations.vertexPosition,
      3,
      gl.FLOAT,
      false,
      0,
      0
    );
    gl.enableVertexAttribArray(programInfo.attribLocations.vertexPosition);

    // 如果着色器支持法线，则绑定法线缓冲区
    if (programInfo.attribLocations.vertexNormal !== undefined) {
      gl.bindBuffer(gl.ARRAY_BUFFER, this.normalBuffer);
      gl.vertexAttribPointer(
        programInfo.attribLocations.vertexNormal,
        3,
        gl.FLOAT,
        false,
        0,
        0
      );
      gl.enableVertexAttribArray(programInfo.attribLocations.vertexNormal);
    }

    // 绑定纹理坐标缓冲区
    gl.bindBuffer(gl.ARRAY_BUFFER, this.textureCoordBuffer);
    gl.vertexAttribPointer(
      programInfo.attribLocations.textureCoord,
      2,
      gl.FLOAT,
      false,
      0,
      0
    );
    gl.enableVertexAttribArray(programInfo.attribLocations.textureCoord);

    // 绑定索引缓冲区
    gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, this.indexBuffer);

    // 绘制圆柱体
    gl.drawElements(
      gl.TRIANGLES,
      this.indices.length,
      gl.UNSIGNED_SHORT,
      0
    );
  }

  /**
   * 更新几何形状参数
   * @param height 新高度
   * @param radius 新半径
   */
  updateGeometry(height: number, radius: number): void {
    this.height = height;
    this.radius = radius;
    
    // 清空现有数据
    this.positions = [];
    this.indices = [];
    this.textureCoords = [];
    this.normals = [];
    
    // 重新生成缓冲区
    this.initBuffers();
  }

  /**
   * 清理资源
   */
  dispose(): void {
    const gl = this.gl;
    if (this.positionBuffer) gl.deleteBuffer(this.positionBuffer);
    if (this.normalBuffer) gl.deleteBuffer(this.normalBuffer);
    if (this.textureCoordBuffer) gl.deleteBuffer(this.textureCoordBuffer);
    if (this.indexBuffer) gl.deleteBuffer(this.indexBuffer);
    
    this.positionBuffer = null;
    this.normalBuffer = null;
    this.textureCoordBuffer = null;
    this.indexBuffer = null;
  }
} 
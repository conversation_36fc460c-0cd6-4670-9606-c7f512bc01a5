/**
 * WebGL着色器程序模块
 * 负责管理顶点着色器和片段着色器
 */

// 顶点着色器程序
export const vertexShaderSource = `
  attribute vec4 aVertexPosition;
  attribute vec2 aTextureCoord;

  uniform mat4 uModelViewMatrix;
  uniform mat4 uProjectionMatrix;
  uniform mat4 uMarkerModelMatrix;
  uniform bool uIsMarker;

  varying highp vec2 vTextureCoord;

  void main() {
    if (uIsMarker) {
      gl_Position = uProjectionMatrix * uModelViewMatrix * uMarkerModelMatrix * aVertexPosition;
    } else {
      gl_Position = uProjectionMatrix * uModelViewMatrix * aVertexPosition;
    }
    vTextureCoord = aTextureCoord;
  }
`;

// 片段着色器程序
export const fragmentShaderSource = `
  precision mediump float;
  
  varying highp vec2 vTextureCoord;
  uniform sampler2D uSampler;
  uniform vec4 uMarkerColor;
  uniform bool uIsMarker;

  void main() {
    if (uIsMarker) {
      gl_FragColor = uMarkerColor;
    } else {
      gl_FragColor = texture2D(uSampler, vTextureCoord);
    }
  }
`;

/**
 * 加载着色器
 * @param gl WebGL上下文
 * @param type 着色器类型 (顶点/片段)
 * @param source 着色器源代码
 */
export function loadShader(gl: WebGLRenderingContext, type: number, source: string): WebGLShader | null {
  const shader = gl.createShader(type);
  if (!shader) return null;

  gl.shaderSource(shader, source);
  gl.compileShader(shader);

  if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
    console.error('着色器编译错误: ' + gl.getShaderInfoLog(shader));
    gl.deleteShader(shader);
    return null;
  }

  return shader;
}

/**
 * 初始化着色器程序
 * @param gl WebGL上下文
 */
export function initShaderProgram(gl: WebGLRenderingContext) {
  const vertexShader = loadShader(gl, gl.VERTEX_SHADER, vertexShaderSource);
  const fragmentShader = loadShader(gl, gl.FRAGMENT_SHADER, fragmentShaderSource);

  if (!vertexShader || !fragmentShader) return null;

  const shaderProgram = gl.createProgram();
  if (!shaderProgram) return null;
  
  gl.attachShader(shaderProgram, vertexShader);
  gl.attachShader(shaderProgram, fragmentShader);
  gl.linkProgram(shaderProgram);

  if (!gl.getProgramParameter(shaderProgram, gl.LINK_STATUS)) {
    console.error('无法初始化着色器程序: ' + gl.getProgramInfoLog(shaderProgram));
    return null;
  }

  return {
    program: shaderProgram,
    attribLocations: {
      vertexPosition: gl.getAttribLocation(shaderProgram, 'aVertexPosition'),
      textureCoord: gl.getAttribLocation(shaderProgram, 'aTextureCoord'),
    },
    uniformLocations: {
      projectionMatrix: gl.getUniformLocation(shaderProgram, 'uProjectionMatrix'),
      modelViewMatrix: gl.getUniformLocation(shaderProgram, 'uModelViewMatrix'),
      uSampler: gl.getUniformLocation(shaderProgram, 'uSampler'),
      markerModelMatrix: gl.getUniformLocation(shaderProgram, 'uMarkerModelMatrix'),
      markerColor: gl.getUniformLocation(shaderProgram, 'uMarkerColor'),
      isMarker: gl.getUniformLocation(shaderProgram, 'uIsMarker'),
    },
  };
} 
<template>
  <div class="app-wrapper">
    <div
      class="sidebar-container"
      :style="{ width: sidebarWidth }"
    >
      <Sidebar :is-collapsed="isCollapsed" />
    </div>
    <div
      class="main-wrapper"
      :style="{ marginLeft: mainWrapperMargin }"
    >
      <Header
        :is-collapsed="isCollapsed"
        @toggle-sidebar="toggleSidebar"
      />
      <div class="main-content">
        <transition
          name="fade-slide"
          mode="out-in"
        >
          <router-view v-if="isRouterViewActive" />
        </transition>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
/**
 * 主布局组件
 * 包含侧边栏、头部和主内容区域的布局管理
 */

import { ref, nextTick, onMounted, onUnmounted, computed } from 'vue'
import Header from './Header.vue'
import Sidebar from './Sidebar.vue'

// 布局配置常量
const LAYOUT_CONFIG = {
  SIDEBAR_WIDTH: {
    EXPANDED: '240px',
    COLLAPSED: '64px'
  },
  TRANSITION_DURATION: 300,
  TRANSITION_TIMING: 'cubic-bezier(0.34, 0.69, 0.1, 1)'
} as const

// 响应式状态
const isCollapsed = ref<boolean>(false)
const isRouterViewActive = ref<boolean>(true)

// 定时器引用
let sidebarToggleTimer: NodeJS.Timeout | null = null

// 计算属性
const sidebarWidth = computed(() =>
  isCollapsed.value ? LAYOUT_CONFIG.SIDEBAR_WIDTH.COLLAPSED : LAYOUT_CONFIG.SIDEBAR_WIDTH.EXPANDED
)

const mainWrapperMargin = computed(() => sidebarWidth.value)

/**
 * 切换侧边栏展开/收起状态
 * 使用优化的动画策略减少重绘
 */
const toggleSidebar = async (): Promise<void> => {
  // 清理之前的定时器
  if (sidebarToggleTimer) {
    clearTimeout(sidebarToggleTimer)
    sidebarToggleTimer = null
  }

  // 临时移除 router-view 以减少级联重绘
  isRouterViewActive.value = false

  // 等待DOM更新
  await nextTick()

  // 切换侧边栏状态
  isCollapsed.value = !isCollapsed.value

  // 等待过渡完成后再恢复 router-view
  sidebarToggleTimer = setTimeout(() => {
    isRouterViewActive.value = true
    // 手动触发 resize 事件，通知图表组件重新计算尺寸
    window.dispatchEvent(new Event('resize'))
  }, LAYOUT_CONFIG.TRANSITION_DURATION)
}

/**
 * 触发窗口resize事件
 * 用于通知子组件重新计算布局
 */
const triggerResize = (): void => {
  window.dispatchEvent(new Event('resize'))
}

// 生命周期钩子
onMounted(() => {
  // 确保初始加载时布局正确
  triggerResize()
})

onUnmounted(() => {
  // 清理定时器
  if (sidebarToggleTimer) {
    clearTimeout(sidebarToggleTimer)
    sidebarToggleTimer = null
  }
})
</script>

<style scoped>
/* 主布局容器 */
.app-wrapper {
  height: 100%;
  width: 100%;
  display: flex;
  overflow: hidden;
  position: relative;
  background-color: var(--apple-background, #f5f5f7);
}

/* 侧边栏容器 */
.sidebar-container {
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 1001;
  overflow: hidden;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
  transition: width 0.3s cubic-bezier(0.34, 0.69, 0.1, 1);
  border-right: 1px solid rgba(0, 0, 0, 0.05);
}

/* 主内容包装器 */
.main-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  position: relative;
  transition: margin-left 0.3s cubic-bezier(0.34, 0.69, 0.1, 1);
}

/* 主内容区域 */
.main-content {
  flex: 1;
  overflow: auto;
  padding: 24px;
  background-color: var(--apple-background, #f5f5f7);
  position: relative;
  min-height: calc(100vh - 64px); /* 减去header高度 */
}

/* 页面过渡动画 */
.fade-slide-enter-active,
.fade-slide-leave-active {
  transition: all 0.3s cubic-bezier(0.34, 0.69, 0.1, 1);
}

.fade-slide-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.fade-slide-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

/* 自定义滚动条样式 */
.main-content::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.main-content::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 4px;
}

.main-content::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.main-content::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.3);
}

.main-content::-webkit-scrollbar-corner {
  background: transparent;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar-container {
    transform: translateX(-100%);
    transition: transform 0.3s cubic-bezier(0.34, 0.69, 0.1, 1);
  }

  .sidebar-container.mobile-open {
    transform: translateX(0);
  }

  .main-wrapper {
    margin-left: 0 !important;
  }

  .main-content {
    padding: 16px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .sidebar-container {
    background-color: rgba(28, 28, 30, 0.8);
    border-right-color: rgba(255, 255, 255, 0.1);
  }

  .main-content::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.2);
  }

  .main-content::-webkit-scrollbar-thumb:hover {
    background-color: rgba(255, 255, 255, 0.3);
  }
}
</style>

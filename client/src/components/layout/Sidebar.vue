<template>
  <div class="sidebar">
    <div class="logo-container">
      <img
        src="@/assets/images/logo.png"
        alt="云装备"
        class="logo"
      >
      <span
        v-if="!props.isCollapsed"
        class="title"
      >海聚科技钻井数据监控系统</span>
    </div>
    <el-scrollbar>
      <el-menu
        :default-active="activeMenu"
        :collapse="props.isCollapsed"
        background-color="transparent"
        text-color="var(--apple-text-primary)"
        active-text-color="var(--apple-primary)"
        :collapse-transition="false"
        router
        unique-opened
      >
        <!-- 修改菜单项的图标包装方式 -->
        <el-menu-item
          index="/device"
          class="menu-item"
        >
          <div class="icon-wrapper">
            <el-icon><Monitor /></el-icon>
          </div>
          <template #title>
            <span>设备管理</span>
          </template>
        </el-menu-item>
        <!-- 算法管理菜单 -->
        <el-sub-menu
          index="/algorithm"
          class="menu-item"
          popper-class="sidebar-submenu"
        >
          <template #title>
            <div class="icon-wrapper">
              <el-icon><Memo /></el-icon>
            </div>
            <span>算法管理</span>
          </template>
          <el-menu-item
            index="/algorithm/cleaning"
            class="sub-menu-item"
          >
            清洗算法
          </el-menu-item>
          <el-menu-item
            index="/algorithm/analysis"
            class="sub-menu-item"
          >
            控件算法
          </el-menu-item>
        </el-sub-menu>
        <el-menu-item
          index="/overview"
          class="menu-item"
        >
          <div class="icon-wrapper">
            <el-icon><DataLine /></el-icon>
          </div>
          <template #title>
            <span>文件总览</span>
          </template>
        </el-menu-item>
        <!-- 模板管理菜单 -->
        <el-menu-item
          index="/template"
          class="menu-item"
        >
          <div class="icon-wrapper">
            <el-icon><Document /></el-icon>
          </div>
          <template #title>
            <span>模板管理</span>
          </template>
        </el-menu-item>
        <!-- 导入管理菜单 -->
        <el-menu-item
          index="/import"
          class="menu-item"
        >
          <div class="icon-wrapper">
            <el-icon><Upload /></el-icon>
          </div>
          <template #title>
            <span>导入管理</span>
          </template>
        </el-menu-item>
      </el-menu>
    </el-scrollbar>
    <div
      v-if="!props.isCollapsed"
      class="sidebar-footer"
    >
      <div class="version">
        版本 {{ appVersion }}
      </div>
      <div class="copyright">
        © {{ currentYear }} 海聚科技
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
/**
 * 侧边栏组件
 * 包含导航菜单、Logo、版本信息等
 */

import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { Document, DataLine, Monitor, Memo, Upload } from '@element-plus/icons-vue'

// 菜单项配置类型
interface MenuItem {
  index: string
  title: string
  icon: any
  children?: MenuItem[]
}

// Props定义
interface Props {
  isCollapsed?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isCollapsed: false
})

// 组合式函数
const route = useRoute()

// 菜单配置
const menuItems: MenuItem[] = [
  {
    index: '/device',
    title: '设备管理',
    icon: Monitor
  },
  {
    index: '/algorithm',
    title: '算法管理',
    icon: Memo,
    children: [
      {
        index: '/algorithm/cleaning',
        title: '清洗算法',
        icon: null
      },
      {
        index: '/algorithm/analysis',
        title: '控件算法',
        icon: null
      }
    ]
  },
  {
    index: '/overview',
    title: '文件总览',
    icon: DataLine
  },
  {
    index: '/template',
    title: '模板管理',
    icon: Document
  },
  {
    index: '/import',
    title: '导入管理',
    icon: Upload
  }
]

// 计算属性
const activeMenu = computed(() => {
  return route.meta?.activeMenu || route.path
})

// 应用版本信息
const appVersion = computed(() => {
  return import.meta.env.VITE_APP_VERSION || '1.0.0'
})

const currentYear = computed(() => {
  return new Date().getFullYear()
})
</script>

<style scoped>
/* 基础样式保持不变 */
.sidebar {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.logo-container {
  height: 64px;
  min-height: 64px; /* 添加最小高度确保不会被压缩 */
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 20px;
  overflow: hidden;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  box-sizing: border-box; /* 确保边框和内边距不会增加元素的总高度 */
}

.logo {
  width: 32px;
  height: 32px;
  margin-right: 12px;
  transition: var(--apple-transition);
}

.title {
  color: var(--apple-text-primary);
  font-size: 16px;
  font-weight: 600;
  white-space: nowrap;
  opacity: 1;
  transition: var(--apple-transition);
}

/* 菜单基础样式 */
.el-menu {
  border-right: none;
  flex: 1;
  padding: 12px;
}

/* 菜单项基础样式 */
.menu-item {
  margin-bottom: 8px;
  border-radius: 8px; /* 保持圆角在父元素上 */
  overflow: hidden; /* 保持 overflow: hidden */
  /* 移除固定高度 */
}

/* 统一菜单项和子菜单标题的基础样式 */
:deep(.el-menu-item),
:deep(.el-sub-menu__title) {
  height: 48px; /* 标准高度 */
  line-height: 48px;
  border-radius: 8px; /* 确保圆角在这里也应用 */
  transition:
    background-color 0.3s ease-out,
    color 0.3s ease-out;
  box-sizing: border-box;
}

/* 特殊处理子菜单项的高度 (如果它们确实需要不同高度) */
:deep(.el-menu:not(.el-menu--collapse) .el-menu-item.sub-menu-item) {
  height: 40px; /* 覆盖子菜单项的高度 */
  line-height: 40px !important; /* 子菜单项行高 */
  /* 其他样式保持不变 */
  padding-left: 50px !important;
  margin-bottom: 8px;
  margin-top: 0;
  box-sizing: border-box;
}

/* 激活状态的菜单项 - 确保背景色生效 */
:deep(.el-menu-item.is-active) {
  background-color: rgba(0, 113, 227, 0.1); /* 这是激活背景色 */
  color: var(--apple-primary);
  font-weight: 500;
}

/* 悬停状态 */
:deep(.el-menu-item:hover),
:deep(.el-sub-menu__title:hover) {
  background-color: rgba(0, 0, 0, 0.03);
}

/* 激活状态的子菜单标题 */
:deep(.el-sub-menu.is-active .el-sub-menu__title) {
  color: var(--apple-primary);
  /* 注意：这里默认不添加背景色，只改变文字颜色。如果需要背景色，可以在此添加 */
}

/* 对作为 menu-item 的 el-sub-menu 应用 max-height 过渡 */
:deep(.el-sub-menu.menu-item) {
  height: auto; /* 高度自适应 */
  max-height: 48px; /* 折叠时的最大高度（匹配标题高度） */
  transition: max-height 0.3s ease-out; /* 应用 max-height 过渡 */
  /* 圆角已由 .menu-item 提供 */
}

/* 展开状态的子菜单 */
:deep(.el-sub-menu.is-opened.menu-item) {
  max-height: 500px; /* 展开时的最大高度，确保足够容纳所有子项 */
}

/* 调整展开时子菜单容器的内边距 */
:deep(.el-sub-menu.is-opened > .el-menu) {
  background-color: transparent;
  padding: 0;
  padding-top: 8px; /* 在标题和第一个子项之间增加一点间距 */
  box-sizing: border-box;
}

/* --- 确认之前的清理依然生效 --- */
/*
:deep(.el-sub-menu.is-opened + .menu-item) { ... }
:deep(.el-sub-menu .el-menu-item:first-child) { ... }
:deep(.el-sub-menu .el-menu-item:last-child) { ... }
*/
/* --- 清理结束 --- */

/* 页脚样式 */
.sidebar-footer {
  padding: 16px;
  text-align: center;
  font-size: 12px;
  color: var(--apple-text-secondary);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.version {
  margin-bottom: 4px;
}

/* 折叠状态的菜单 */
:deep(.el-menu--collapse) {
  width: 64px;
  padding: 12px 0; /* 只保留上下内边距 */
}

/* 图标包装器样式 - 更精确的控制 */
.icon-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
  position: relative; /* 添加相对定位 */
}

/* 确保所有图标在收起状态下的位置一致 */
:deep(.el-menu--collapse) .icon-wrapper {
  position: absolute; /* 绝对定位 */
  left: 50%; /* 水平居中 */
  top: 50%; /* 垂直居中 */
  transform: translate(-50%, -50%); /* 精确居中 */
  margin: 0 !important;
}

/* 收起状态下的菜单项样式 */
:deep(.el-menu--collapse .el-menu-item),
:deep(.el-menu--collapse .el-sub-menu__title) {
  position: relative; /* 为绝对定位的子元素提供参考 */
  height: 48px;
  padding: 0 !important;
  width: 52px;
  margin-left: 12px;
  border-radius: 8px 0 0 8px;
}

/* 隐藏子菜单箭头 */
:deep(.el-menu--collapse .el-sub-menu__title .el-sub-menu__icon-arrow) {
  display: none !important;
}

/* 确保图标内部的SVG元素大小一致 */
.icon-wrapper :deep(svg) {
  width: 20px;
  height: 20px;
  display: block;
}

/* 确保收起状态下激活项的背景色正确显示 */
:deep(.el-menu--collapse .el-menu-item.is-active) {
  background-color: rgba(0, 113, 227, 0.1);
  color: var(--apple-primary);
}
</style>

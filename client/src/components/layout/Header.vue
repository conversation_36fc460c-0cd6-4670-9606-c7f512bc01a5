<template>
  <div class="header">
    <div class="left-section">
      <div
        class="toggle-button"
        @click="toggleSidebar"
      >
        <el-icon>
          <Fold v-if="isCollapsed" />
          <Expand v-else />
        </el-icon>
      </div>
      <div class="breadcrumb">
        <span>海聚云装备</span>
      </div>
    </div>
    <div class="right-menu">
      <el-dropdown
        trigger="click"
        @command="handleCommand"
      >
        <div class="user-info">
          <el-avatar
            :size="32"
            class="user-avatar"
            :src="userStore.userInfo?.avatar_url"
          >
            {{ userAvatarText }}
          </el-avatar>
          <span class="username">{{ userDisplayName }}</span>
          <el-icon class="dropdown-icon">
            <ArrowDown />
          </el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu class="user-dropdown">
            <el-dropdown-item command="profile">
              <el-icon><User /></el-icon>
              <span>个人信息</span>
            </el-dropdown-item>
            <el-dropdown-item command="settings">
              <el-icon><Setting /></el-icon>
              <span>系统设置</span>
            </el-dropdown-item>
            <el-dropdown-item
              divided
              command="logout"
            >
              <el-icon><SwitchButton /></el-icon>
              <span>退出登录</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup lang="ts">
/**
 * 头部组件
 * 包含侧边栏切换、用户信息、下拉菜单等功能
 */

import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/store/user'
import { ElMessage } from 'element-plus'
import { Fold, Expand, ArrowDown, User, Setting, SwitchButton } from '@element-plus/icons-vue'

// 下拉菜单命令类型
type DropdownCommand = 'profile' | 'settings' | 'logout'

// Props定义
interface Props {
  isCollapsed?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isCollapsed: false
})

// Emits定义
interface Emits {
  (e: 'toggle-sidebar'): void
}

const emit = defineEmits<Emits>()

// 组合式函数
const router = useRouter()
const userStore = useUserStore()

// 计算属性
const userDisplayName = computed(() => {
  return userStore.userInfo?.name || userStore.userInfo?.username || '用户'
})

const userAvatarText = computed(() => {
  return userDisplayName.value.charAt(0).toUpperCase()
})

/**
 * 切换侧边栏
 */
const toggleSidebar = (): void => {
  emit('toggle-sidebar')
}

/**
 * 处理下拉菜单命令
 * @param command 菜单命令
 */
const handleCommand = async (command: DropdownCommand): Promise<void> => {
  switch (command) {
    case 'logout':
      await handleLogout()
      break
    case 'profile':
      handleProfile()
      break
    case 'settings':
      handleSettings()
      break
    default:
      console.warn('未知的菜单命令:', command)
  }
}

/**
 * 处理退出登录
 */
const handleLogout = async (): Promise<void> => {
  try {
    await userStore.logoutAction()
    await router.push('/login')
    ElMessage.success('退出登录成功')
  } catch (error) {
    console.error('退出登录失败:', error)
    ElMessage.error('退出登录失败')
  }
}

/**
 * 处理个人信息
 */
const handleProfile = (): void => {
  ElMessage.info('个人信息功能开发中')
}

/**
 * 处理系统设置
 */
const handleSettings = (): void => {
  ElMessage.info('系统设置功能开发中')
}
</script>

<style scoped>
.header {
  height: 64px;
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;
  position: sticky;
  top: 0;
  z-index: 1000;
}

.left-section {
  display: flex;
  align-items: center;
}

.breadcrumb {
  margin-left: 16px;
  font-size: 16px;
  font-weight: 500;
  color: var(--apple-text-primary);
}

.toggle-button {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--apple-text-primary);
  border-radius: 6px;
  transition: var(--apple-transition);
}

.toggle-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--apple-primary);
}

.right-menu {
  display: flex;
  align-items: center;
}

.search-box {
  margin-right: 16px;
}

.search-input {
  width: 200px;
}

.search-input :deep(.el-input__inner) {
  border-radius: 8px;
  background-color: rgba(0, 0, 0, 0.03);
  border: none;
}

.icon-item {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: var(--apple-text-primary);
  margin-right: 16px;
  cursor: pointer;
  border-radius: 6px;
  transition: var(--apple-transition);
}

.icon-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--apple-primary);
}

.notification-badge :deep(.el-badge__content) {
  background-color: var(--apple-primary);
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 8px;
  transition: var(--apple-transition);
}

.user-info:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.user-avatar {
  background: linear-gradient(135deg, var(--apple-primary), #00a2ff);
  color: #fff;
  margin-right: 8px;
}

.username {
  font-size: 14px;
  color: var(--apple-text-primary);
  margin-right: 4px;
}

.dropdown-icon {
  font-size: 12px;
  color: var(--apple-text-secondary);
}

:deep(.user-dropdown) {
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  padding: 8px;
}

:deep(.user-dropdown .el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  border-radius: 6px;
}

:deep(.user-dropdown .el-dropdown-menu__item:hover) {
  background-color: rgba(0, 0, 0, 0.03);
}

:deep(.user-dropdown .el-dropdown-menu__item i) {
  margin-right: 8px;
  font-size: 16px;
}
</style>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`应用「${algorithmName}」算法`"
    width="65%"
    destroy-on-close
    class="apply-algorithm-dialog"
  >
    <div
      v-loading="loading"
      class="apply-dialog-content"
    >
      <!-- 穿梭框区域 -->
      <div class="device-config-content">
        <el-transfer
          v-model="appliedDeviceIds"
          v-loading="loading || transferLoading"
          :data="transferDeviceList"
          :titles="['未应用设备', '已应用设备']"
          :button-texts="['取消应用', '应用']"
          filterable
          :filter-method="filterDevice"
          @change="handleTransferChange"
        >
          <template #default="{ option }">
            <div class="transfer-device-item">
              <div class="device-name">
                {{ option.label }}
              </div>
              <div class="device-serial">
                序列号: {{ option.serialNumber }}
              </div>
              <div
                v-if="option.hasOtherAlgorithm"
                class="device-warning"
              >
                已应用其他算法
              </div>
            </div>
          </template>
        </el-transfer>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, defineProps, defineEmits } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getDeviceListWithAlgorithm, batchSetDeviceArithmetic, batchDeleteDeviceArithmetic } from '@/api'
import { getDeviceTagType } from '@/utils/utils'



// 穿梭框设备项类型定义
interface TransferDeviceItem {
  key: number
  label: string
  serialNumber: string
  hasOtherAlgorithm: boolean
  disabled?: boolean
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  algorithmId: {
    type: Number,
    required: true
  },
  algorithmName: {
    type: String,
    default: ''
  },
  algorithmType: {
    type: Number,
    required: true
  },
  productKey: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:visible', 'success'])

// 判断设备是否已应用其他算法
const hasOtherAlgorithmApplied = (device: any): boolean => {
  if (!device.algorithms || device.algorithms.length === 0) {
    return false
  }

  // 查找是否有其他类型的算法应用
  const otherAlgorithms = device.algorithms.filter((alg: any) => alg.type !== props.algorithmType)
  return otherAlgorithms.length > 0
}

// 数据转换函数：将设备列表转换为穿梭框数据结构
const buildTransferData = (deviceList: any[]) => {
  const transferList: TransferDeviceItem[] = deviceList.map(device => ({
    key: device.id,
    label: device.name || device.deviceName || '',
    serialNumber: device.serialNumber || '',
    hasOtherAlgorithm: hasOtherAlgorithmApplied(device),
    disabled: false
  }))

  const appliedIds = deviceList
    .filter(device => device.isApplied)
    .map(device => device.id)

  return { transferList, appliedIds }
}

// 设备列表数据（保留原有数据，用于API调用）
const deviceList = ref([])
const loading = ref(false)

const dialogVisible = ref(false)

// 穿梭框相关数据
const transferDeviceList = ref<TransferDeviceItem[]>([]) // 穿梭框数据源
const appliedDeviceIds = ref<number[]>([]) // 已应用算法的设备ID列表
const originalAppliedIds = ref<number[]>([]) // 原始已应用设备ID列表，用于错误恢复
const transferLoading = ref(false) // 穿梭操作加载状态





// 穿梭框过滤方法
const filterDevice = (query: string, item: TransferDeviceItem): boolean => {
  const keyword = query.toLowerCase()
  const label = item.label || ''
  const serialNumber = item.serialNumber || ''
  return label.toLowerCase().includes(keyword) ||
         serialNumber.toLowerCase().includes(keyword)
}

// 监听visible属性变化
watch(
  () => props.visible,
  (newVal: boolean) => {
    dialogVisible.value = newVal
    if (newVal) {
      // 对话框打开时重置穿梭框状态
      appliedDeviceIds.value = []
      originalAppliedIds.value = []

      fetchDevices()
    }
  }
)

// 监听dialogVisible变化
watch(dialogVisible, (newVal: boolean) => {
  if (!newVal) {
    // 对话框关闭时重置穿梭框状态
    appliedDeviceIds.value = []
    originalAppliedIds.value = []
    emit('update:visible', false)
  }
})

// 获取设备列表
const fetchDevices = async () => {
  try {
    loading.value = true
    // 构建查询参数
    const params: any = {
      page: 1,
      pageSize: 10000,
      algorithmType: props.algorithmType, // 传递算法类型参数
      arithmeticId: props.algorithmId, // 传递算法ID参数，用于后端过滤
      type: props.productKey // 传递设备类型参数，只加载匹配的设备类型
    }

    console.log('🔍 应用算法弹框 - 查询参数:', {
      algorithmId: props.algorithmId,
      algorithmName: props.algorithmName,
      algorithmType: props.algorithmType,
      productKey: props.productKey,
      params
    })



    // 使用新API一次性获取带算法信息的设备列表
    const response = await getDeviceListWithAlgorithm(params)
    if (response.success) {
      console.log('获取到设备列表:', response.data.list)
      deviceList.value = response.data.list || []

      // 转换数据结构为穿梭框格式
      const { transferList, appliedIds } = buildTransferData(deviceList.value)
      transferDeviceList.value = transferList
      appliedDeviceIds.value = [...appliedIds]
      originalAppliedIds.value = [...appliedIds]
    } else {
      ElMessage.error('获取设备列表失败')
    }
  } catch (error) {
    console.error('获取设备列表失败:', error)
    ElMessage.error('获取设备列表失败: ' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}







// 穿梭框变化处理方法
const handleTransferChange = async (newAppliedIds: number[], direction: 'left' | 'right', movedKeys: number[]) => {
  if (transferLoading.value) return // 防止重复操作

  transferLoading.value = true

  try {
    let response: any

    if (direction === 'right') {
      // 向右穿梭 - 应用算法
      response = await batchSetDeviceArithmetic({
        deviceIds: movedKeys,
        arithmeticId: props.algorithmId,
        arithmeticType: props.algorithmType
      })
    } else {
      // 向左穿梭 - 取消应用算法
      response = await batchDeleteDeviceArithmetic({
        deviceIds: movedKeys,
        arithmeticId: props.algorithmId,
        arithmeticType: props.algorithmType
      })
    }

    if (response.success) {
      const action = direction === 'right' ? '应用' : '取消应用'

      // 检查批量操作的详细结果
      if (response.data && typeof response.data.success === 'number' && typeof response.data.failed === 'number') {
        const { success: successCount, failed: failedCount, total, failedDevices } = response.data

        if (successCount > 0 && failedCount === 0) {
          // 全部成功
          ElMessage.success(`${action}成功，共处理 ${total} 个设备`)

          // 更新原始应用状态
          originalAppliedIds.value = [...newAppliedIds]

          // 触发成功回调，通知父组件刷新
          emit('success')
        } else if (successCount > 0 && failedCount > 0) {
          // 部分成功
          let message = `${action}部分成功：成功 ${successCount} 个，失败 ${failedCount} 个`

          // 添加失败原因到消息中
          if (failedDevices && failedDevices.length > 0) {
            const reasons = failedDevices.map(item => `设备${item.deviceId}: ${item.reason}`).slice(0, 3) // 最多显示3个
            message += `\n失败原因：${reasons.join('; ')}`
            if (failedDevices.length > 3) {
              message += ` 等${failedDevices.length}个设备`
            }
          }

          ElMessage.warning(message)

          // 部分成功也需要刷新数据
          emit('success')

          // 恢复到实际的应用状态（需要重新获取）
          await fetchDevices()
        } else if (failedCount > 0) {
          // 全部失败
          let message = `${action}失败，共 ${failedCount} 个设备失败`

          // 添加失败原因到消息中
          if (failedDevices && failedDevices.length > 0) {
            const reasons = failedDevices.map(item => `设备${item.deviceId}: ${item.reason}`).slice(0, 3) // 最多显示3个
            message += `\n失败原因：${reasons.join('; ')}`
            if (failedDevices.length > 3) {
              message += ` 等${failedDevices.length}个设备`
            }
          }

          ElMessage.error(message)

          // 操作失败，恢复原始状态
          appliedDeviceIds.value = [...originalAppliedIds.value]
        } else {
          // 异常情况
          ElMessage.success(`${action}成功`)
          originalAppliedIds.value = [...newAppliedIds]
          emit('success')
        }
      } else {
        // 兼容旧的响应格式
        ElMessage.success(`${action}成功`)
        originalAppliedIds.value = [...newAppliedIds]
        emit('success')
      }
    } else {
      ElMessage.error(response.message || `操作失败`)
      // 操作失败，恢复原始状态
      appliedDeviceIds.value = [...originalAppliedIds.value]
    }
  } catch (error: any) {
    console.error('穿梭操作错误:', error)
    ElMessage.error('操作失败: ' + (error.message || '未知错误'))
    // 操作失败，恢复原始状态
    appliedDeviceIds.value = [...originalAppliedIds.value]
  } finally {
    transferLoading.value = false
  }
}







// 组件挂载时加载数据
onMounted(() => {
  if (props.visible) {
    fetchDevices()
  } else {
    // 确保在visible变为true之前也预加载数据
    fetchDevices()
  }
})
</script>

<style scoped>
.apply-algorithm-dialog :deep(.el-dialog__header) {
  padding: 16px 20px;
  margin-right: 0;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  border-radius: 4px 4px 0 0;
}

.apply-algorithm-dialog :deep(.el-dialog__title) {
  font-weight: bold;
  font-size: 18px;
  color: #303133;
}

.apply-algorithm-dialog :deep(.el-dialog__body) {
  padding: 20px;
}

.apply-dialog-content {
  min-height: 450px;
}



/* 设备配置内容样式 */
.device-config-content {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 420px;
  padding: 20px;
}

/* 设备数量标签间距 */
.ml-2 {
  margin-left: 8px;
}

/* 穿梭框内设备项样式 */
.transfer-device-item {
  width: 100%;
  padding: 6px 0;
  box-sizing: border-box;
  display: block;
}

.transfer-device-item .device-name {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 2px;
  line-height: 1.2;
  word-break: break-word;
}

.transfer-device-item .device-serial {
  font-size: 12px;
  color: #909399;
  margin-bottom: 2px;
  line-height: 1.2;
}

.transfer-device-item .device-warning {
  font-size: 11px;
  color: #e6a23c;
  background-color: #fdf6ec;
  padding: 1px 4px;
  border-radius: 2px;
  display: inline-block;
}

/* 穿梭框样式调整 */
:deep(.el-transfer) {
  display: inline-block;
}

:deep(.el-transfer .el-transfer-panel) {
  width: 350px;
  height: 400px;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
}

:deep(.el-transfer .el-transfer-panel__header) {
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  padding: 10px 16px;
  font-weight: 600;
  color: #303133;
  height: 40px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

:deep(.el-transfer .el-transfer-panel__body) {
  height: calc(100% - 40px);
  padding: 0;
  display: flex;
  flex-direction: column;
}

:deep(.el-transfer .el-transfer-panel__filter) {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-transfer .el-transfer-panel__list) {
  flex: 1;
  overflow-y: auto;
  margin: 0;
  padding: 0;
}

:deep(.el-transfer .el-transfer-panel__item) {
  padding: 0;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
  height: auto;
}

:deep(.el-transfer .el-transfer-panel__item:last-child) {
  border-bottom: none;
}

:deep(.el-transfer .el-transfer-panel__item:hover) {
  background-color: #f5f7fa;
}

:deep(.el-transfer .el-transfer-panel__item .el-checkbox) {
  width: 100%;
  margin: 0;
  padding: 0;
  height: auto;
  display: block;
}

:deep(.el-transfer .el-transfer-panel__item .el-checkbox__input) {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  margin: 0;
}

:deep(.el-transfer .el-transfer-panel__item .el-checkbox__label) {
  width: 100%;
  padding: 8px 16px 8px 36px;
  margin: 0;
  display: block;
  line-height: 1.2;
  min-height: 45px;
  box-sizing: border-box;
}
</style>

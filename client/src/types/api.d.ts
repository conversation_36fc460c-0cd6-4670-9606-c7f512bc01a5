/**
 * 海聚科技钻井数据监控系统 - API类型定义
 * 包含所有API接口的请求和响应类型定义
 */

// ==================== 通用类型 ====================

// 通用响应格式
export interface ApiResponse<T = unknown> {
  code: number
  message: string
  data: T
  success?: boolean
  timestamp?: string
}

// 分页请求参数
export interface PaginationParams {
  page: number
  pageSize: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// 分页响应格式
export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 通用ID类型
export type ID = string | number

// 时间戳类型
export type Timestamp = string

// ==================== 用户相关类型 ====================

// 用户角色枚举
export type UserRole = 'admin' | 'user' | 'operator' | 'viewer'

// 用户信息
export interface User {
  id: ID
  username: string
  email?: string
  name?: string
  role: UserRole
  avatar_url?: string
  createdAt: Timestamp
  updatedAt: Timestamp
}

// 用户信息（简化版，用于Store）
export interface UserInfo {
  id: ID
  username: string
  name: string
  role: string
  avatar_url?: string
}

// ==================== 设备相关类型 ====================

// 设备状态枚举
export type DeviceStatus = 'online' | 'offline' | 'maintenance' | 'error'

// 设备类型枚举
export type DeviceType = 'drill' | 'sensor' | 'monitor' | 'controller'

// 设备信息
export interface Device {
  id: ID
  device_sn: string
  device_name: string
  product_key: string
  project_name?: string
  type?: DeviceType
  status?: DeviceStatus
  lastDataTime?: Timestamp
  lastFileTime?: Timestamp
  macAddress?: string
  createdAt?: Timestamp
  modifiedAt?: Timestamp
}

// 设备列表请求参数
export interface DeviceListParams extends PaginationParams {
  keyword?: string
  status?: DeviceStatus
  type?: DeviceType
}

// ==================== 算法相关类型 ====================

// 算法类型枚举
export type ArithmeticType = 'cleaning' | 'analysis' | 'prediction' | 'optimization'

// 算法状态枚举
export type ArithmeticStatus = 'active' | 'inactive' | 'testing' | 'deprecated'

// 算法信息
export interface Arithmetic {
  id: ID
  type: number
  typeName?: string
  name: string
  description?: string
  productKey: string
  ossPath?: string
  content?: string
  md5?: string
  version?: string
  isDefault: boolean
  status?: ArithmeticStatus
  createdAt: Timestamp
  modifiedAt?: Timestamp
}

// 算法列表请求参数
export interface ArithmeticListParams extends PaginationParams {
  keyword?: string
  type?: number
  productKey?: string
  status?: ArithmeticStatus
}

// 登录请求参数
export interface LoginRequest {
  username: string
  password: string
}

// 登录响应数据
export interface LoginResponse {
  message: string
  user: {
    id: string
    username: string
    name: string
    role: string
  }
  // token现在通过HttpOnly Cookie传递，不在响应中返回
}

export interface LogoutResponse {
  message: string
}

export interface UserInfo {
  id: string
  username: string
  name: string
  role: string
  avatar_url?: string
}

export interface ExampleData {
  id: number
  name: string
}

export interface ExampleResponse {
  message: string
  timestamp: string
  data: ExampleData[]
}

// 文件相关类型
export interface File {
  id: string
  device_sn: string
  file_name: string
  file_size: number
  created_at: string
}

// 文件列表响应
export interface FileListResponse {
  success: boolean
  data: {
    total: number
    list: File[]
  }
}

// 文件详情响应
export interface FileDetailResponse {
  success: boolean
  data: File
}

// 算法列表响应
export interface ArithmeticListResponse {
  success: boolean
  data: {
    total: number
    list: Arithmetic[]
  }
}

// 算法详情响应
export interface ArithmeticDetailResponse {
  success: boolean
  data: Arithmetic
}

// 统计数据响应
export interface StatsResponse {
  success: boolean
  data: {
    deviceCount: number
    dataRows: number
    cleanedCount: number
    totalSize: number
    formattedSize: string
  }
}

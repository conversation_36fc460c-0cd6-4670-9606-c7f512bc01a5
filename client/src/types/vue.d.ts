/**
 * Vue 3 + TypeScript 类型定义
 * 包含Vue组件、全局属性等类型定义
 */

/// <reference types="vite/client" />

// Vue组件类型声明
declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<
    Record<string, unknown>,
    Record<string, unknown>,
    unknown
  >
  export default component
}

// 扩展Window接口
declare global {
  interface Window {
    // Element Plus 全局方法
    $message?: typeof import('element-plus')['ElMessage']
    $loading?: typeof import('element-plus')['ElLoading']
    $confirm?: typeof import('element-plus')['ElMessageBox']['confirm']

    // 钉钉相关
    dd?: {
      ready: (callback: () => void) => void
      runtime: {
        permission: {
          requestAuthCode: (options: {
            corpId: string
            onSuccess: (result: { code: string }) => void
            onFail: (error: unknown) => void
          }) => void
        }
      }
    }

    // 开发环境调试
    __VUE_DEVTOOLS_GLOBAL_HOOK__?: unknown
  }
}

// 环境变量类型定义
interface ImportMetaEnv {
  readonly VITE_API_BASE_URL: string
  readonly VITE_APP_TITLE: string
  readonly VITE_DINGTALK_APP_ID: string
  readonly VITE_DINGTALK_CORP_ID: string
  readonly MODE: string
  readonly BASE_URL: string
  readonly PROD: boolean
  readonly DEV: boolean
  readonly SSR: boolean
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}

// Vue Router 元信息扩展（如果需要在这里定义）
declare module 'vue-router' {
  interface RouteMeta {
    title?: string
    requiresAuth?: boolean
    activeMenu?: string
    icon?: string
    hidden?: boolean
    roles?: string[]
  }
}

export {}

/**
 * 环境变量类型定义
 * 定义所有Vite环境变量的TypeScript类型
 */

/// <reference types="vite/client" />

// 环境变量接口定义
interface ImportMetaEnv {
  // ==================== 基础配置 ====================

  // 应用基础信息
  readonly VITE_APP_TITLE: string
  readonly VITE_APP_DESCRIPTION: string
  readonly VITE_APP_VERSION: string

  // API配置
  readonly VITE_API_URL: string
  readonly VITE_API_BASE_URL: string
  readonly VITE_API_TIMEOUT: string

  // ==================== 钉钉集成配置 ====================

  // 钉钉OAuth配置
  readonly VITE_DINGTALK_CLIENT_ID: string
  readonly VITE_DINGTALK_CORP_ID: string
  readonly VITE_DINGTALK_APP_ID: string
  readonly VITE_DINGTALK_REDIRECT_URI: string
  readonly VITE_DINGTALK_SCOPE: string

  // ==================== 开发配置 ====================

  // 开发服务器配置
  readonly VITE_DEV_SERVER_HOST: string
  readonly VITE_DEV_SERVER_PORT: string
  readonly VITE_DEV_PROXY_TARGET: string

  // 调试配置
  readonly VITE_DEBUG_MODE: string
  readonly VITE_MOCK_API: string
  readonly VITE_ENABLE_DEVTOOLS: string

  // ==================== 生产配置 ====================

  // 生产环境配置
  readonly VITE_BUILD_SOURCEMAP: string
  readonly VITE_BUILD_ANALYZE: string
  readonly VITE_CDN_URL: string

  // 监控配置
  readonly VITE_SENTRY_DSN: string
  readonly VITE_ANALYTICS_ID: string

  // ==================== 功能开关 ====================

  // 功能开关
  readonly VITE_ENABLE_PWA: string
  readonly VITE_ENABLE_I18N: string
  readonly VITE_ENABLE_THEME: string
  readonly VITE_ENABLE_WEBSOCKET: string

  // ==================== Vite内置变量 ====================

  // Vite内置环境变量
  readonly MODE: 'development' | 'production' | 'test'
  readonly BASE_URL: string
  readonly PROD: boolean
  readonly DEV: boolean
  readonly SSR: boolean
}

// ImportMeta接口扩展
interface ImportMeta {
  readonly env: ImportMetaEnv
  readonly hot?: {
    readonly data: Record<string, unknown>
    accept(): void
    accept(cb: (mod: unknown) => void): void
    accept(dep: string, cb: (mod: unknown) => void): void
    accept(deps: readonly string[], cb: (mods: unknown[]) => void): void
    dispose(cb: (data: Record<string, unknown>) => void): void
    decline(): void
    invalidate(): void
    on(event: string, cb: (...args: unknown[]) => void): void
  }
}

// 全局常量类型定义
declare const __APP_ENV__: string
declare const __APP_VERSION__: string
declare const __BUILD_TIME__: string

// Node.js环境变量类型（用于构建时）
declare namespace NodeJS {
  interface ProcessEnv {
    NODE_ENV: 'development' | 'production' | 'test'
    VITE_APP_TITLE: string
    VITE_API_URL: string
    [key: string]: string | undefined
  }
}

export {}

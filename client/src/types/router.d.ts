/**
 * Vue Router 类型定义扩展
 * 扩展路由元信息和相关类型
 */

import 'vue-router'

// 路由名称枚举
export type RouteNames =
  | 'Login'
  | 'Device'
  | 'DeviceDetail'
  | 'DeviceChartConfig'
  | 'DeviceRealtime'
  | 'DigitalCore'
  | 'DigitalCoreDetail'
  | 'AlgorithmCleaning'
  | 'AlgorithmAnalysis'
  | 'AlgorithmEdit'
  | 'Overview'
  | 'Import'
  | 'ImportDetail'
  | 'Template'
  | 'TemplateCreate'
  | 'TemplateEdit'

// 用户角色类型
export type UserRole = 'admin' | 'user' | 'operator' | 'viewer'

// 扩展Vue Router的RouteMeta接口
declare module 'vue-router' {
  interface RouteMeta {
    // 页面标题
    title?: string

    // 是否需要认证（默认true）
    requiresAuth?: boolean

    // 激活的菜单项（用于高亮侧边栏）
    activeMenu?: string

    // 图标名称
    icon?: string

    // 是否隐藏在菜单中
    hidden?: boolean

    // 允许访问的角色
    roles?: UserRole[]

    // 面包屑导航
    breadcrumb?: boolean

    // 是否缓存页面
    keepAlive?: boolean

    // 页面权限码
    permission?: string

    // 是否为外部链接
    isExternal?: boolean

    // 排序权重
    sort?: number
  }
}

// 路由配置类型
export interface RouteConfig {
  path: string
  name?: RouteNames
  component?: () => Promise<any>
  redirect?: string
  meta?: RouteMeta
  children?: RouteConfig[]
}

// 菜单项类型
export interface MenuItem {
  id: string
  title: string
  path: string
  icon?: string
  children?: MenuItem[]
  meta?: RouteMeta
}

// 导航守卫类型
export type NavigationGuardNext = (to?: string | false | void) => void

export {}

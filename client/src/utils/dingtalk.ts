/**
 * 钉钉OAuth集成工具
 * 提供钉钉登录相关的配置和工具函数
 */

// 钉钉OAuth配置类型
export interface DingTalkConfig {
  clientId: string
  redirectUri: string
  scope: string
  responseType: 'code'
  prompt: 'consent'
  corpId?: string
  appId?: string
}

// 钉钉OAuth响应类型
export interface DingTalkAuthResponse {
  code: string
  state?: string
}

// 环境信息类型
export interface EnvironmentInfo {
  mode: string
  dev: boolean
  prod: boolean
  redirectUri: string
  clientId: string
  corpId?: string
  appId?: string
}

/**
 * 获取钉钉OAuth配置
 * @returns 钉钉OAuth配置对象
 */
export const getDingTalkConfig = (): DingTalkConfig => {
  const config: DingTalkConfig = {
    clientId: import.meta.env.VITE_DINGTALK_CLIENT_ID || '',
    redirectUri: import.meta.env.VITE_DINGTALK_REDIRECT_URI || '',
    scope: 'openid corpid',
    responseType: 'code',
    prompt: 'consent',
    corpId: import.meta.env.VITE_DINGTALK_CORP_ID,
    appId: import.meta.env.VITE_DINGTALK_APP_ID
  }

  // 验证必要的配置项
  if (!config.clientId) {
    throw new Error('钉钉Client ID未配置，请检查环境变量 VITE_DINGTALK_CLIENT_ID')
  }

  if (!config.redirectUri) {
    throw new Error('钉钉重定向URI未配置，请检查环境变量 VITE_DINGTALK_REDIRECT_URI')
  }

  return config
}

/**
 * 生成钉钉OAuth授权URL
 * @param state 可选的状态参数，用于防止CSRF攻击
 * @returns 完整的钉钉OAuth授权URL
 */
export const generateDingTalkAuthUrl = (state?: string): string => {
  try {
    const config = getDingTalkConfig()
    const params = new URLSearchParams({
      redirect_uri: config.redirectUri,
      response_type: config.responseType,
      client_id: config.clientId,
      scope: config.scope,
      prompt: config.prompt
    })

    // 添加可选的state参数
    if (state) {
      params.append('state', state)
    }

    return `https://login.dingtalk.com/oauth2/auth?${params.toString()}`
  } catch (error) {
    console.error('生成钉钉授权URL失败:', error)
    throw error
  }
}

/**
 * 从URL中解析钉钉授权码
 * @param url 包含授权码的URL
 * @returns 授权码和状态
 */
export const parseDingTalkAuthCode = (url: string): DingTalkAuthResponse => {
  const urlObj = new URL(url)
  const code = urlObj.searchParams.get('code')
  const state = urlObj.searchParams.get('state') || undefined

  if (!code) {
    throw new Error('URL中未找到钉钉授权码')
  }

  return { code, state }
}

/**
 * 获取当前环境信息（用于调试）
 * @returns 环境信息对象
 */
export const getEnvironmentInfo = (): EnvironmentInfo => {
  return {
    mode: import.meta.env.MODE,
    dev: import.meta.env.DEV,
    prod: import.meta.env.PROD,
    redirectUri: import.meta.env.VITE_DINGTALK_REDIRECT_URI || '',
    clientId: import.meta.env.VITE_DINGTALK_CLIENT_ID || '',
    corpId: import.meta.env.VITE_DINGTALK_CORP_ID,
    appId: import.meta.env.VITE_DINGTALK_APP_ID
  }
}

/**
 * 检查钉钉配置是否完整
 * @returns 配置是否完整
 */
export const isDingTalkConfigured = (): boolean => {
  try {
    const config = getDingTalkConfig()
    return !!(config.clientId && config.redirectUri)
  } catch (error) {
    return false
  }
}

/**
 * 通用工具函数集
 * 包含各种格式化、转换和辅助函数
 */

// 设备类型枚举
export type DeviceType = 'AD' | 'WPD' | 'PDF' | string

// Element Plus标签类型
export type TagType = 'primary' | 'success' | 'warning' | 'danger' | 'info'

/**
 * 获取设备类型对应的标签样式
 * @param type 设备类型代码
 * @returns Element Plus标签类型
 */
export const getDeviceTagType = (type: DeviceType): TagType => {
  switch (type) {
    case 'AD':
      return 'primary' // 蓝色
    case 'WPD':
      return 'warning' // 黄色
    case 'PDF':
      return 'success' // 绿色
    default:
      return 'info' // 灰色
  }
}

/**
 * 格式化日期时间
 * @param date 日期对象或日期字符串或时间戳
 * @param defaultValue 当日期无效时返回的默认值
 * @returns 格式化后的日期时间字符串
 */
export const formatDateTime = (
  date: Date | string | number | undefined | null,
  defaultValue = '未知'
): string => {
  if (!date) return defaultValue

  try {
    const dateObj = date instanceof Date ? date : new Date(date)
    // 检查日期是否有效
    if (isNaN(dateObj.getTime())) return defaultValue
    return dateObj.toLocaleString()
  } catch (error) {
    return defaultValue
  }
}

/**
 * 格式化文件大小
 * @param sizeInBytes 文件大小（字节）
 * @param defaultValue 当大小无效时返回的默认值
 * @returns 格式化后的文件大小字符串
 */
export const formatFileSize = (
  sizeInBytes: number | undefined | null,
  defaultValue = '未知'
): string => {
  if (sizeInBytes === undefined || sizeInBytes === null) return defaultValue

  if (sizeInBytes < 1024) {
    return sizeInBytes + ' B'
  } else if (sizeInBytes < 1024 * 1024) {
    return (sizeInBytes / 1024).toFixed(2) + ' KB'
  } else if (sizeInBytes < 1024 * 1024 * 1024) {
    return (sizeInBytes / (1024 * 1024)).toFixed(2) + ' MB'
  } else {
    return (sizeInBytes / (1024 * 1024 * 1024)).toFixed(2) + ' GB'
  }
}

// 工作模式枚举
export enum WorkMode {
  Standby = 0,        // 待机状态
  W80Hammer = 1,      // W80水锤钻模式
  W80Casing = 2,      // W80跟管钻进模式
  PDCRotary = 3,      // PDC纯旋转钻孔模式&钻注一体
  SealGrouting = 4,   // 封孔注浆模式
  PointCoring = 5,    // 定点取芯模式
  RopeCoring = 6      // 绳索取芯钻进模式
}

/**
 * 格式化工作模式
 * @param mode 钻机工作模式: 0：待机状态、1：W80水锤钻模式、2：W80跟管钻进模式、3：PDC纯旋转钻孔模式&钻注一体、4：封孔注浆模式、5：定点取芯模式、6：绳索取芯钻进模式
 * @returns 格式化后的工作模式字符串
 */
export const formatWorkMode = (mode: number): string => {
  switch (mode) {
    case WorkMode.Standby:
      return '待机状态'
    case WorkMode.W80Hammer:
      return 'W80水锤钻模式'
    case WorkMode.W80Casing:
      return 'W80跟管钻进模式'
    case WorkMode.PDCRotary:
      return 'PDC纯旋转钻孔模式&钻注一体'
    case WorkMode.SealGrouting:
      return '封孔注浆模式'
    case WorkMode.PointCoring:
      return '定点取芯模式'
    case WorkMode.RopeCoring:
      return '绳索取芯钻进模式'
    default:
      return '未知模式'
  }
}

// 采集状态枚举
export enum CollectionStatus {
  Ended = 0,      // 采集结束
  Collecting = 1, // 采集中
  Paused = 2,     // 采集暂停
  Standby = 3     // 采集待命
}

/**
 * 格式化采集状态
 * @param status 0——采集结束 1——采集中 2——采集暂停 3——采集待命
 * @returns 格式化后的采集状态字符串
 */
export const formatCollectionStatus = (status: number): string => {
  switch (status) {
    case CollectionStatus.Ended:
      return '采集结束'
    case CollectionStatus.Collecting:
      return '采集中'
    case CollectionStatus.Paused:
      return '采集暂停'
    case CollectionStatus.Standby:
      return '采集待命'
    default:
      return '未知状态'
  }
}

// 常用的工具函数
/**
 * 延迟执行函数
 * @param ms 延迟时间（毫秒）
 * @returns Promise
 */
export const sleep = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * 生成唯一ID
 * @returns 唯一ID字符串
 */
export const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

/**
 * 深拷贝对象
 * @param obj 要拷贝的对象
 * @returns 拷贝后的对象
 */
export const deepClone = <T>(obj: T): T => {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime()) as unknown as T
  if (obj instanceof Array) return obj.map(item => deepClone(item)) as unknown as T
  if (typeof obj === 'object') {
    const clonedObj = {} as T
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
  return obj
}

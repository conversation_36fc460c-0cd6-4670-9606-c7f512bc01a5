/**
 * 通用列表状态持久化工具类
 * 海聚科技钻井数据监控系统 - 通用状态管理基础设施
 * 
 * 基于deviceList.ts的成功模式抽象出的通用工具类，
 * 为所有列表页面提供统一的状态持久化功能。
 */

// ==================== 类型定义 ====================

/**
 * 基础筛选表单接口
 * 所有具体的筛选表单都应该继承此接口
 */
export interface BaseFilterForm {
  [key: string]: any
}

/**
 * 基础列表状态接口
 * 包含所有列表页面的通用状态结构
 */
export interface BaseListState<T extends BaseFilterForm> {
  /** 筛选表单状态 */
  filterForm: T
  /** 当前页码 */
  currentPage: number
  /** 每页显示数量 */
  pageSize: number
  /** 总记录数 */
  total: number
}

/**
 * 持久化状态数据结构
 * 用于序列化到sessionStorage的通用数据格式
 */
export interface PersistedListState<T extends BaseFilterForm> {
  /** 筛选表单数据 */
  filterForm: T
  /** 当前页码 */
  currentPage: number
  /** 每页显示数量 */
  pageSize: number
  /** 状态保存时间戳 */
  timestamp: number
}

/**
 * 状态验证器函数类型
 * 用于验证特定类型的筛选表单数据
 */
export type StateValidator<T extends BaseFilterForm> = (filterForm: any) => filterForm is T

/**
 * 存储键生成函数类型
 */
export type StorageKeyGenerator = (userId: string) => string

/**
 * 用户ID获取函数类型
 */
export type UserIdGetter = () => string | null

/**
 * 状态持久化配置接口
 */
export interface StatePersistenceConfig<T extends BaseFilterForm> {
  /** 存储键前缀 */
  storageKeyPrefix: string
  /** 默认筛选表单值 */
  defaultFilterForm: T
  /** 状态验证器函数 */
  validateFilterForm: StateValidator<T>
  /** 状态过期时间（毫秒），默认1小时 */
  expiryTime?: number
}

// ==================== 常量定义 ====================

/** 默认状态过期时间（1小时） */
export const DEFAULT_STATE_EXPIRY_TIME: number = 60 * 60 * 1000

// ==================== 核心工具函数 ====================

/**
 * 检测sessionStorage是否可用
 * @returns 是否可用
 */
export const isSessionStorageAvailable = (): boolean => {
  try {
    const testKey = '__test_session_storage__'
    sessionStorage.setItem(testKey, 'test')
    sessionStorage.removeItem(testKey)
    return true
  } catch (error) {
    console.warn('SessionStorage不可用:', error)
    return false
  }
}

/**
 * 获取当前用户ID
 * 从localStorage中的userInfo获取用户ID
 * @returns 用户ID或null
 */
export const getCurrentUserId: UserIdGetter = (): string | null => {
  try {
    // 检查localStorage可用性
    if (typeof localStorage === 'undefined') {
      console.warn('LocalStorage不可用，无法获取用户ID')
      return null
    }

    const userInfo = localStorage.getItem('userInfo')
    if (!userInfo) {
      console.log('未找到用户信息')
      return null
    }

    let user: any
    try {
      user = JSON.parse(userInfo)
    } catch (parseError) {
      console.error('解析用户信息失败:', parseError)
      return null
    }

    if (!user || typeof user !== 'object') {
      console.error('用户信息格式错误:', user)
      return null
    }

    const userId = user.id
    if (!userId || (typeof userId !== 'string' && typeof userId !== 'number')) {
      console.error('用户ID格式错误:', userId)
      return null
    }

    return String(userId)
  } catch (error) {
    console.error('获取用户ID失败:', error)
    return null
  }
}

/**
 * 创建存储键
 * @param storageKeyPrefix 存储键前缀
 * @param userId 用户ID
 * @returns 完整的存储键
 */
export const createStorageKey = (storageKeyPrefix: string, userId: string): string => {
  return `${storageKeyPrefix}_${userId}`
}

/**
 * 验证通用状态数据格式
 * @param state 待验证的状态数据
 * @param validateFilterForm 筛选表单验证器
 * @returns 是否格式正确
 */
export const validateStateData = <T extends BaseFilterForm>(
  state: any,
  validateFilterForm: StateValidator<T>
): state is PersistedListState<T> => {
  if (!state || typeof state !== 'object') {
    return false
  }

  // 检查必需字段
  if (!state.filterForm || typeof state.filterForm !== 'object') {
    return false
  }

  if (typeof state.currentPage !== 'number' || state.currentPage < 1) {
    return false
  }

  if (typeof state.pageSize !== 'number' || state.pageSize < 1) {
    return false
  }

  if (typeof state.timestamp !== 'number' || state.timestamp <= 0) {
    return false
  }

  // 使用自定义验证器验证filterForm
  if (!validateFilterForm(state.filterForm)) {
    return false
  }

  return true
}

// ==================== 状态持久化类 ====================

/**
 * 通用状态持久化管理器
 * 提供统一的状态保存、恢复、清除和重置功能
 */
export class ListStatePersistence<T extends BaseFilterForm> {
  private config: Required<StatePersistenceConfig<T>>
  private storageKeyGenerator: StorageKeyGenerator

  constructor(config: StatePersistenceConfig<T>) {
    this.config = {
      ...config,
      expiryTime: config.expiryTime || DEFAULT_STATE_EXPIRY_TIME
    }
    
    this.storageKeyGenerator = (userId: string) => 
      createStorageKey(this.config.storageKeyPrefix, userId)
  }

  /**
   * 保存状态到sessionStorage
   * @param state 要保存的状态数据
   */
  saveState(state: Omit<BaseListState<T>, 'total'>): void {
    try {
      // 检查sessionStorage可用性
      if (!isSessionStorageAvailable()) {
        console.warn('SessionStorage不可用，无法保存状态')
        return
      }

      const userId = getCurrentUserId()
      if (!userId) {
        console.warn('无法保存状态：用户未登录')
        return
      }

      const storageKey = this.storageKeyGenerator(userId)
      const persistedState: PersistedListState<T> = {
        filterForm: { ...state.filterForm },
        currentPage: state.currentPage,
        pageSize: state.pageSize,
        timestamp: Date.now()
      }

      // 验证状态数据格式
      if (!validateStateData(persistedState, this.config.validateFilterForm)) {
        console.error('状态数据格式错误，无法保存:', persistedState)
        return
      }

      const stateJson = JSON.stringify(persistedState)
      sessionStorage.setItem(storageKey, stateJson)

      // 验证保存结果
      const savedData = sessionStorage.getItem(storageKey)
      if (savedData !== stateJson) {
        console.error('状态保存验证失败，数据可能未正确保存')
        return
      }

      console.log(`${this.config.storageKeyPrefix}状态已保存`, { 
        userId, 
        timestamp: persistedState.timestamp 
      })
    } catch (error) {
      console.error(`保存${this.config.storageKeyPrefix}状态失败:`, error)

      // 尝试清理可能损坏的数据
      this.cleanupCorruptedData()
    }
  }

  /**
   * 从sessionStorage恢复状态
   * @returns 恢复的状态数据，如果失败则返回null
   */
  restoreState(): PersistedListState<T> | null {
    try {
      // 检查sessionStorage可用性
      if (!isSessionStorageAvailable()) {
        console.warn('SessionStorage不可用，无法恢复状态')
        return null
      }

      const userId = getCurrentUserId()
      if (!userId) {
        console.warn('无法恢复状态：用户未登录')
        return null
      }

      const storageKey = this.storageKeyGenerator(userId)
      const savedState = sessionStorage.getItem(storageKey)

      if (!savedState) {
        console.log(`没有找到保存的${this.config.storageKeyPrefix}状态`)
        return null
      }

      let state: PersistedListState<T>
      try {
        state = JSON.parse(savedState)
      } catch (parseError) {
        console.error('解析保存的状态数据失败:', parseError)
        sessionStorage.removeItem(storageKey)
        return null
      }

      // 验证状态数据格式
      if (!validateStateData(state, this.config.validateFilterForm)) {
        console.error(`${this.config.storageKeyPrefix}状态数据格式错误，清除无效数据`)
        sessionStorage.removeItem(storageKey)
        return null
      }

      // 检查状态时效性
      if (Date.now() - state.timestamp > this.config.expiryTime) {
        console.log(`${this.config.storageKeyPrefix}状态已过期，清除过期状态`)
        sessionStorage.removeItem(storageKey)
        return null
      }

      console.log(`${this.config.storageKeyPrefix}状态已恢复`, {
        userId,
        timestamp: state.timestamp,
        filterForm: state.filterForm,
        currentPage: state.currentPage,
        pageSize: state.pageSize
      })

      return state
    } catch (error) {
      console.error(`恢复${this.config.storageKeyPrefix}状态失败:`, error)
      this.cleanupCorruptedData()
      return null
    }
  }

  /**
   * 清除保存的状态
   */
  clearState(): void {
    try {
      // 检查sessionStorage可用性
      if (!isSessionStorageAvailable()) {
        console.warn('SessionStorage不可用，无法清除状态')
        return
      }

      const userId = getCurrentUserId()
      if (!userId) {
        console.warn('无法清除状态：用户未登录')
        return
      }

      const storageKey = this.storageKeyGenerator(userId)

      // 检查状态是否存在
      const existingState = sessionStorage.getItem(storageKey)
      if (!existingState) {
        console.log(`没有找到需要清除的${this.config.storageKeyPrefix}状态`)
        return
      }

      sessionStorage.removeItem(storageKey)

      // 验证清除结果
      const remainingState = sessionStorage.getItem(storageKey)
      if (remainingState) {
        console.error(`清除${this.config.storageKeyPrefix}状态失败，状态仍然存在:`, remainingState)
        // 强制清除
        try {
          sessionStorage.removeItem(storageKey)
        } catch (forceError) {
          console.error('强制清除状态失败:', forceError)
        }
      } else {
        console.log(`${this.config.storageKeyPrefix}状态已清除`, { userId })
      }
    } catch (error) {
      console.error(`清除${this.config.storageKeyPrefix}状态失败:`, error)
    }
  }

  /**
   * 获取默认状态
   * @returns 默认状态数据
   */
  getDefaultState(): BaseListState<T> {
    return {
      filterForm: { ...this.config.defaultFilterForm },
      currentPage: 1,
      pageSize: 10,
      total: 0
    }
  }

  /**
   * 清理损坏的数据
   * @private
   */
  private cleanupCorruptedData(): void {
    try {
      const userId = getCurrentUserId()
      if (userId) {
        const storageKey = this.storageKeyGenerator(userId)
        sessionStorage.removeItem(storageKey)
        console.log('已清理可能损坏的状态数据')
      }
    } catch (cleanupError) {
      console.error('清理损坏数据失败:', cleanupError)
    }
  }
}

// ==================== 工厂函数 ====================

/**
 * 创建状态持久化管理器的工厂函数
 * 简化Store中状态持久化管理器的创建
 *
 * @param config 状态持久化配置
 * @returns 状态持久化管理器实例
 */
export function createStatePersistence<T extends BaseFilterForm>(
  config: StatePersistenceConfig<T>
): ListStatePersistence<T> {
  return new ListStatePersistence(config)
}

/**
 * 创建防抖状态保存函数
 * 避免频繁的状态保存操作
 *
 * @param persistence 状态持久化管理器
 * @param state 状态数据的getter函数
 * @param delay 防抖延迟时间（毫秒），默认300ms
 * @returns 防抖后的保存函数
 */
export function createDebouncedSaveState<T extends BaseFilterForm>(
  persistence: ListStatePersistence<T>,
  state: () => Omit<BaseListState<T>, 'total'>,
  delay: number = 300
): () => void {
  let timeoutId: NodeJS.Timeout | null = null

  return () => {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }

    timeoutId = setTimeout(() => {
      persistence.saveState(state())
      timeoutId = null
    }, delay)
  }
}

// ==================== 常用验证器 ====================

/**
 * 创建字符串字段验证器
 * 用于验证包含字符串字段的筛选表单
 *
 * @param requiredFields 必需的字符串字段名数组
 * @param optionalFields 可选的字符串字段名数组
 * @returns 验证器函数
 */
export function createStringFieldsValidator<T extends BaseFilterForm>(
  requiredFields: (keyof T)[],
  optionalFields: (keyof T)[] = []
): StateValidator<T> {
  return (filterForm: any): filterForm is T => {
    if (!filterForm || typeof filterForm !== 'object') {
      return false
    }

    // 检查必需字段
    for (const field of requiredFields) {
      if (typeof filterForm[field] !== 'string') {
        return false
      }
    }

    // 检查可选字段（如果存在的话）
    for (const field of optionalFields) {
      if (filterForm[field] !== undefined && typeof filterForm[field] !== 'string') {
        return false
      }
    }

    return true
  }
}

/**
 * 创建数组字段验证器
 * 用于验证包含数组字段的筛选表单
 *
 * @param arrayFields 数组字段配置
 * @param stringFields 字符串字段名数组
 * @returns 验证器函数
 */
export function createArrayFieldsValidator<T extends BaseFilterForm>(
  arrayFields: { [K in keyof T]?: 'string[]' | 'number[]' },
  stringFields: (keyof T)[] = []
): StateValidator<T> {
  return (filterForm: any): filterForm is T => {
    if (!filterForm || typeof filterForm !== 'object') {
      return false
    }

    // 检查字符串字段
    for (const field of stringFields) {
      if (typeof filterForm[field] !== 'string') {
        return false
      }
    }

    // 检查数组字段
    for (const [field, type] of Object.entries(arrayFields)) {
      const value = filterForm[field]
      if (!Array.isArray(value)) {
        return false
      }

      if (type === 'string[]') {
        if (!value.every(item => typeof item === 'string')) {
          return false
        }
      } else if (type === 'number[]') {
        if (!value.every(item => typeof item === 'number')) {
          return false
        }
      }
    }

    return true
  }
}

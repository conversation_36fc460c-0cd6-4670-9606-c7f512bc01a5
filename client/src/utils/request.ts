/**
 * HTTP请求工具封装
 * 基于axios，提供统一的请求和响应处理
 */

import axios, {
  type AxiosError,
  type InternalAxiosRequestConfig,
  type AxiosResponse,
  type AxiosInstance
} from 'axios'
import { useUserStore } from '@/store/user'

// Token刷新状态管理
let isRefreshing = false
let failedQueue: Array<{
  resolve: (value?: unknown) => void
  reject: (reason?: unknown) => void
}> = []

// 处理队列中的请求
const processQueue = (error: unknown, token: string | null = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error)
    } else {
      resolve(token)
    }
  })

  failedQueue = []
}

// Token刷新函数
const refreshToken = async (): Promise<boolean> => {
  try {
    const response = await axios.post('/api/users/refresh-token', {}, {
      withCredentials: true,
      timeout: 10000
    })

    if (response.status === 200) {
      console.log('Token刷新成功')
      return true
    }

    return false
  } catch (error) {
    console.error('Token刷新失败:', error)
    return false
  }
}

// 检查Token状态
const checkTokenStatus = async (): Promise<{ needsRefresh: boolean; valid: boolean }> => {
  try {
    const response = await axios.get('/api/users/token-status', {
      withCredentials: true,
      timeout: 5000
    })

    return {
      needsRefresh: response.data?.needsRefresh || false,
      valid: response.data?.valid || false
    }
  } catch (error) {
    console.error('Token状态检查失败:', error)
    return { needsRefresh: false, valid: false }
  }
}

// 请求错误类型定义
export interface RequestError {
  status: number
  message: string
  data?: unknown
  code?: number
}

// 响应数据类型定义
export interface ApiResponse<T = unknown> {
  code: number
  message: string
  data: T
  success?: boolean
  timestamp?: string
}

// 创建axios实例
const service: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 15000,
  withCredentials: true, // 允许携带Cookie
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
service.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // // 添加请求时间戳（用于调试）
    // if (import.meta.env.DEV) {
    //   console.log(`[Request] ${config.method?.toUpperCase()} ${config.url}`, {
    //     params: config.params,
    //     data: config.data,
    //     timestamp: new Date().toISOString()
    //   })
    // }

    // 不再需要手动设置Authorization头，token通过HttpOnly Cookie自动发送
    return config
  },
  (error: AxiosError) => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(createRequestError(error))
  }
)

// 创建统一的错误对象
const createRequestError = (error: AxiosError): RequestError => {
  if (error.response) {
    // 服务器返回了错误状态码
    const { status, data } = error.response
    const errorData = data as { code?: number; message?: string }

    return {
      status,
      message: errorData.message || `请求失败 (${status})`,
      data: errorData,
      code: errorData.code
    }
  } else if (error.request) {
    // 请求已发出，但没有收到响应
    return {
      status: 0,
      message: '网络错误，请检查您的网络连接'
    }
  } else {
    // 请求配置出错
    return {
      status: 0,
      message: error.message || '请求配置错误'
    }
  }
}

// 处理认证失败
const handleAuthError = (error: RequestError): void => {
  if (error.code === -10086) {
    // 使用 user store 的清除本地数据方法
    const userStore = useUserStore()
    userStore.clearLocalData()

    // 跳转到登录页
    window.location.href = '/login'
  }
}

// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    // 开发环境下打印响应信息
    if (import.meta.env.DEV) {
      console.log(`[Response] ${response.config.method?.toUpperCase()} ${response.config.url}`, {
        status: response.status,
        data: response.data,
        timestamp: new Date().toISOString()
      })
    }

    // 检查是否需要刷新Token（后端提示）
    const refreshNeeded = response.headers['x-token-refresh-needed']
    if (refreshNeeded === 'true') {
      console.log('后端提示需要刷新Token，开始后台刷新')
      // 后台静默刷新，不阻塞当前请求
      refreshToken().catch(error => {
        console.error('后台Token刷新失败:', error)
      })
    }

    return response.data as any
  },
  async (error: AxiosError): Promise<any> => {
    const originalRequest = error.config as InternalAxiosRequestConfig & { _retry?: boolean }
    const requestError = createRequestError(error)

    // 处理401错误（Token过期）
    if (error.response?.status === 401 && !originalRequest._retry) {
      // 避免刷新Token的请求进入重试循环
      if (originalRequest.url?.includes('/refresh-token')) {
        handleAuthError(requestError)
        return Promise.reject(requestError)
      }

      // 排除登录相关接口的401错误，这些接口的401是业务错误（如用户名密码错误），不应该触发token刷新
      const authRelatedUrls = ['/users/login', '/users/dingtalk-login', '/users/register']
      const isAuthRelatedRequest = authRelatedUrls.some(url => originalRequest.url?.includes(url))

      if (isAuthRelatedRequest) {
        // 登录相关接口的401错误直接返回，不触发token刷新
        return Promise.reject(requestError)
      }

      // 如果正在刷新Token，将请求加入队列
      if (isRefreshing) {
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject })
        }).then(() => {
          // Token刷新成功，重新发送原请求
          return service(originalRequest)
        }).catch(err => {
          return Promise.reject(err)
        })
      }

      // 标记正在刷新Token
      originalRequest._retry = true
      isRefreshing = true

      try {
        const refreshSuccess = await refreshToken()

        if (refreshSuccess) {
          // Token刷新成功，处理队列中的请求
          processQueue(null, 'success')
          isRefreshing = false

          // 重新发送原请求
          return service(originalRequest)
        } else {
          // Token刷新失败，清理队列并跳转登录
          processQueue(new Error('Token刷新失败'), null)
          isRefreshing = false
          handleAuthError(requestError)
          return Promise.reject(requestError)
        }
      } catch (refreshError) {
        // Token刷新异常，清理队列并跳转登录
        processQueue(refreshError, null)
        isRefreshing = false
        handleAuthError(requestError)
        return Promise.reject(requestError)
      }
    }

    // 其他错误直接处理
    handleAuthError(requestError)
    return Promise.reject(requestError)
  }
)

// 请求配置接口
export interface RequestConfig<T = unknown> extends InternalAxiosRequestConfig {
  data?: T
}

// 主要的请求函数
const request = <T = unknown, R = unknown>(config: RequestConfig<T>): Promise<R> => {
  return service(config)
}

// 便捷方法
export const get = <T = unknown>(url: string, params?: unknown): Promise<T> => {
  return request<unknown, T>({ method: 'GET', url, params } as RequestConfig<unknown>)
}

export const post = <T = unknown, D = unknown>(url: string, data?: D): Promise<T> => {
  return request<D, T>({ method: 'POST', url, data } as RequestConfig<D>)
}

export const put = <T = unknown, D = unknown>(url: string, data?: D): Promise<T> => {
  return request<D, T>({ method: 'PUT', url, data } as RequestConfig<D>)
}

export const del = <T = unknown>(url: string): Promise<T> => {
  return request<unknown, T>({ method: 'DELETE', url } as RequestConfig<unknown>)
}

// 导出默认请求函数
export default request

// 导出axios实例（用于特殊情况）
export { service as axiosInstance }

// 导出Token管理函数
export { refreshToken, checkTokenStatus }

// 手动刷新Token（供外部调用，如测试页面）
export const ensureTokenValid = async (): Promise<boolean> => {
  try {
    console.log('手动触发Token刷新')
    return await refreshToken()
  } catch (error) {
    console.error('手动Token刷新失败:', error)
    return false
  }
}

// 初始化Token检查（应用启动时调用）
export const initTokenCheck = () => {
  console.log('Token响应头检查机制已初始化 - 仅依赖后端响应头提示')
}

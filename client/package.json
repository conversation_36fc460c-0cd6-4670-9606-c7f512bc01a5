{"name": "web-panel-client", "version": "1.0.0", "description": "海聚科技设备管理系统客户端", "scripts": {"dev": "vite --mode development", "build": "vite build --mode production", "build:dev": "vite build --mode development", "build:prod": "vite build --mode production", "preview": "vite preview", "verify-build": "node scripts/verify-build.js", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "test": "jest"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "axios": "^1.8.4", "echarts": "^5.4.3", "echarts-gl": "^2.0.9", "element-plus": "^2.3.8", "gl-matrix": "^3.4.3", "gsap": "^3.13.0", "html2canvas": "^1.4.1", "lodash-es": "^4.17.21", "monaco-editor": "^0.52.2", "mqtt": "^5.12.0", "pinia": "^2.1.4", "vue": "^3.3.4", "vue-router": "^4.2.4", "vuedraggable": "^4.1.0"}, "devDependencies": {"@types/axios": "^0.14.4", "@types/jest": "^29.5.12", "@types/lodash-es": "^4.17.12", "@types/node": "^20.17.30", "@typescript-eslint/eslint-plugin": "^7.1.0", "@typescript-eslint/parser": "^7.1.0", "@vitejs/plugin-vue": "^4.2.3", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.4.4", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.22.0", "jest": "^29.7.0", "prettier": "^3.2.5", "ts-jest": "^29.1.2", "typescript": "^5.3.3", "vite": "^4.4.6", "vue-tsc": "^1.8.27"}}
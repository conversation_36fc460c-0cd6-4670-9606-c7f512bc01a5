import { defineConfig, loadEnv, type ConfigEnv, type UserConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }: ConfigEnv): UserConfig => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '')

  return {
    plugins: [vue()],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src')
      }
    },
    // 开发服务器配置（仅在开发环境生效）
    server: {
      host: '0.0.0.0', // 允许外部访问
      port: 5179,      // 开发服务器端口
      open: true,      // 自动打开浏览器
      proxy: {
        '/api': {
          target: 'http://localhost:3001',
          changeOrigin: true,
          secure: false
        }
      }
    },
    // 构建配置
    build: {
      outDir: 'dist',
      sourcemap: mode === 'development',
      minify: mode === 'production' ? 'esbuild' : false
    },
    // 定义全局常量
    define: {
      __APP_ENV__: JSON.stringify(mode)
    }
  }
})

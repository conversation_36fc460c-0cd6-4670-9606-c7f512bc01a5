module.exports = {
  apps: [{
    name: "device-management",
    script: "./server/dist/index.js",
    instances: "max",
    exec_mode: "cluster",
    env: {
      NODE_ENV: "production",
      PORT: 3001
    },
    watch: false,
    max_memory_restart: "500M",
    log_date_format: "YYYY-MM-DD HH:mm:ss",
    // 修复：将PM2日志输出到专门的目录，避免与winston冲突
    error_file: "./logs/pm2/error.log",
    out_file: "./logs/pm2/out.log",
    merge_logs: true,
    // 新增：日志轮转配置
    log_file: "./logs/pm2/combined.log",
    time: true,
    // 新增：禁用PM2的日志时间戳，因为winston已经处理
    disable_logs: false,
    // 新增：环境变量确保winston日志正常工作
    env_production: {
      NODE_ENV: "production",
      PORT: 3001,
      LOG_LEVEL: "info",
      LOG_FILE_PATH: "./logs"
    }
  }]
};

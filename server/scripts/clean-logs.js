#!/usr/bin/env node

/**
 * 日志清理脚本
 * 清理超过指定天数的日志文件
 */

const fs = require('fs')
const path = require('path')

// 配置
const LOG_DIR = process.env.LOG_FILE_PATH || './logs'
const KEEP_DAYS = parseInt(process.env.LOG_KEEP_DAYS || '30') // 保留天数
const DRY_RUN = process.argv.includes('--dry-run') // 是否为试运行

console.log('🧹 开始清理日志文件...')
console.log(`📁 日志目录: ${LOG_DIR}`)
console.log(`📅 保留天数: ${KEEP_DAYS}`)
console.log(`🔍 模式: ${DRY_RUN ? '试运行（不会实际删除）' : '实际删除'}`)

// 计算截止日期
const cutoffDate = new Date()
cutoffDate.setDate(cutoffDate.getDate() - KEEP_DAYS)
console.log(`📆 删除早于 ${cutoffDate.toISOString().split('T')[0]} 的日志文件`)

// 检查日志目录是否存在
if (!fs.existsSync(LOG_DIR)) {
  console.log('❌ 日志目录不存在')
  process.exit(1)
}

// 读取日志目录
const files = fs.readdirSync(LOG_DIR)
const logFiles = files.filter(file => {
  // 匹配日期格式的日志文件：xxx-YYYY-MM-DD.log
  return /\d{4}-\d{2}-\d{2}\.log$/.test(file)
})

console.log(`📋 找到 ${logFiles.length} 个日期格式的日志文件`)

let deletedCount = 0
let deletedSize = 0

logFiles.forEach(file => {
  const filePath = path.join(LOG_DIR, file)
  const stats = fs.statSync(filePath)
  
  // 从文件名提取日期
  const dateMatch = file.match(/(\d{4}-\d{2}-\d{2})\.log$/)
  if (!dateMatch) return
  
  const fileDate = new Date(dateMatch[1])
  
  if (fileDate < cutoffDate) {
    const sizeInMB = (stats.size / 1024 / 1024).toFixed(2)
    
    if (DRY_RUN) {
      console.log(`🗑️  [试运行] 将删除: ${file} (${sizeInMB} MB)`)
    } else {
      try {
        fs.unlinkSync(filePath)
        console.log(`✅ 已删除: ${file} (${sizeInMB} MB)`)
      } catch (error) {
        console.error(`❌ 删除失败: ${file} - ${error.message}`)
        return
      }
    }
    
    deletedCount++
    deletedSize += stats.size
  }
})

const deletedSizeInMB = (deletedSize / 1024 / 1024).toFixed(2)

console.log('\n📊 清理统计:')
console.log(`🗑️  ${DRY_RUN ? '将删除' : '已删除'} ${deletedCount} 个文件`)
console.log(`💾 ${DRY_RUN ? '将释放' : '已释放'} ${deletedSizeInMB} MB 空间`)

if (DRY_RUN) {
  console.log('\n💡 要实际执行删除，请运行: node scripts/clean-logs.js')
} else {
  console.log('\n✅ 日志清理完成')
}

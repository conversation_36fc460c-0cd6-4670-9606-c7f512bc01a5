#!/usr/bin/env node

/**
 * 日志目录初始化脚本
 * 创建必要的日志目录结构
 */

const fs = require('fs')
const path = require('path')

// 日志目录配置
const LOG_BASE_DIR = process.env.LOG_FILE_PATH || './logs'
const LOG_DIRS = [
  LOG_BASE_DIR,           // winston日志主目录
  path.join(LOG_BASE_DIR, 'pm2'),  // PM2日志目录
  path.join(LOG_BASE_DIR, 'archive')  // 归档日志目录
]

console.log('🚀 开始初始化日志目录结构...')

// 创建日志目录
LOG_DIRS.forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true })
    console.log(`✅ 创建目录: ${dir}`)
  } else {
    console.log(`📁 目录已存在: ${dir}`)
  }
})

// 设置目录权限（Linux/Unix系统）
if (process.platform !== 'win32') {
  try {
    LOG_DIRS.forEach(dir => {
      fs.chmodSync(dir, 0o755)
    })
    console.log('✅ 设置目录权限完成')
  } catch (error) {
    console.warn('⚠️  设置目录权限失败:', error.message)
  }
}

// 创建日志配置说明文件
const readmeContent = `# 日志目录说明

## 目录结构
- \`./logs/\` - Winston日志文件
  - \`error-YYYY-MM-DD.log\` - 错误日志（按日轮转）
  - \`info-YYYY-MM-DD.log\` - 信息日志（按日轮转）
  - \`combined-YYYY-MM-DD.log\` - 综合日志（按日轮转）
  - \`error.log\` - 当前错误日志符号链接
  - \`info.log\` - 当前信息日志符号链接
  - \`combined.log\` - 当前综合日志符号链接

- \`./logs/pm2/\` - PM2进程管理日志
  - \`error.log\` - PM2错误日志
  - \`out.log\` - PM2标准输出日志
  - \`combined.log\` - PM2综合日志

- \`./logs/archive/\` - 归档日志目录

## 日志级别
- \`error\` - 错误信息
- \`warn\` - 警告信息
- \`info\` - 一般信息（包括console.log重定向）
- \`http\` - HTTP请求日志
- \`debug\` - 调试信息

## 日志轮转
- 每日自动轮转
- 保留30天历史日志
- 单个日志文件最大20MB

## 注意事项
1. 生产环境中console.log会自动重定向到winston日志
2. PM2日志和Winston日志分离，避免重复记录
3. 定期清理过期日志文件
`

const readmePath = path.join(LOG_BASE_DIR, 'README.md')
fs.writeFileSync(readmePath, readmeContent)
console.log(`📝 创建日志说明文件: ${readmePath}`)

console.log('\n✅ 日志目录初始化完成！')
console.log('\n📋 目录结构:')
LOG_DIRS.forEach(dir => {
  console.log(`   ${dir}`)
})

console.log('\n💡 使用建议:')
console.log('1. 生产环境启动前运行此脚本')
console.log('2. 定期运行 npm run clean-logs 清理过期日志')
console.log('3. 监控日志文件大小，避免磁盘空间不足')

#!/usr/bin/env node

/**
 * 安全检查脚本
 * 用于检查项目的安全配置和潜在漏洞
 */

const fs = require('fs')
const path = require('path')
const crypto = require('crypto')

// 如果直接运行此脚本，提供生成密钥的功能
if (process.argv.includes('--generate-jwt-secret')) {
  console.log('🔑 生成安全的JWT密钥:')
  console.log(generateSecureJWTSecret())
  return
}

console.log('🔍 开始安全检查...\n')

// 检查项目
const checks = {
  envFile: checkEnvFile,
  jwtSecret: checkJWTSecret,
  dependencies: checkDependencies,
  gitignore: checkGitignore,
  cors: checkCorsConfig,
  filePermissions: checkFilePermissions
}

let totalIssues = 0

// 执行所有检查
Object.entries(checks).forEach(([name, checkFn]) => {
  console.log(`📋 检查 ${name}...`)
  const issues = checkFn()
  if (issues.length > 0) {
    console.log(`❌ 发现 ${issues.length} 个问题:`)
    issues.forEach(issue => console.log(`   - ${issue}`))
    totalIssues += issues.length
  } else {
    console.log(`✅ ${name} 检查通过`)
  }
  console.log('')
})

// 总结
console.log('📊 安全检查总结:')
if (totalIssues === 0) {
  console.log('✅ 恭喜！未发现安全问题')
} else {
  console.log(`❌ 发现 ${totalIssues} 个安全问题需要修复`)
  process.exit(1)
}

/**
 * 检查环境变量文件
 */
function checkEnvFile() {
  const issues = []
  const envPath = path.join(__dirname, '../.env')
  
  if (!fs.existsSync(envPath)) {
    issues.push('.env 文件不存在')
    return issues
  }
  
  const envContent = fs.readFileSync(envPath, 'utf8')
  
  // 检查是否包含默认值
  const dangerousDefaults = [
    'your-secret-key',
    'your_jwt_secret',
    'postgres',
    '123456',
    'admin',
    'password'
  ]
  
  dangerousDefaults.forEach(defaultValue => {
    if (envContent.includes(defaultValue)) {
      issues.push(`发现默认值: ${defaultValue}`)
    }
  })
  
  // 检查是否包含真实的密钥
  if (envContent.includes('LTAI5tJAQeERj7F37ExVfrZM')) {
    issues.push('发现泄露的阿里云AccessKeyId')
  }
  
  if (envContent.includes('******************************')) {
    issues.push('发现泄露的阿里云AccessKeySecret')
  }
  
  return issues
}

/**
 * 检查JWT密钥强度
 */
function checkJWTSecret() {
  const issues = []
  
  try {
    require('dotenv').config({ path: path.join(__dirname, '../.env') })
    const jwtSecret = process.env.JWT_SECRET
    
    if (!jwtSecret) {
      issues.push('JWT_SECRET 未设置')
      return issues
    }
    
    if (jwtSecret.length < 32) {
      issues.push(`JWT密钥长度过短 (${jwtSecret.length} < 32)`)
    }
    
    if (jwtSecret === 'your-secret-key') {
      issues.push('JWT密钥使用默认值')
    }
    
    // 检查密钥复杂度
    const hasUppercase = /[A-Z]/.test(jwtSecret)
    const hasLowercase = /[a-z]/.test(jwtSecret)
    const hasNumbers = /\d/.test(jwtSecret)
    const hasSpecialChars = /[!@#$%^&*(),.?":{}|<>]/.test(jwtSecret)
    
    const complexity = [hasUppercase, hasLowercase, hasNumbers, hasSpecialChars].filter(Boolean).length
    
    if (complexity < 3) {
      issues.push('JWT密钥复杂度不足（建议包含大小写字母、数字和特殊字符）')
    }
    
  } catch (error) {
    issues.push(`无法检查JWT密钥: ${error.message}`)
  }
  
  return issues
}

/**
 * 检查依赖包
 */
function checkDependencies() {
  const issues = []
  const packagePath = path.join(__dirname, '../package.json')
  
  if (!fs.existsSync(packagePath)) {
    issues.push('package.json 不存在')
    return issues
  }
  
  const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'))
  const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies }
  
  // 检查是否安装了安全相关的包
  const securityPackages = ['helmet', 'express-rate-limit', 'express-validator']
  securityPackages.forEach(pkg => {
    if (!dependencies[pkg]) {
      issues.push(`缺少安全包: ${pkg}`)
    }
  })
  
  return issues
}

/**
 * 检查.gitignore
 */
function checkGitignore() {
  const issues = []
  const gitignorePath = path.join(__dirname, '../../.gitignore')
  
  if (!fs.existsSync(gitignorePath)) {
    issues.push('.gitignore 文件不存在')
    return issues
  }
  
  const gitignoreContent = fs.readFileSync(gitignorePath, 'utf8')
  
  const requiredEntries = ['.env', '.env.local', '.env.production', 'logs/', '*.log']
  requiredEntries.forEach(entry => {
    if (!gitignoreContent.includes(entry)) {
      issues.push(`gitignore 缺少条目: ${entry}`)
    }
  })
  
  return issues
}

/**
 * 检查CORS配置
 */
function checkCorsConfig() {
  const issues = []
  const appPath = path.join(__dirname, '../src/app.ts')
  
  if (!fs.existsSync(appPath)) {
    issues.push('app.ts 文件不存在')
    return issues
  }
  
  const appContent = fs.readFileSync(appPath, 'utf8')
  
  // 检查是否使用了不安全的CORS配置
  if (appContent.includes('app.use(cors())')) {
    issues.push('CORS配置过于宽松，允许所有域名访问')
  }
  
  return issues
}

/**
 * 检查文件权限
 */
function checkFilePermissions() {
  const issues = []
  
  try {
    const envPath = path.join(__dirname, '../.env')
    if (fs.existsSync(envPath)) {
      const stats = fs.statSync(envPath)
      const mode = stats.mode & parseInt('777', 8)
      
      // 检查文件权限是否过于宽松
      if (mode & parseInt('044', 8)) {
        issues.push('.env 文件权限过于宽松，其他用户可以读取')
      }
    }
  } catch (error) {
    // 在某些系统上可能无法检查权限
    console.log('⚠️  无法检查文件权限:', error.message)
  }
  
  return issues
}

/**
 * 生成安全的JWT密钥
 */
function generateSecureJWTSecret() {
  return crypto.randomBytes(64).toString('hex')
}

// 如果直接运行此脚本，提供生成密钥的功能
if (process.argv.includes('--generate-jwt-secret')) {
  console.log('🔑 生成安全的JWT密钥:')
  console.log(generateSecureJWTSecret())
  process.exit(0)
}

// 如果有参数，只执行特定功能
if (process.argv.length > 2 && !process.argv.includes('--generate-jwt-secret')) {
  console.log('❌ 未知参数')
  process.exit(1)
}

import { Sequelize } from 'sequelize'
import config from './index'

// 使用集中管理的配置
const { name, user, password, host, port } = config.database

// 创建Sequelize实例
const sequelize = new Sequelize(name, user, password, {
  host,
  port: parseInt(port),
  dialect: 'postgres',
  logging: config.nodeEnv === 'development' ? console.log : false,
  timezone: '+08:00',
  dialectOptions: {
    useUTC: false,
    dateStrings: true,
    typeCast: true,
    statement: {
      preparationStatements: [
        "SET timezone='Asia/Shanghai';"
      ]
    }
  },
  pool: {
    max: 20,        // 增加最大连接数，支持更多并发请求
    min: 2,         // 保持最小连接数，避免冷启动延迟
    acquire: 60000, // 增加获取连接超时时间到60秒，适应数据处理场景
    idle: 30000,    // 增加空闲超时时间到30秒，减少频繁创建/销毁连接
    evict: 1000     // 每1秒检查一次空闲连接
  }
})

// 测试数据库连接
export const testConnection = async () => {
  try {
    await sequelize.authenticate()
    console.log(`已连接到数据库: ${name} (${user}@${host}:${port})`)
  } catch (error) {
    console.error('数据库连接失败:', error)
    console.error(`尝试连接的配置: ${name} (${user}@${host}:${port})`)
  }
}

export default sequelize

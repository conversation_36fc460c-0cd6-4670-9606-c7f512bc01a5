import { RateLimitAlgorithm } from '../middleware/rateLimiter'
import { DDoSProtectionConfig } from '../middleware/ddosProtection'
import { securityConfig } from './security'

/**
 * 获取速率限制配置（统一配置，从环境变量读取）
 */
export function getRateLimitConfig() {
  return {
    // 基础速率限制 - 从统一配置读取
    basic: {
      algorithm: RateLimitAlgorithm.SLIDING_WINDOW,
      windowMs: securityConfig.rateLimit.api.windowMs,
      maxRequests: securityConfig.rateLimit.api.maxRequests,
      message: '请求过于频繁，请稍后再试',
      standardHeaders: true
    },

    // 登录限制 - 从统一配置读取，使用IP+用户名组合键
    login: {
      algorithm: RateLimitAlgorithm.SLIDING_WINDOW,
      windowMs: securityConfig.rateLimit.login.windowMs,
      maxRequests: securityConfig.rateLimit.login.maxRequests,
      message: '登录尝试过于频繁，请稍后再试',
      skipSuccessfulRequests: securityConfig.rateLimit.login.skipSuccessfulRequests,
      keyGenerator: (req: any) => `login:${req.ip}:${req.body?.username || 'unknown'}`
    },

    // DDoS防护配置 - 从统一配置读取
    ddos: {
      enabled: securityConfig.rateLimit.ddos.enabled,
      maxRequestsPerSecond: securityConfig.rateLimit.ddos.maxRequestsPerSecond,
      maxRequestsPerMinute: securityConfig.rateLimit.ddos.maxRequestsPerMinute,
      maxConcurrentConnections: securityConfig.rateLimit.ddos.maxConcurrentConnections,
      detectionWindowMs: securityConfig.rateLimit.ddos.detectionWindowMs,
      banDurationMs: securityConfig.rateLimit.ddos.banDurationMs,
      maxBanCount: securityConfig.rateLimit.ddos.maxBanCount,
      whitelist: securityConfig.rateLimit.ddos.whitelist,
      blacklist: securityConfig.rateLimit.ddos.blacklist,
      suspiciousUserAgents: securityConfig.rateLimit.ddos.suspiciousUserAgents
    } as DDoSProtectionConfig
  }
}





import crypto from 'crypto'

/**
 * 安全配置
 */
export const securityConfig = {
  // JWT配置
  jwt: {
    // 生成强随机密钥的函数
    generateSecret: (): string => {
      return crypto.randomBytes(64).toString('hex')
    },
    
    // JWT选项
    options: {
      algorithm: 'HS256' as const,
      issuer: 'web-panel',
      audience: 'web-panel-client',
      expiresIn: '15m', // 短期访问token
      refreshExpiresIn: '7d' // 长期刷新token
    }
  },

  // 密码策略
  password: {
    minLength: 8,
    maxLength: 128,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: true,
    specialChars: '@$!%*?&',
    
    // 密码强度验证
    validate: (password: string): { isValid: boolean; errors: string[] } => {
      const errors: string[] = []
      
      if (password.length < securityConfig.password.minLength) {
        errors.push(`密码长度不能少于${securityConfig.password.minLength}个字符`)
      }
      
      if (password.length > securityConfig.password.maxLength) {
        errors.push(`密码长度不能超过${securityConfig.password.maxLength}个字符`)
      }
      
      if (securityConfig.password.requireUppercase && !/[A-Z]/.test(password)) {
        errors.push('密码必须包含至少一个大写字母')
      }
      
      if (securityConfig.password.requireLowercase && !/[a-z]/.test(password)) {
        errors.push('密码必须包含至少一个小写字母')
      }
      
      if (securityConfig.password.requireNumbers && !/\d/.test(password)) {
        errors.push('密码必须包含至少一个数字')
      }
      
      if (securityConfig.password.requireSpecialChars) {
        const specialCharsRegex = new RegExp(`[${securityConfig.password.specialChars}]`)
        if (!specialCharsRegex.test(password)) {
          errors.push(`密码必须包含至少一个特殊字符 (${securityConfig.password.specialChars})`)
        }
      }
      
      return {
        isValid: errors.length === 0,
        errors
      }
    }
  },

  // 文件上传限制
  fileUpload: {
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedMimeTypes: [
      'text/csv'
    ],
    allowedExtensions: ['.csv'],
    
    // 文件类型验证
    validateFile: (filename: string, mimetype: string): { isValid: boolean; error?: string } => {
      const ext = filename.toLowerCase().substring(filename.lastIndexOf('.'))
      
      if (!securityConfig.fileUpload.allowedExtensions.includes(ext)) {
        return {
          isValid: false,
          error: `不支持的文件类型: ${ext}`
        }
      }
      
      if (!securityConfig.fileUpload.allowedMimeTypes.includes(mimetype)) {
        return {
          isValid: false,
          error: `不支持的MIME类型: ${mimetype}`
        }
      }
      
      return { isValid: true }
    }
  },

  // 输入验证
  input: {
    maxStringLength: 1000,
    maxArrayLength: 100,
    maxObjectDepth: 5,
    
    // 危险字符模式
    dangerousPatterns: [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, // Script标签
      /javascript:/gi, // JavaScript协议
      /on\w+\s*=/gi, // 事件处理器
      /eval\s*\(/gi, // eval函数
      /expression\s*\(/gi, // CSS expression
      /vbscript:/gi, // VBScript协议
      /data:text\/html/gi // Data URL HTML
    ],
    
    // XSS检测
    containsXSS: (input: string): boolean => {
      return securityConfig.input.dangerousPatterns.some(pattern => pattern.test(input))
    },
    
    // 清理输入
    sanitize: (input: string): string => {
      return input
        .trim()
        .replace(/[<>]/g, '') // 移除尖括号
        .replace(/javascript:/gi, '') // 移除JavaScript协议
        .replace(/on\w+=/gi, '') // 移除事件处理器
        .replace(/eval\s*\(/gi, '') // 移除eval
    }
  },

  // 算法执行安全
  algorithm: {
    maxExecutionTime: 30000, // 30秒
    maxMemoryUsage: 128 * 1024 * 1024, // 128MB
    maxCodeSize: 1024 * 1024, // 1MB
    minCodeSize: 10, // 最小代码大小
    maxParameterSize: 10 * 1024 * 1024, // 10MB参数大小限制
    maxNestingDepth: 20, // 最大嵌套深度
    maxFunctionCalls: 10000, // 最大函数调用次数

    // 禁止的API和模块（扩展列表）
    forbiddenAPIs: [
      'require',
      'import',
      'eval',
      'Function',
      'setTimeout',
      'setInterval',
      'setImmediate',
      'process',
      'global',
      'globalThis',
      'window',
      'Buffer',
      'fs',
      'path',
      'os',
      'child_process',
      'cluster',
      'crypto',
      'http',
      'https',
      'net',
      'dgram',
      'dns',
      'vm',
      'worker_threads',
      'inspector',
      'repl',
      'readline',
      'stream',
      'events',
      'util',
      'url',
      'querystring',
      'zlib',
      'tls',
      'perf_hooks',
      'async_hooks',
      'trace_events',
      'v8',
      'module'
    ],

    // 禁止的全局对象属性
    forbiddenGlobalProperties: [
      'constructor',
      '__proto__',
      'prototype',
      '__defineGetter__',
      '__defineSetter__',
      '__lookupGetter__',
      '__lookupSetter__',
      'valueOf',
      'toString',
      'hasOwnProperty',
      'isPrototypeOf',
      'propertyIsEnumerable'
    ],

    // 危险的字符串模式
    dangerousStringPatterns: [
      /javascript:/gi,
      /data:text\/html/gi,
      /vbscript:/gi,
      /<script/gi,
      /on\w+\s*=/gi,
      /expression\s*\(/gi
    ],
    
    // 检查代码是否包含禁止的API（增强版）
    containsForbiddenAPI: (code: string): { isValid: boolean; forbiddenAPIs: string[] } => {
      const found: string[] = []

      securityConfig.algorithm.forbiddenAPIs.forEach(api => {
        // 特殊处理Function - 只检测构造函数调用，不检测function关键字
        if (api === 'Function') {
          // 检测 new Function() 或 Function() 调用
          const functionCallRegex = /(?:new\s+Function\s*\(|Function\s*\()/gi
          if (functionCallRegex.test(code)) {
            found.push(api)
          }
        } else if (api === 'events') {
          // 特殊处理events - 只检测require('events')或import events，不检测变量名events
          const eventsImportRegex = /(?:require\s*\(\s*['"`]events['"`]\s*\)|import.*from\s*['"`]events['"`]|import\s*['"`]events['"`])/gi
          if (eventsImportRegex.test(code)) {
            found.push(api)
          }
        } else {
          // 对其他API使用更严格的检查方式，但排除一些常见的变量名
          const isCommonVariableName = ['events', 'util', 'path', 'url'].includes(api)

          if (isCommonVariableName) {
            // 对于常见变量名，只检查require/import语句
            const importRegex = new RegExp(`(?:require\\s*\\(\\s*['"\`]${api}['"\`]\\s*\\)|import.*from\\s*['"\`]${api}['"\`]|import\\s*['"\`]${api}['"\`])`, 'gi')
            if (importRegex.test(code)) {
              found.push(api)
            }
          } else {
            // 对于其他API使用严格检查
            const patterns = [
              new RegExp(`\\b${api}\\b`, 'gi'), // 直接调用
              new RegExp(`\\['${api}'\\]`, 'gi'), // 属性访问
              new RegExp(`\\["${api}"\\]`, 'gi'), // 属性访问
              new RegExp(`\\[\`${api}\`\\]`, 'gi'), // 模板字符串访问
              new RegExp(`\\.${api}\\b`, 'gi') // 点号访问
            ]

            if (patterns.some(pattern => pattern.test(code))) {
              found.push(api)
            }
          }
        }
      })

      return {
        isValid: found.length === 0,
        forbiddenAPIs: found
      }
    },

    // 验证算法代码完整性
    validateAlgorithmCode: (code: string): { isValid: boolean; errors: string[] } => {
      const errors: string[] = []

      // 1. 检查代码大小
      if (code.length < securityConfig.algorithm.minCodeSize) {
        errors.push(`代码过短，最少需要${securityConfig.algorithm.minCodeSize}个字符`)
      }

      if (code.length > securityConfig.algorithm.maxCodeSize) {
        errors.push(`代码过长，最多允许${securityConfig.algorithm.maxCodeSize}个字符`)
      }

      // 2. 检查危险字符串模式
      securityConfig.algorithm.dangerousStringPatterns.forEach(pattern => {
        if (pattern.test(code)) {
          errors.push(`代码包含危险模式: ${pattern.source}`)
        }
      })

      // 3. 检查禁止的全局属性访问
      securityConfig.algorithm.forbiddenGlobalProperties.forEach(prop => {
        const patterns = [
          new RegExp(`\\b${prop}\\b`, 'gi'),
          new RegExp(`\\['${prop}'\\]`, 'gi'),
          new RegExp(`\\["${prop}"\\]`, 'gi')
        ]

        if (patterns.some(pattern => pattern.test(code))) {
          errors.push(`禁止访问全局属性: ${prop}`)
        }
      })

      // 4. 检查代码结构
      const structureErrors = securityConfig.algorithm.validateCodeStructure(code)
      errors.push(...structureErrors)

      return {
        isValid: errors.length === 0,
        errors
      }
    },

    // 验证代码结构
    validateCodeStructure: (code: string): string[] => {
      const errors: string[] = []

      // 检查括号匹配
      const brackets = { '(': 0, '[': 0, '{': 0 }
      for (const char of code) {
        if (char === '(') brackets['(']++
        else if (char === ')') brackets['(']--
        else if (char === '[') brackets['[']++
        else if (char === ']') brackets['[']--
        else if (char === '{') brackets['{']++
        else if (char === '}') brackets['{']--
      }

      Object.entries(brackets).forEach(([bracket, count]) => {
        if (count !== 0) {
          errors.push(`括号不匹配: ${bracket}`)
        }
      })

      // 检查可能的无限循环
      const infiniteLoopPatterns = [
        /while\s*\(\s*true\s*\)/gi,
        /for\s*\(\s*;\s*;\s*\)/gi,
        /while\s*\(\s*1\s*\)/gi
      ]

      infiniteLoopPatterns.forEach(pattern => {
        if (pattern.test(code)) {
          errors.push('检测到可能的无限循环')
        }
      })

      // 检查过度递归
      const recursivePatterns = [
        /function\s+(\w+)[^}]*\1\s*\(/gi
      ]

      recursivePatterns.forEach(pattern => {
        if (pattern.test(code)) {
          errors.push('检测到可能的递归调用，请确保有正确的终止条件')
        }
      })

      return errors
    },

    // 验证运行时参数
    validateRuntimeParameters: (params: any): { isValid: boolean; errors: string[] } => {
      const errors: string[] = []

      try {
        const paramString = JSON.stringify(params)

        // 检查参数大小
        if (paramString.length > securityConfig.algorithm.maxParameterSize) {
          errors.push(`参数过大，最大允许${Math.round(securityConfig.algorithm.maxParameterSize/1024/1024)}MB`)
        }

        // 检查嵌套深度
        const depth = securityConfig.algorithm.calculateObjectDepth(params)
        if (depth > securityConfig.algorithm.maxNestingDepth) {
          errors.push(`参数嵌套过深，最大允许${securityConfig.algorithm.maxNestingDepth}层`)
        }

        // 检查参数中的危险内容
        securityConfig.algorithm.dangerousStringPatterns.forEach(pattern => {
          if (pattern.test(paramString)) {
            errors.push(`参数包含危险内容: ${pattern.source}`)
          }
        })

      } catch (error) {
        errors.push('参数序列化失败，可能包含循环引用')
      }

      return {
        isValid: errors.length === 0,
        errors
      }
    },

    // 计算对象嵌套深度
    calculateObjectDepth: (obj: any, currentDepth: number = 0): number => {
      if (currentDepth > 100) return currentDepth // 防止栈溢出

      if (obj === null || typeof obj !== 'object') {
        return currentDepth
      }

      let maxDepth = currentDepth
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          const depth = securityConfig.algorithm.calculateObjectDepth(obj[key], currentDepth + 1)
          maxDepth = Math.max(maxDepth, depth)
        }
      }

      return maxDepth
    }
  },

  // 速率限制配置
  rateLimit: {
    // 基础API限制
    api: {
      windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15分钟
      maxRequests: parseInt(process.env.RATE_LIMIT_MAX || '1000'),
      algorithm: 'SLIDING_WINDOW' as const
    },

    // 登录限制
    login: {
      windowMs: parseInt(process.env.LOGIN_RATE_LIMIT_WINDOW_MS || '900000'), // 15分钟
      maxRequests: parseInt(process.env.LOGIN_RATE_LIMIT_MAX || '5'),
      skipSuccessfulRequests: true
    },

    // DDoS防护
    ddos: {
      enabled: process.env.DDOS_PROTECTION_ENABLED !== 'false',
      maxRequestsPerSecond: parseInt(process.env.DDOS_MAX_REQUESTS_PER_SECOND || '20'),
      maxRequestsPerMinute: parseInt(process.env.DDOS_MAX_REQUESTS_PER_MINUTE || '300'),
      maxConcurrentConnections: parseInt(process.env.DDOS_MAX_CONCURRENT_CONNECTIONS || '100'),
      detectionWindowMs: parseInt(process.env.DDOS_DETECTION_WINDOW_MS || '60000'),
      banDurationMs: parseInt(process.env.DDOS_BAN_DURATION_MS || '600000'), // 10分钟
      maxBanCount: parseInt(process.env.DDOS_MAX_BAN_COUNT || '3'),
      whitelist: (process.env.DDOS_IP_WHITELIST || '127.0.0.1,::1').split(',').map(ip => ip.trim()),
      blacklist: (process.env.DDOS_IP_BLACKLIST || '').split(',').filter(ip => ip.trim()),
      suspiciousUserAgents: [
        'bot', 'crawler', 'spider', 'scraper', 'scanner',
        'curl', 'wget', 'python', 'java', 'go-http-client',
        'masscan', 'nmap', 'sqlmap', 'nikto'
      ]
    }
  },

  // 会话管理
  session: {
    maxConcurrentSessions: 5, // 每个用户最多5个并发会话
    sessionTimeout: 15 * 60 * 1000, // 15分钟无活动超时
    refreshTokenRotation: true, // 启用刷新token轮换
    
    // 生成会话ID
    generateSessionId: (): string => {
      return crypto.randomBytes(32).toString('hex')
    }
  },

  // 审计日志
  audit: {
    logSensitiveOperations: true,
    sensitiveOperations: [
      'login',
      'logout',
      'register',
      'password_change',
      'algorithm_create',
      'algorithm_update',
      'algorithm_delete',
      'device_create',
      'device_update',
      'device_delete',
      'file_upload',
      'file_delete'
    ],
    
    // 记录审计日志
    log: (operation: string, userId: string, details: any, req: any): void => {
      if (securityConfig.audit.sensitiveOperations.includes(operation)) {
        console.log('AUDIT LOG:', {
          timestamp: new Date().toISOString(),
          operation,
          userId,
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          details: JSON.stringify(details)
        })
      }
    }
  }
}

/**
 * 生成强随机密码
 */
export const generateSecurePassword = (length: number = 16): string => {
  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789@$!%*?&'
  let password = ''
  
  for (let i = 0; i < length; i++) {
    password += charset.charAt(crypto.randomInt(0, charset.length))
  }
  
  return password
}

/**
 * 生成安全的JWT密钥
 */
export const generateJWTSecret = (): string => {
  return securityConfig.jwt.generateSecret()
}

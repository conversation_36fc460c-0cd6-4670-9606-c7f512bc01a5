import { Response, NextFunction, Request } from 'express'
import { AppError } from '../middleware/error'
import { ChartComponent } from '../models'
import { Op } from 'sequelize'

// 定义查询参数接口
interface ChartComponentQueryParams {
  type?: string
  product_key?: string
  status?: string
  limit?: string
  offset?: string
}

/**
 * 获取所有图表组件
 */
export const getAllCharts = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { 
      type, 
      product_key, 
      status,
      limit = '10',
      offset = '0'
    } = req.query as ChartComponentQueryParams
    
    // 构建查询条件
    const conditions: any = {}
    if (type) conditions.type = type
    if (product_key) conditions.product_key = product_key
    if (status) conditions.status = parseInt(status, 10)

    // 分页参数
    const limitNum = parseInt(limit, 10)
    const offsetNum = parseInt(offset, 10)
    
    // 查询数据
    const { count, rows } = await ChartComponent.findAndCountAll({
      where: conditions,
      limit: limitNum,
      offset: offsetNum,
      order: [['id', 'ASC']]
    })
    
    res.status(200).json({
      success: true,
      data: {
        total: count,
        list: rows
      }
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 根据ID获取图表组件
 */
export const getChartById = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { id } = req.params
    if (!id) {
      throw new AppError('图表ID不能为空', 400)
    }
    
    const chart = await ChartComponent.findByPk(parseInt(id, 10))
    
    if (!chart) {
      throw new AppError('图表不存在', 404)
    }
    
    res.status(200).json({
      success: true,
      data: chart
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 创建图表组件
 */
export const createChart = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { name, type, component, config, product_key, description, status } = req.body
    
    if (!name || !type || !component) {
      throw new AppError('名称、类型和组件参数不能为空', 400)
    }
    
    // 创建图表组件
    const newChart = await ChartComponent.create({
      name,
      type,
      component,
      config: config || '{}',
      product_key,
      description,
      status: status || 1,
      created_at: new Date()
    })
    
    res.status(201).json({
      success: true,
      data: newChart
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 更新图表组件
 */
export const updateChart = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { id } = req.params
    const { name, type, component, config, product_key, description, status } = req.body
    
    if (!id) {
      throw new AppError('图表ID不能为空', 400)
    }
    
    // 检查图表是否存在
    const chart = await ChartComponent.findByPk(parseInt(id, 10))
    if (!chart) {
      throw new AppError('图表不存在', 404)
    }
    
    // 更新图表数据
    const updateData: any = {
      modified_at: new Date()
    }
    
    if (name) updateData.name = name
    if (type) updateData.type = type
    if (component) updateData.component = component
    if (config) updateData.config = config
    if (product_key !== undefined) updateData.product_key = product_key
    if (description !== undefined) updateData.description = description
    if (status !== undefined) updateData.status = status
    
    await chart.update(updateData)
    
    // 重新获取最新数据
    const updatedChart = await ChartComponent.findByPk(parseInt(id, 10))
    
    res.status(200).json({
      success: true,
      data: updatedChart!
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 删除图表组件
 */
export const deleteChart = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { id } = req.params
    
    if (!id) {
      throw new AppError('图表ID不能为空', 400)
    }
    
    // 检查图表是否存在
    const chart = await ChartComponent.findByPk(parseInt(id, 10))
    if (!chart) {
      throw new AppError('图表不存在', 404)
    }
    
    // 删除图表
    await chart.destroy()
    
    res.status(200).json({
      success: true,
      message: '删除图表组件成功'
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 根据图表类型获取图表组件
 */
export const getChartsByType = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { type } = req.query as { type?: string }
    
    if (!type) {
      throw new AppError('图表类型不能为空', 400)
    }
    
    const charts = await ChartComponent.findAll({
      where: { type },
      order: [['id', 'ASC']]
    })
    
    res.status(200).json({
      success: true,
      data: {
        total: charts.length,
        list: charts
      }
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 根据产品类型获取图表组件
 */
export const getChartsByProduct = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { product_key } = req.query as { product_key?: string }
    
    // 构建查询条件，如果product_key为空则查询通用图表
    const conditions: any = product_key 
      ? { product_key, status: 1 } 
      : { 
          [Op.or]: [
            { product_key: null },
            { product_key: '' }
          ],
          status: 1
        }
    
    const charts = await ChartComponent.findAll({
      where: conditions,
      order: [['id', 'ASC']]
    })
    
    res.status(200).json({
      success: true,
      data: {
        total: charts.length,
        list: charts
      }
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 启用/禁用图表组件
 */
export const setChartStatus = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { id } = req.params
    const { status } = req.body
    
    if (!id) {
      throw new AppError('图表ID不能为空', 400)
    }
    
    if (status === undefined) {
      throw new AppError('状态参数不能为空', 400)
    }
    
    // 检查图表是否存在
    const chart = await ChartComponent.findByPk(parseInt(id, 10))
    if (!chart) {
      throw new AppError('图表不存在', 404)
    }
    
    // 更新状态
    await chart.update({
      status,
      modified_at: new Date()
    })
    
    // 重新获取最新数据
    const updatedChart = await ChartComponent.findByPk(parseInt(id, 10))
    
    res.status(200).json({
      success: true,
      data: updatedChart!
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 获取图表组件统计信息
 */
export const getChartStats = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const charts = await ChartComponent.findAll()
    
    const stats = {
      total: charts.length,
      byType: {} as Record<string, number>,
      byStatus: { '0': 0, '1': 0 } as Record<string, number>
    }

    charts.forEach(chart => {
      // 按类型统计
      if (stats.byType[chart.type]) {
        stats.byType[chart.type]++
      } else {
        stats.byType[chart.type] = 1
      }

      // 按状态统计
      const status = String(chart.status || 1)
      if (stats.byStatus[status] !== undefined) {
        stats.byStatus[status]++
      } else {
        stats.byStatus[status] = 1
      }
    })
    
    res.status(200).json({
      success: true,
      data: stats
    })
  } catch (error) {
    next(error)
  }
} 
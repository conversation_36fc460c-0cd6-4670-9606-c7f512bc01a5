import { Response, NextFunction, Request } from 'express'
import { <PERSON><PERSON>, DeviceArithmeticRef, Arithmetic, File } from '../models'
import { AppError } from '../middleware/error'
import { Op, QueryTypes } from 'sequelize'
import { getDeviceTypeName, formatDateTime } from '../utils/utils'
import sequelize from '../config/database'
import { getSecureDeviceTableName, validateDeviceTable } from '../models/data'
import { validateDeviceSerialNumber } from '../middleware/validation'

/**
 * 获取设备列表
 */
export const getDeviceList = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const {
      type,
      name,
      serialNumber,
      page = '1',
      pageSize = '10'
    } = req.query as {
      type?: string
      name?: string
      serialNumber?: string
      page?: string
      pageSize?: string
    }

    // 构建查询条件
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const where: any = {}

    // 根据产品类型过滤
    if (type) {
      where.product_key = type
    }

    // 根据序列号过滤
    if (serialNumber) {
      where.device_sn = { [Op.like]: `%${serialNumber}%` }
    }

    // 根据设备名称过滤
    if (name) {
      where.device_name = { [Op.like]: `%${name}%` }
    }

    // 分页设置
    const pageNumber = parseInt(page)
    const limit = parseInt(pageSize)
    const offset = (pageNumber - 1) * limit

    // 查询设备列表，关联查询最新文件信息
    const { count, rows } = await Device.findAndCountAll({
      where,
      limit,
      offset,
      order: [['created_at', 'DESC']],
      include: [
        {
          model: File,
          as: 'files',
          attributes: ['id', 'modified_at'],
          required: false, // LEFT JOIN，即使没有文件也显示设备
          order: [['id', 'DESC']],
          limit: 1 // 只获取最新的一个文件
        }
      ]
    })

    // 转换为前端需要的格式
    const devices = rows.map(device => {
      // 使用公共工具函数获取设备类型名称
      const typeName = getDeviceTypeName(device.product_key)

      // 获取最新文件的上传时间（使用类型断言处理关联查询结果）
      const deviceWithFiles = device as any
      const latestFile = deviceWithFiles.files && deviceWithFiles.files.length > 0 ? deviceWithFiles.files[0] : null
      const lastFileTime = latestFile ? formatDateTime(latestFile.modified_at) : null

      return {
        id: device.id,
        type: device.product_key,
        typeName: typeName,
        deviceName: device.device_name,
        serialNumber: device.device_sn || `${device.product_key}-${device.id}`,
        lastDeviceTime: formatDateTime(device.modified_at),
        lastFileTime: lastFileTime
      }
    })

    res.status(200).json({
      success: true,
      data: {
        total: count,
        list: devices
      }
    })
  } catch (error) {
    console.error('获取设备列表失败:', error)
    next(error)
  }
}

/**
 * 获取设备详情
 */
export const getDeviceDetail = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { id } = req.params

    const device = await Device.findByPk(id)

    if (!device) {
      throw new AppError('设备不存在', 404)
    }

    // 使用公共工具函数获取设备类型名称
    const typeName = getDeviceTypeName(device.product_key)

    res.status(200).json({
      success: true,
      data: {
        id: device.id,
        type: device.product_key,
        typeName: typeName,
        deviceName: device.device_name,
        projectName: device.project_name,
        serialNumber: device.device_sn,
        productKey: device.product_key,
        macAddress: device.device_mac,
        deviceSecret: device.device_secret,
        createdAt: formatDateTime(device.created_at),
        modifiedAt: formatDateTime(device.modified_at)
      }
    })
  } catch (error) {
    console.error('获取设备详情失败:', error)
    next(error)
  }
}

/**
 * 获取设备列表（包含算法信息）
 */
export const getDeviceListWithAlgorithm = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const {
      type,
      name,
      serialNumber,
      algorithmType,
      arithmeticId,
      page = '1',
      pageSize = '100'
    } = req.query as {
      type?: string
      name?: string
      serialNumber?: string
      algorithmType?: string
      arithmeticId?: string
      page?: string
      pageSize?: string
    }

    // 构建查询条件
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const where: any = {}

    // 根据产品类型过滤
    if (type) {
      where.product_key = type
    }

    // 根据序列号过滤
    if (serialNumber) {
      where.device_sn = { [Op.like]: `%${serialNumber}%` }
    }

    // 根据设备名称过滤
    if (name) {
      where.device_name = { [Op.like]: `%${name}%` }
    }

    // 分页设置
    const pageNumber = parseInt(page)
    const limit = parseInt(pageSize)
    const offset = (pageNumber - 1) * limit

    // 获取已经应用了指定算法的设备ID列表（用于标识状态，不再用于过滤）
    let appliedDeviceIds: number[] = []
    if (arithmeticId && algorithmType !== undefined) {
      const appliedDevices = await DeviceArithmeticRef.findAll({
        where: {
          arithmetic_id: arithmeticId,
          arithmetic_type: algorithmType
        },
        attributes: ['device_id']
      })
      appliedDeviceIds = appliedDevices.map(d => d.device_id)
    }

    // 查询设备列表，关联查询最新文件信息
    const { count, rows } = await Device.findAndCountAll({
      where,
      limit,
      offset,
      order: [['created_at', 'DESC']],
      include: [
        {
          model: File,
          as: 'files',
          attributes: ['id', 'modified_at'],
          required: false, // LEFT JOIN，即使没有文件也显示设备
          order: [['id', 'DESC']],
          limit: 1 // 只获取最新的一个文件
        }
      ]
    })

    // 获取所有设备ID
    const deviceIds = rows.map(device => device.id)

    // 查询设备关联的算法信息
    let deviceAlgorithmRefs = []
    if (algorithmType !== undefined) {
      // 如果指定了算法类型，只查询该类型的算法
      deviceAlgorithmRefs = await DeviceArithmeticRef.findAll({
        where: {
          device_id: { [Op.in]: deviceIds },
          arithmetic_type: algorithmType
        }
      })
    } else {
      // 否则查询所有类型的算法
      deviceAlgorithmRefs = await DeviceArithmeticRef.findAll({
        where: {
          device_id: { [Op.in]: deviceIds }
        }
      })
    }

    // 获取所有关联算法的ID
    const arithmeticIds = deviceAlgorithmRefs.map(ref => ref.arithmetic_id)

    // 查询所有关联的算法详情
    const arithmetics = await Arithmetic.findAll({
      where: {
        id: { [Op.in]: arithmeticIds }
      }
    })

    // 创建算法映射表，方便快速查找
    interface ArithmeticInfo {
      id: number
      name: string
      type: number
      productKey: string
    }

    const arithmeticMap: Record<number, ArithmeticInfo> = {}
    arithmetics.forEach(arithmetic => {
      if (arithmetic.id !== undefined) {
        arithmeticMap[arithmetic.id] = {
          id: arithmetic.id,
          name: arithmetic.name || '',
          type: arithmetic.type || 0,
          productKey: arithmetic.product_key || ''
        }
      }
    })

    // 创建设备-算法关联映射表
    interface AlgorithmRefInfo extends ArithmeticInfo {
      refId: number
      arithmeticType: number
    }

    const deviceAlgorithmMap: Record<number, AlgorithmRefInfo[]> = {}
    deviceAlgorithmRefs.forEach(ref => {
      if (!deviceAlgorithmMap[ref.device_id]) {
        deviceAlgorithmMap[ref.device_id] = []
      }
      if (arithmeticMap[ref.arithmetic_id]) {
        deviceAlgorithmMap[ref.device_id].push({
          ...arithmeticMap[ref.arithmetic_id],
          refId: ref.id,
          arithmeticType: ref.arithmetic_type
        })
      }
    })

    // 转换为前端需要的格式
    const devices = rows.map(device => {
      // 使用公共工具函数获取设备类型名称
      const typeName = getDeviceTypeName(device.product_key)

      // 检查设备是否已应用当前算法
      const isApplied = appliedDeviceIds.includes(device.id)

      // 获取最新文件的上传时间（使用类型断言处理关联查询结果）
      const deviceWithFiles = device as any
      const latestFile = deviceWithFiles.files && deviceWithFiles.files.length > 0 ? deviceWithFiles.files[0] : null
      const lastFileTime = latestFile ? formatDateTime(latestFile.modified_at) : null

      return {
        id: device.id,
        type: device.product_key,
        typeName: typeName,
        name: device.device_name || `${typeName}设备${device.id}`,
        serialNumber: device.device_sn || `${device.product_key}-${device.id}`,
        lastDataTime: formatDateTime(device.modified_at),
        lastFileTime: lastFileTime,
        algorithms: deviceAlgorithmMap[device.id] || [], // 添加设备关联的算法信息
        isApplied: isApplied // 标识是否已应用当前算法
      }
    })

    res.status(200).json({
      success: true,
      data: {
        total: count,
        list: devices
      }
    })
  } catch (error) {
    console.error('获取带算法信息的设备列表失败:', error)
    next(error)
  }
}

/**
 * 更新设备信息
 */
export const updateDevice = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { id } = req.params
    const { deviceName, projectName, serialNumber, macAddress, deviceSecret } = req.body

    const device = await Device.findByPk(id)

    if (!device) {
      throw new AppError('设备不存在', 404)
    }

    // 更新设备信息
    await device.update({
      device_name: deviceName,
      device_sn: serialNumber,
      device_mac: macAddress,
      device_secret: deviceSecret,
      project_name: projectName,
      modified_at: new Date() // 更新修改时间
    })

    // 使用公共工具函数获取设备类型名称
    const typeName = getDeviceTypeName(device.product_key)

    res.status(200).json({
      success: true,
      data: {
        id: device.id,
        type: device.product_key,
        typeName: typeName,
        deviceName: device.device_name,
        projectName: device.project_name,
        serialNumber: device.device_sn,
        productKey: device.product_key,
        macAddress: device.device_mac,
        deviceSecret: device.device_secret,
        createdAt: formatDateTime(device.created_at),
        modifiedAt: formatDateTime(device.modified_at)
      },
      message: '设备信息更新成功'
    })
  } catch (error) {
    console.error('更新设备信息失败:', error)
    next(error)
  }
}

/**
 * 获取设备的数字岩芯信息
 */
export const getDeviceDigitalCore = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { deviceId } = req.params;
    const { page = '1', pageSize = '10' } = req.query as { page?: string, pageSize?: string };

    if (!deviceId) {
      throw new AppError('设备ID不能为空', 400);
    }

    // 获取设备信息
    const device = await Device.findByPk(deviceId);
    if (!device || !device.device_sn) {
      throw new AppError('设备不存在或缺少序列号', 404);
    }
    const device_sn = device.device_sn;

    // 分页设置
    const pageNumber = parseInt(page);
    const limit = parseInt(pageSize);
    const offset = (pageNumber - 1) * limit;

    // 查询dh_file表，获取设备的文件列表
    const { count, rows } = await File.findAndCountAll({
      where: {
        device_sn: device_sn
      },
      limit,
      offset,
      order: [['created_at', 'DESC']]
    });

    // 构建结果数据
    const digitalCoreList = await Promise.all(rows.map(async (file: any) => {
      // 从文件名中提取图片名称(不带.csv部分)
      const fileName = file.file_name || '';
      const imageName = fileName.replace(/\.csv$/i, '');
      
      // 安全构建设备原始数据表名
      if (!validateDeviceSerialNumber(device_sn)) {
        console.warn(`无效的设备序列号: ${device_sn}`)
        return {
          fileId: file.id,
          fileName: file.file_name,
          imageName,
          startDepth: null,
          endDepth: null,
          dataCount: 0
        }
      }

      const tableName = getSecureDeviceTableName(device_sn)

      // 验证表是否存在
      const tableExists = await validateDeviceTable(device_sn)
      if (!tableExists) {
        console.warn(`设备数据表不存在: ${device_sn}`)
        return {
          fileId: file.id,
          fileName: file.file_name,
          imageName,
          startDepth: null,
          endDepth: null,
          dataCount: 0
        }
      }

      // 查询该文件时间范围内的深度数据
      let startDepth: number | null = null;
      let endDepth: number | null = null;
      let dataCount = 0;
      
      if (file.first_data_time && file.last_data_time) {
        try {
          // 查询最小和最大深度值
          const depthQuery = `
            SELECT 
              MIN(dpth) as min_depth, 
              MAX(dpth) as max_depth,
              COUNT(*) as data_count
            FROM ${tableName}
            WHERE collection_at BETWEEN :startTime AND :endTime
          `;
          
          const depthResult = await sequelize.query(depthQuery, {
            replacements: {
              startTime: file.first_data_time,
              endTime: file.last_data_time
            },
            type: QueryTypes.SELECT
          });
          
          if (depthResult && depthResult.length > 0) {
            const result = depthResult[0] as any;
            startDepth = result.min_depth !== null ? Number(result.min_depth) : null;
            endDepth = result.max_depth !== null ? Number(result.max_depth) : null;
            dataCount = result.data_count !== null ? Number(result.data_count) : 0;
          }
        } catch (error) {
          console.error(`查询设备${deviceId}深度数据失败:`, error);
        }
      }
      
      return {
        fileId: file.id,
        fileName: fileName,
        imageName: imageName,
        fileSize: file.file_size || 0,
        holeNo: file.hole_no || '',
        startTime: formatDateTime(file.first_data_time),
        endTime: formatDateTime(file.last_data_time),
        createdAt: formatDateTime(file.created_at),
        dataCount: dataCount,
        startDepth: startDepth,
        endDepth: endDepth
      };
    }));

    res.status(200).json({
      success: true,
      data: {
        total: count,
        list: digitalCoreList
      }
    });
  } catch (error) {
    console.error('获取设备数字岩芯信息失败:', error);
    next(error);
  }
}

/**
 * 获取数字岩芯详情
 */
export const getDeviceDigitalCoreDetail = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { deviceId, fileId } = req.params;

    if (!deviceId || !fileId) {
      throw new AppError('设备ID和文件ID不能为空', 400);
    }

    // 获取设备信息
    const device = await Device.findByPk(deviceId);
    if (!device || !device.device_sn) {
      throw new AppError('设备不存在或缺少序列号', 404);
    }
    const device_sn = device.device_sn;

    // 查询文件信息
    const file = await File.findOne({
      where: {
        id: fileId,
        device_sn: device_sn
      }
    });

    if (!file) {
      throw new AppError('文件不存在', 404);
    }

    // 从文件名中提取图片名称(不带.csv部分)
    const fileName = file.file_name || '';
    const imageName = fileName.replace(/\.csv$/i, '');
    
    // 安全构建设备原始数据表名
    if (!validateDeviceSerialNumber(device_sn)) {
      throw new AppError(`无效的设备序列号: ${device_sn}`, 400)
    }

    const tableName = getSecureDeviceTableName(device_sn)

    // 验证表是否存在
    const tableExists = await validateDeviceTable(device_sn)
    if (!tableExists) {
      throw new AppError(`设备数据表不存在: ${device_sn}`, 404)
    }

    // 查询该文件时间范围内的深度数据
    let startDepth: number | null = null;
    let endDepth: number | null = null;
    let dataCount = 0;
    
    if (file.first_data_time && file.last_data_time) {
      try {
        // 查询最小和最大深度值
        const depthQuery = `
          SELECT 
            MIN(dpth) as min_depth, 
            MAX(dpth) as max_depth,
            COUNT(*) as data_count
          FROM ${tableName}
          WHERE collection_at BETWEEN :startTime AND :endTime
        `;
        
        const depthResult = await sequelize.query(depthQuery, {
          replacements: {
            startTime: file.first_data_time,
            endTime: file.last_data_time
          },
          type: QueryTypes.SELECT
        });
        
        if (depthResult && depthResult.length > 0) {
          const result = depthResult[0] as any;
          startDepth = result.min_depth !== null ? Number(result.min_depth) : null;
          endDepth = result.max_depth !== null ? Number(result.max_depth) : null;
          dataCount = result.data_count !== null ? Number(result.data_count) : 0;
        }
      } catch (error) {
        console.error(`查询设备${deviceId}深度数据失败:`, error);
      }
    }
    
    const coreData = {
      fileId: file.id,
      fileName: fileName,
      imageName: imageName,
      fileSize: file.file_size || 0,
      holeNo: file.hole_no || '',
      startTime: formatDateTime(file.first_data_time),
      endTime: formatDateTime(file.last_data_time),
      createdAt: formatDateTime(file.created_at),
      dataCount: dataCount,
      startDepth: startDepth,
      endDepth: endDepth
    };

    res.status(200).json({
      success: true,
      data: coreData
    });
  } catch (error) {
    console.error('获取数字岩芯详情失败:', error);
    next(error);
  }
}

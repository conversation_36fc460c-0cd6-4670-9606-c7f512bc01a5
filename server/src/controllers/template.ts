import { Response, NextFunction, Request } from 'express'
import { AppError } from '../middleware/error'
import { Template, DeviceTemplateConfig } from '../models'
import { Op } from 'sequelize'

// 定义查询参数接口
interface TemplateQueryParams {
  name?: string
  status?: string
  page?: string
  pageSize?: string
}

/**
 * 获取所有模板
 */
export const getAllTemplates = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const {
      name,
      status,
      page = '1',
      pageSize = '10'
    } = req.query as TemplateQueryParams

    // 构建查询条件
    const conditions: any = {}
    if (name) conditions.name = { [Op.like]: `%${name}%` }
    if (status) conditions.status = parseInt(status, 10)

    // 分页参数处理
    const pageNum = parseInt(page, 10)
    const pageSizeNum = parseInt(pageSize, 10)
    const limitNum = pageSizeNum
    const offsetNum = (pageNum - 1) * pageSizeNum
    
    // 先查询模板列表
    const { count, rows } = await Template.findAndCountAll({
      where: conditions,
      limit: limitNum,
      offset: offsetNum,
      order: [['id', 'DESC']]
    })

    // 为每个模板查询设备数量 - 使用批量查询优化
    const templateIds = rows.map(template => template.id)
    const deviceCounts = await DeviceTemplateConfig.findAll({
      where: {
        template_id: templateIds
      },
      attributes: [
        'template_id',
        [Template.sequelize!.fn('COUNT', Template.sequelize!.col('id')), 'count']
      ],
      group: ['template_id'],
      raw: true
    }) as any[]

    // 创建设备数量映射
    const deviceCountMap = new Map()
    deviceCounts.forEach(item => {
      deviceCountMap.set(item.template_id, parseInt(item.count, 10))
    })
    
    // 合并模板数据和设备数量
    const templatesWithDeviceCount = rows.map(template => {
      const templateData = template.toJSON()
      return {
        ...templateData,
        deviceCount: deviceCountMap.get(template.id) || 0
      }
    })

    res.status(200).json({
      success: true,
      data: {
        total: count,
        list: templatesWithDeviceCount
      }
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 根据ID获取模板
 */
export const getTemplateById = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { id } = req.params
    if (!id) {
      throw new AppError('模板ID不能为空', 400)
    }
    
    const template = await Template.findByPk(parseInt(id, 10))
    
    if (!template) {
      throw new AppError('模板不存在', 404)
    }
    
    res.status(200).json({
      success: true,
      data: template
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 创建模板
 */
export const createTemplate = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { name, config, description, status } = req.body
    
    if (!name) {
      throw new AppError('模板名称不能为空', 400)
    }
    
    // 创建模板
    const newTemplate = await Template.create({
      name,
      config: config || '{}',
      description,
      status: status || 1,
      created_at: new Date()
    })
    
    res.status(201).json({
      success: true,
      data: newTemplate
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 更新模板
 */
export const updateTemplate = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { id } = req.params
    const { name, config, description, status } = req.body
    
    if (!id) {
      throw new AppError('模板ID不能为空', 400)
    }
    
    // 检查模板是否存在
    const template = await Template.findByPk(parseInt(id, 10))
    if (!template) {
      throw new AppError('模板不存在', 404)
    }
    
    // 更新模板数据
    const updateData: any = {
      modified_at: new Date()
    }
    
    if (name) updateData.name = name
    if (config !== undefined) updateData.config = config
    if (description !== undefined) updateData.description = description
    if (status !== undefined) updateData.status = status
    
    await template.update(updateData)
    
    // 重新获取最新数据
    const updatedTemplate = await Template.findByPk(parseInt(id, 10))
    
    res.status(200).json({
      success: true,
      data: updatedTemplate!
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 删除模板
 */
export const deleteTemplate = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { id } = req.params
    
    if (!id) {
      throw new AppError('模板ID不能为空', 400)
    }
    
    // 检查模板是否存在
    const template = await Template.findByPk(parseInt(id, 10))
    if (!template) {
      throw new AppError('模板不存在', 404)
    }
    
    // 删除模板
    await template.destroy()
    
    res.status(200).json({
      success: true,
      message: '删除模板成功'
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 设置模板状态
 */
export const setTemplateStatus = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { id } = req.params
    const { status } = req.body
    
    if (!id) {
      throw new AppError('模板ID不能为空', 400)
    }
    
    if (status === undefined || (status !== 0 && status !== 1)) {
      throw new AppError('状态值无效，必须为0或1', 400)
    }
    
    // 检查模板是否存在
    const template = await Template.findByPk(parseInt(id, 10))
    if (!template) {
      throw new AppError('模板不存在', 404)
    }
    
    // 更新状态
    await template.update({
      status,
      modified_at: new Date()
    })
    
    res.status(200).json({
      success: true,
      message: `模板${status === 1 ? '启用' : '禁用'}成功`
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 获取模板统计信息
 */
export const getTemplateStats = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // 获取总数
    const total = await Template.count()
    
    // 获取启用的模板数量
    const enabledCount = await Template.count({
      where: { status: 1 }
    })
    
    // 获取禁用的模板数量
    const disabledCount = await Template.count({
      where: { status: 0 }
    })
    
    res.status(200).json({
      success: true,
      data: {
        total,
        enabled: enabledCount,
        disabled: disabledCount
      }
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 复制模板
 */
export const copyTemplate = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { id } = req.params
    const { name } = req.body

    if (!id) {
      throw new AppError('模板ID不能为空', 400)
    }

    if (!name || !name.trim()) {
      throw new AppError('新模板名称不能为空', 400)
    }

    // 检查原模板是否存在
    const originalTemplate = await Template.findByPk(parseInt(id, 10))
    if (!originalTemplate) {
      throw new AppError('原模板不存在', 404)
    }

    // 检查新模板名称是否已存在
    const existingTemplate = await Template.findOne({
      where: { name: name.trim() }
    })
    if (existingTemplate) {
      throw new AppError('模板名称已存在', 400)
    }

    // 创建新模板（复制原模板的配置，但不复制绑定关系）
    const newTemplate = await Template.create({
      name: name.trim(),
      config: originalTemplate.config, // 复制配置
      description: originalTemplate.description ? `${originalTemplate.description} (复制)` : '复制的模板',
      status: 1, // 新模板默认启用
      created_at: new Date()
    })

    res.status(201).json({
      success: true,
      data: newTemplate,
      message: '模板复制成功'
    })
  } catch (error) {
    next(error)
  }
}
import { Response, NextFunction, Request } from 'express'
import { File, Device } from '../models'
import { AppError } from '../middleware/error'
import { Op, QueryTypes } from 'sequelize'
import sequelize from '../config/database'
import { getSecureDeviceTableName, validateDeviceTable } from '../models/data'
import { validateDeviceSerialNumber } from '../middleware/validation'

/**
 * 获取文件和设备统计数据
 */
export const getStats = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    // 获取设备总数
    const deviceCount = await Device.count()

    // 获取数据总行数
    const dataRows = (await File.sum('data_rows')) || 0

    // 获取清洗完成数量
    const cleanedCount = await File.count({
      where: {
        is_decrypted: true
      }
    })

    // 获取文件总大小
    const totalSize = (await File.sum('file_size')) || 0

    // 格式化文件大小
    let formattedSize = '0 B'
    if (totalSize) {
      if (totalSize < 1024) {
        formattedSize = totalSize + ' B'
      } else if (totalSize < 1024 * 1024) {
        formattedSize = (totalSize / 1024).toFixed(2) + ' KB'
      } else if (totalSize < 1024 * 1024 * 1024) {
        formattedSize = (totalSize / (1024 * 1024)).toFixed(2) + ' MB'
      } else {
        formattedSize = (totalSize / (1024 * 1024 * 1024)).toFixed(2) + ' GB'
      }
    }

    res.status(200).json({
      success: true,
      data: {
        deviceCount,
        dataRows,
        cleanedCount,
        totalSize,
        formattedSize
      }
    })
  } catch (error) {
    console.error('获取统计数据失败:', error)
    next(error)
  }
}

/**
 * 获取文件列表
 */
export const getFileList = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const {
      deviceSn,
      fileName,
      projectName,
      tunnelSectionNo,
      holeNo,
      startDate,
      endDate,
      page = '1',
      pageSize = '10'
    } = req.query as any

    // 构建查询条件
    const where: any = {}

    // 根据设备序列号过滤
    if (deviceSn) {
      where.device_sn = { [Op.like]: `%${deviceSn}%` }
    }

    // 根据文件名过滤
    if (fileName) {
      where.file_name = { [Op.like]: `%${fileName}%` }
    }

    // 根据项目名称过滤
    if (projectName) {
      where.project_name = projectName
    }

    // 根据隧道段标识码过滤
    if (tunnelSectionNo) {
      where.tunnel_section_no = tunnelSectionNo
    }

    // 根据孔号过滤
    if (holeNo) {
      where.hole_no = holeNo
    }

    // 根据时间范围过滤
    if (startDate && endDate) {
      where.created_at = {
        [Op.between]: [new Date(startDate), new Date(endDate)]
      }
    } else if (startDate) {
      where.created_at = {
        [Op.gte]: new Date(startDate)
      }
    } else if (endDate) {
      where.created_at = {
        [Op.lte]: new Date(endDate)
      }
    }

    // 分页设置
    const pageNumber = parseInt(page)
    const limit = parseInt(pageSize)
    const offset = (pageNumber - 1) * limit

    // 查询文件列表
    const { count, rows } = await File.findAndCountAll({
      where,
      limit,
      offset,
      order: [['modified_at', 'DESC']]
    })

    res.status(200).json({
      success: true,
      data: {
        total: count,
        list: rows
      }
    })
  } catch (error) {
    console.error('获取文件列表失败:', error)
    next(error)
  }
}

/**
 * 获取文件详情
 */
export const getFileDetail = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { id } = req.params

    const file = await File.findByPk(id)

    if (!file) {
      throw new AppError('文件不存在', 404)
    }

    res.status(200).json({
      success: true,
      data: file
    })
  } catch (error) {
    console.error('获取文件详情失败:', error)
    next(error)
  }
}

/**
 * 上传文件
 */
export const uploadFile = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // 文件上传逻辑将在实际实现中处理
    // 这里仅创建文件记录
    const fileData = req.body

    // 创建文件记录
    const file = await File.create({
      ...fileData,
      created_at: new Date()
    })

    res.status(201).json({
      success: true,
      message: '文件上传成功',
      data: file
    })
  } catch (error) {
    console.error('文件上传失败:', error)
    next(error)
  }
}

/**
 * 更新文件信息
 */
export const updateFile = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { id } = req.params
    const fileData = req.body

    const file = await File.findByPk(id)

    if (!file) {
      throw new AppError('文件不存在', 404)
    }

    // 更新文件记录
    await file.update({
      ...fileData,
      modified_at: new Date()
    })

    res.status(200).json({
      success: true,
      message: '文件信息更新成功',
      data: file
    })
  } catch (error) {
    console.error('更新文件信息失败:', error)
    next(error)
  }
}

/**
 * 删除文件
 */
export const deleteFile = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { id } = req.params

    const file = await File.findByPk(id)

    if (!file) {
      throw new AppError('文件不存在', 404)
    }

    // 删除文件记录
    await file.destroy()

    res.status(200).json({
      success: true,
      message: '文件删除成功'
    })
  } catch (error) {
    console.error('删除文件失败:', error)
    next(error)
  }
}

/**
 * 获取设备文件列表
 */
export const getDeviceFiles = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { deviceId } = req.params

    // 获取设备序列号
    const device = await Device.findByPk(deviceId)
    if (!device) {
      throw new AppError('设备不存在', 404)
    }

    // 查询设备对应的文件列表
    const files = await File.findAll({
      where: {
        device_sn: device.device_sn
      },
      order: [['created_at', 'DESC']]
    })

    // 格式化返回数据
    const fileList = files.map(file => ({
      id: file.id.toString(),
      name: file.file_name
    }))

    res.status(200).json({
      success: true,
      data: fileList
    })
  } catch (error) {
    console.error('获取设备文件列表失败:', error)
    next(error)
  }
}

/**
 * 获取文件原始数据
 */
export const getFileOriginalData = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { fileId } = req.params

    // 1. 从dh_file表中查询文件信息，获取device_sn、first_data_time、last_data_time
    const file = await File.findByPk(fileId)
    if (!file) {
      throw new AppError('文件不存在', 404)
    }

    const { device_sn, first_data_time, last_data_time } = file

    if (!device_sn || !first_data_time || !last_data_time) {
      throw new AppError('文件缺少必要信息', 400)
    }

    // 2. 安全验证设备序列号
    if (!validateDeviceSerialNumber(device_sn)) {
      throw new AppError('无效的设备序列号格式', 400)
    }

    // 3. 获取安全的表名
    const tableName = getSecureDeviceTableName(device_sn)

    // 4. 验证表是否存在
    const tableExists = await validateDeviceTable(device_sn)
    if (!tableExists) {
      throw new AppError(`设备数据表不存在: ${device_sn}`, 404)
    }

    // 5. 使用安全的参数化查询（表名已经过验证，可以安全拼接）
    const query = `
      SELECT * FROM ${tableName}
      WHERE collection_at BETWEEN :firstDataTime AND :lastDataTime
      ORDER BY collection_at ASC
    `

    const originalData = await sequelize.query(query, {
      replacements: {
        firstDataTime: first_data_time,
        lastDataTime: last_data_time
      },
      type: QueryTypes.SELECT
    })

    res.status(200).json({
      success: true,
      data: originalData
    })
  } catch (error) {
    console.error('获取文件原始数据失败:', error)
    next(error)
  }
}

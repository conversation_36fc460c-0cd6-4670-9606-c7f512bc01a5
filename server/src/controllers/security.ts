import { Request, Response, NextFunction } from 'express'
import { AppError } from '../middleware/error'
import { sqlSecurityMonitor, SqlSecurityEventType } from '../middleware/sqlSecurityMonitor'
import fs from 'fs'
import path from 'path'

/**
 * 获取SQL安全统计信息
 */
export const getSecurityStats = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const stats = sqlSecurityMonitor.getSecurityStats()
    
    // 获取今天的日志文件大小
    const today = new Date().toISOString().split('T')[0]
    const logFile = path.join(stats.logDirectory, `sql-security-${today}.log`)
    
    let logFileSize = 0
    let logFileExists = false
    
    try {
      if (fs.existsSync(logFile)) {
        const fileStats = fs.statSync(logFile)
        logFileSize = fileStats.size
        logFileExists = true
      }
    } catch (error) {
      console.warn('无法读取日志文件统计:', error)
    }

    res.json({
      success: true,
      message: '获取安全统计成功',
      data: {
        ...stats,
        logFile: {
          exists: logFileExists,
          size: logFileSize,
          sizeFormatted: formatFileSize(logFileSize),
          path: logFile
        },
        timestamp: new Date().toISOString()
      }
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 获取安全事件日志
 */
export const getSecurityLogs = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { date, limit = 100, severity, type } = req.query
    
    // 验证参数
    const limitNum = parseInt(limit as string, 10)
    if (isNaN(limitNum) || limitNum < 1 || limitNum > 1000) {
      throw new AppError('limit参数必须是1-1000之间的数字', 400)
    }

    // 确定日志文件
    const targetDate = date ? String(date) : new Date().toISOString().split('T')[0]
    
    // 验证日期格式
    if (!/^\d{4}-\d{2}-\d{2}$/.test(targetDate)) {
      throw new AppError('日期格式必须为YYYY-MM-DD', 400)
    }

    const stats = sqlSecurityMonitor.getSecurityStats()
    const logFile = path.join(stats.logDirectory, `sql-security-${targetDate}.log`)

    if (!fs.existsSync(logFile)) {
      res.json({
        success: true,
        message: '指定日期没有安全日志',
        data: {
          events: [],
          total: 0,
          date: targetDate,
          filters: { severity, type, limit: limitNum }
        }
      })
      return
    }

    // 读取日志文件
    const logContent = fs.readFileSync(logFile, 'utf8')
    const lines = logContent.trim().split('\n').filter(line => line.trim())
    
    // 解析事件
    const events = []
    for (const line of lines) {
      try {
        const event = JSON.parse(line)
        
        // 应用过滤器
        if (severity && event.severity !== severity) continue
        if (type && event.type !== type) continue
        
        events.push(event)
      } catch (parseError) {
        console.warn('解析日志行失败:', parseError)
      }
    }

    // 按时间倒序排列并限制数量
    events.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
    const limitedEvents = events.slice(0, limitNum)

    res.json({
      success: true,
      message: '获取安全日志成功',
      data: {
        events: limitedEvents,
        total: events.length,
        returned: limitedEvents.length,
        date: targetDate,
        filters: { severity, type, limit: limitNum }
      }
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 获取安全事件统计分析
 */
export const getSecurityAnalytics = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { days = 7 } = req.query
    const daysNum = parseInt(days as string, 10)
    
    if (isNaN(daysNum) || daysNum < 1 || daysNum > 30) {
      throw new AppError('days参数必须是1-30之间的数字', 400)
    }

    const stats = sqlSecurityMonitor.getSecurityStats()
    const analytics = {
      totalEvents: 0,
      eventsByType: {} as Record<string, number>,
      eventsBySeverity: {} as Record<string, number>,
      eventsByDay: [] as Array<{ date: string; count: number }>,
      topIPs: [] as Array<{ ip: string; count: number }>,
      recentCriticalEvents: [] as any[]
    }

    // 分析最近几天的日志
    for (let i = 0; i < daysNum; i++) {
      const date = new Date()
      date.setDate(date.getDate() - i)
      const dateStr = date.toISOString().split('T')[0]
      
      const logFile = path.join(stats.logDirectory, `sql-security-${dateStr}.log`)
      
      let dayCount = 0
      const ipCounts = new Map<string, number>()
      
      if (fs.existsSync(logFile)) {
        try {
          const logContent = fs.readFileSync(logFile, 'utf8')
          const lines = logContent.trim().split('\n').filter(line => line.trim())
          
          for (const line of lines) {
            try {
              const event = JSON.parse(line)
              
              dayCount++
              analytics.totalEvents++
              
              // 按类型统计
              analytics.eventsByType[event.type] = (analytics.eventsByType[event.type] || 0) + 1
              
              // 按严重程度统计
              analytics.eventsBySeverity[event.severity] = (analytics.eventsBySeverity[event.severity] || 0) + 1
              
              // IP统计
              ipCounts.set(event.ip, (ipCounts.get(event.ip) || 0) + 1)
              
              // 收集关键事件
              if (event.severity === 'CRITICAL' && analytics.recentCriticalEvents.length < 10) {
                analytics.recentCriticalEvents.push(event)
              }
            } catch (parseError) {
              // 忽略解析错误
            }
          }
        } catch (readError) {
          console.warn(`读取日志文件失败: ${logFile}`, readError)
        }
      }
      
      analytics.eventsByDay.push({
        date: dateStr,
        count: dayCount
      })
      
      // 合并IP统计
      for (const [ip, count] of ipCounts.entries()) {
        const existing = analytics.topIPs.find(item => item.ip === ip)
        if (existing) {
          existing.count += count
        } else {
          analytics.topIPs.push({ ip, count })
        }
      }
    }

    // 排序和限制
    analytics.eventsByDay.reverse() // 按日期正序
    analytics.topIPs.sort((a, b) => b.count - a.count).splice(10) // 只保留前10个IP
    analytics.recentCriticalEvents.sort((a, b) => 
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    )

    res.json({
      success: true,
      message: '获取安全分析成功',
      data: {
        ...analytics,
        period: {
          days: daysNum,
          startDate: analytics.eventsByDay[0]?.date,
          endDate: analytics.eventsByDay[analytics.eventsByDay.length - 1]?.date
        },
        timestamp: new Date().toISOString()
      }
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 手动触发安全事件（测试用）
 */
export const triggerTestSecurityEvent = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    if (process.env.NODE_ENV === 'production') {
      throw new AppError('生产环境不允许触发测试事件', 403)
    }

    const { type = 'SQL_INJECTION_ATTEMPT', severity = 'MEDIUM' } = req.body

    // 验证参数
    if (!Object.values(SqlSecurityEventType).includes(type)) {
      throw new AppError('无效的事件类型', 400)
    }

    if (!['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'].includes(severity)) {
      throw new AppError('无效的严重程度', 400)
    }

    // 记录测试事件
    sqlSecurityMonitor.logSecurityEvent(
      type,
      severity,
      req,
      {
        violations: ['这是一个测试事件'],
        requestData: req.body
      },
      'LOGGED'
    )

    res.json({
      success: true,
      message: '测试安全事件已记录',
      data: {
        type,
        severity,
        timestamp: new Date().toISOString()
      }
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 格式化文件大小
 */
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

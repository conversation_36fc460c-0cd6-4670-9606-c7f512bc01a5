import { Response, NextFunction, Request } from 'express'
import { AppError } from '../middleware/error'
import { DeviceChartComponentConfig, ChartComponent, Device } from '../models'
import { Op } from 'sequelize'

// 定义查询参数接口
interface DeviceChartConfigQueryParams {
  deviceId?: string
  chartComponentId?: string
  limit?: string
  offset?: string
}

/**
 * 获取所有设备图表组件配置
 */
export const getAllDeviceChartConfigs = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { 
      deviceId, 
      chartComponentId,
      limit = '10',
      offset = '0'
    } = req.query as DeviceChartConfigQueryParams
    
    // 构建查询条件
    const conditions: any = {}
    if (deviceId) conditions.device_id = parseInt(deviceId, 10)
    if (chartComponentId) conditions.chart_component_id = parseInt(chartComponentId, 10)

    // 分页参数
    const limitNum = parseInt(limit, 10)
    const offsetNum = parseInt(offset, 10)
    
    // 查询数据
    const { count, rows } = await DeviceChartComponentConfig.findAndCountAll({
      where: conditions,
      limit: limitNum,
      offset: offsetNum,
      order: [['id', 'ASC']],
      include: [
        {
          model: ChartComponent,
          as: 'chartComponent'
        }
      ]
    })
    
    res.status(200).json({
      success: true,
      data: {
        total: count,
        list: rows
      }
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 根据ID获取设备图表组件配置
 */
export const getDeviceChartConfigById = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { id } = req.params
    if (!id) {
      throw new AppError('配置ID不能为空', 400)
    }
    
    const config = await DeviceChartComponentConfig.findByPk(parseInt(id, 10), {
      include: [
        {
          model: ChartComponent,
          as: 'chartComponent'
        }
      ]
    })
    
    if (!config) {
      throw new AppError('设备图表配置不存在', 404)
    }
    
    res.status(200).json({
      success: true,
      data: config
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 创建设备图表组件配置
 */
export const createDeviceChartConfig = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { deviceId, chartComponentId } = req.body
    
    if (!deviceId || !chartComponentId) {
      throw new AppError('设备ID和图表组件ID不能为空', 400)
    }
    
    // 检查设备是否存在
    const device = await Device.findByPk(parseInt(deviceId, 10))
    if (!device) {
      throw new AppError('设备不存在', 404)
    }
    
    // 检查图表组件是否存在
    const chart = await ChartComponent.findByPk(parseInt(chartComponentId, 10))
    if (!chart) {
      throw new AppError('图表组件不存在', 404)
    }
    
    // 检查是否已存在相同的配置
    const existConfig = await DeviceChartComponentConfig.findOne({
      where: {
        device_id: parseInt(deviceId, 10),
        chart_component_id: parseInt(chartComponentId, 10)
      }
    })
    
    if (existConfig) {
      throw new AppError('该设备已配置此图表组件', 400)
    }
    
    // 创建设备图表组件配置
    const newConfig = await DeviceChartComponentConfig.create({
      device_id: parseInt(deviceId, 10),
      chart_component_id: parseInt(chartComponentId, 10),
      created_at: new Date()
    })
    
    res.status(201).json({
      success: true,
      data: newConfig
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 更新设备图表组件配置
 */
export const updateDeviceChartConfig = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { id } = req.params
    const { deviceId, chartComponentId } = req.body
    
    if (!id) {
      throw new AppError('配置ID不能为空', 400)
    }
    
    // 检查配置是否存在
    const config = await DeviceChartComponentConfig.findByPk(parseInt(id, 10))
    if (!config) {
      throw new AppError('设备图表配置不存在', 404)
    }
    
    // 更新配置数据
    const updateData: any = {
      modified_at: new Date()
    }
    
    if (deviceId) {
      // 检查设备是否存在
      const device = await Device.findByPk(parseInt(deviceId, 10))
      if (!device) {
        throw new AppError('设备不存在', 404)
      }
      updateData.device_id = parseInt(deviceId, 10)
    }
    
    if (chartComponentId) {
      // 检查图表组件是否存在
      const chart = await ChartComponent.findByPk(parseInt(chartComponentId, 10))
      if (!chart) {
        throw new AppError('图表组件不存在', 404)
      }
      updateData.chart_component_id = parseInt(chartComponentId, 10)
    }
    
    // 如果同时更新了设备ID和图表组件ID，需要检查是否存在重复配置
    if (deviceId && chartComponentId) {
      const existConfig = await DeviceChartComponentConfig.findOne({
        where: {
          device_id: parseInt(deviceId, 10),
          chart_component_id: parseInt(chartComponentId, 10)
        }
      })
      
      if (existConfig && existConfig.id !== parseInt(id, 10)) {
        throw new AppError('该设备已配置此图表组件', 400)
      }
    }
    
    await config.update(updateData)
    
    // 重新获取最新数据
    const updatedConfig = await DeviceChartComponentConfig.findByPk(parseInt(id, 10), {
      include: [
        {
          model: ChartComponent,
          as: 'chartComponent'
        }
      ]
    })
    
    res.status(200).json({
      success: true,
      data: updatedConfig!
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 删除设备图表组件配置
 */
export const deleteDeviceChartConfig = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { id } = req.params
    
    if (!id) {
      throw new AppError('配置ID不能为空', 400)
    }
    
    // 检查配置是否存在
    const config = await DeviceChartComponentConfig.findByPk(parseInt(id, 10))
    if (!config) {
      throw new AppError('设备图表配置不存在', 404)
    }
    
    // 删除配置
    await config.destroy()
    
    res.status(200).json({
      success: true,
      message: '删除设备图表组件配置成功'
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 获取设备的所有图表组件配置
 */
export const getDeviceChartConfigs = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { deviceId } = req.params
    
    if (!deviceId) {
      throw new AppError('设备ID不能为空', 400)
    }
    
    // 检查设备是否存在
    const device = await Device.findByPk(parseInt(deviceId, 10))
    if (!device) {
      throw new AppError('设备不存在', 404)
    }
    
    // 获取设备的图表配置
    let configs = await DeviceChartComponentConfig.findAll({
      where: { device_id: parseInt(deviceId, 10) },
      include: [
        {
          model: ChartComponent,
          as: 'chartComponent'
        }
      ],
      order: [['id', 'ASC']]
    })

    // 如果设备没有任何图表配置，则创建默认配置
    if (configs.length === 0) {
      await createDefaultChartConfigs(parseInt(deviceId, 10))
      
      // 重新获取配置
      configs = await DeviceChartComponentConfig.findAll({
        where: { device_id: parseInt(deviceId, 10) },
        include: [
          {
            model: ChartComponent,
            as: 'chartComponent'
          }
        ],
        order: [['id', 'ASC']]
      })
    }
    
    res.status(200).json({
      success: true,
      data: configs
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 创建设备的默认图表配置
 * @param deviceId 设备ID
 */
const createDefaultChartConfigs = async (deviceId: number): Promise<void> => {
  try {
    // 获取所有启用状态的图表组件
    const availableCharts = await ChartComponent.findAll({
      where: { status: 1 },
      order: [['id', 'ASC']]
    })
    
    if (availableCharts.length === 0) {
      return
    }
    
    // 为设备创建默认图表配置
    for (const chart of availableCharts) {
      try {
        // 检查是否已存在配置，避免重复创建
        const existConfig = await DeviceChartComponentConfig.findOne({
          where: {
            device_id: deviceId,
            chart_component_id: chart.id
          }
        })
        
        if (!existConfig) {
          await DeviceChartComponentConfig.create({
            device_id: deviceId,
            chart_component_id: chart.id,
            created_at: new Date()
          })
        }
      } catch (err) {
        console.error(`为设备创建图表配置失败，设备ID: ${deviceId}，图表ID: ${chart.id}`, err)
        // 失败时继续下一个，不中断整个过程
      }
    }
  } catch (error) {
    console.error(`创建设备默认图表配置失败，设备ID: ${deviceId}`, error)
  }
}

/**
 * 获取设备的图表组件配置状态
 * 返回所有可用的图表组件，同时标记在当前设备上的启用状态
 */
export const getDeviceChartConfigStatus = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { deviceId } = req.params
    
    if (!deviceId) {
      throw new AppError('设备ID不能为空', 400)
    }
    
    // 检查设备是否存在
    const device = await Device.findByPk(parseInt(deviceId, 10))
    if (!device) {
      throw new AppError('设备不存在', 404)
    }
    
    // 1. 获取所有启用状态的图表组件
    const allCharts = await ChartComponent.findAll({
      where: { status: 1 },
      order: [['id', 'ASC']]
    })
    
    // 2. 获取设备已配置的图表组件
    let deviceConfigs = await DeviceChartComponentConfig.findAll({
      where: { device_id: parseInt(deviceId, 10) }
    })
    
    // 如果设备没有任何图表配置，则创建默认配置
    if (deviceConfigs.length === 0) {
      await createDefaultChartConfigs(parseInt(deviceId, 10))
      
      // 重新获取配置
      deviceConfigs = await DeviceChartComponentConfig.findAll({
        where: { device_id: parseInt(deviceId, 10) }
      })
    }
    
    // 3. 标记每个图表组件的启用状态
    const chartsWithStatus = allCharts.map(chart => {
      // 检查是否已配置
      const config = deviceConfigs.find(c => c.chart_component_id === chart.id)
      
      // 返回带有状态的图表组件
      return {
        ...chart.toJSON(),
        enabled: !!config,
        configId: config ? config.id : null
      }
    })
    
    res.status(200).json({
      success: true,
      data: {
        total: chartsWithStatus.length,
        list: chartsWithStatus
      }
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 获取设备已启用的图表组件（专用于设备详情页）
 * 只返回当前设备已启用的图表组件，不包含未启用的
 */
export const getDeviceEnabledCharts = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { deviceId } = req.params
    
    if (!deviceId) {
      throw new AppError('设备ID不能为空', 400)
    }
    
    // 检查设备是否存在
    const device = await Device.findByPk(parseInt(deviceId, 10))
    if (!device) {
      throw new AppError('设备不存在', 404)
    }
    
    // 获取设备已启用的图表组件配置
    const configs = await DeviceChartComponentConfig.findAll({
      where: { device_id: parseInt(deviceId, 10) },
      include: [
        {
          model: ChartComponent,
          as: 'chartComponent',
          where: { status: 1 } // 只包含状态为启用的图表组件
        }
      ],
      order: [['id', 'ASC']]
    })
    
    // 转换结果格式
    const enabledCharts = configs.map(config => {
      const chartComponent = config.get('chartComponent') as any
      return {
        id: chartComponent.id,
        name: chartComponent.name,
        type: chartComponent.type,
        component: chartComponent.component,
        description: chartComponent.description,
        thumbnail: chartComponent.thumbnail,
        configId: config.id,
        enabled: true // 由于我们只查询了已启用的，所以这里设为true
      }
    })
    
    res.status(200).json({
      success: true,
      data: {
        total: enabledCharts.length,
        list: enabledCharts
      }
    })
  } catch (error) {
    next(error)
  }
} 
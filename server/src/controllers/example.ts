import { Request, Response } from 'express'
import { AppError } from '../middleware/error'

// 模拟数据
let examples = [
  { id: 1, name: '示例1' },
  { id: 2, name: '示例2' }
]

export const getExamples = (req: Request, res: Response) => {
  res.json(examples)
}

export const createExample = (req: Request, res: Response) => {
  const { name } = req.body
  if (!name) {
    throw new AppError('名称不能为空', 400)
  }

  const newExample = {
    id: examples.length + 1,
    name
  }
  examples.push(newExample)

  res.status(201).json(newExample)
}

export const updateExample = (req: Request, res: Response) => {
  const { id } = req.params
  const { name } = req.body

  const example = examples.find(e => e.id === parseInt(id))
  if (!example) {
    throw new AppError('示例不存在', 404)
  }

  if (!name) {
    throw new AppError('名称不能为空', 400)
  }

  example.name = name
  res.json(example)
}

export const deleteExample = (req: Request, res: Response) => {
  const { id } = req.params

  const index = examples.findIndex(e => e.id === parseInt(id))
  if (index === -1) {
    throw new AppError('示例不存在', 404)
  }

  examples = examples.filter(e => e.id !== parseInt(id))
  res.status(204).send()
}

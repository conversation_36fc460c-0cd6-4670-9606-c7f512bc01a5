import jwt from 'jsonwebtoken'
import { Response, NextFunction } from 'express'
import axios from 'axios'
import crypto from 'crypto'
import {
  LoginRequest,
  LoginResponse,
  LogoutRequest,
  LogoutResponse,
  GetCurrentUserRequest,
  GetCurrentUserResponse,
  UpdateProfileResponse,
  DingTalkLoginRequest
} from '../types/controllers'
import config from '../config'
import { AppError } from '../middleware/error'
import { User } from '../models'
import sequelize from '../config/database'
import { tokenBlacklist } from '../utils/tokenBlacklist'

/**
 * 生成访问令牌
 */
function generateAccessToken(user: any): string {
  return jwt.sign(
    {
      id: String(user.id),
      username: user.account,
      role: user.role,
      iat: Math.floor(Date.now() / 1000),
      jti: crypto.randomUUID() // 添加唯一标识符
    },
    config.jwtSecret,
    {
      expiresIn: '2h', // 缩短到2小时
      algorithm: 'HS256',
      issuer: 'web-panel',
      audience: 'web-panel-client'
    }
  )
}

/**
 * 生成刷新令牌
 */
function generateRefreshToken(user: any): string {
  return jwt.sign(
    {
      id: String(user.id),
      username: user.account,
      type: 'refresh',
      iat: Math.floor(Date.now() / 1000),
      jti: crypto.randomUUID()
    },
    config.jwtRefreshSecret || config.jwtSecret, // 使用不同的密钥
    {
      expiresIn: '7d', // 7天
      algorithm: 'HS256',
      issuer: 'web-panel',
      audience: 'web-panel-client'
    }
  )
}

/**
 * 登录接口（优化：事务操作，确保token和响应一致性）
 */
export const login = async (
  req: LoginRequest,
  res: Response<LoginResponse | { message: string }>,
  next: NextFunction
): Promise<void> => {
  try {
    const { username, password } = req.body
    if (!username || !password) {
      throw new AppError('用户名和密码不能为空', 400)
    }
    const user = await User.findOne({ where: { account: username } })
    if (!user) {
      throw new AppError('用户名错误或用户不存在', 401)
    }
    const isPasswordValid = await user.comparePassword(password)
    if (!isPasswordValid) {
      throw new AppError('密码错误', 401)
    }
    // 事务操作
    const result = await sequelize.transaction(async t => {
      // 生成访问令牌和刷新令牌
      const accessToken = generateAccessToken(user)
      const refreshToken = generateRefreshToken(user)

      // 将旧的token加入黑名单（如果存在）
      if (user.token) {
        tokenBlacklist.addToBlacklist(user.token, 'new_login')
      }

      await User.update({ token: accessToken }, { where: { id: user.id }, transaction: t })
      return {
        message: '登录成功',
        user: {
          id: String(user.id),
          username: user.account,
          name: user.username,
          role: user.role,
          avatar_url: user.avatar_url
        },
        accessToken,
        refreshToken
      }
    })

    // 设置HttpOnly Cookie - 访问令牌
    res.cookie('accessToken', result.accessToken, {
      httpOnly: true,
      secure: config.security.cookieSecure,
      sameSite: config.security.sameSite,
      maxAge: 2 * 60 * 60 * 1000, // 2小时
      path: '/'
    })

    // 设置HttpOnly Cookie - 刷新令牌
    res.cookie('refreshToken', result.refreshToken, {
      httpOnly: true,
      secure: config.security.cookieSecure,
      sameSite: config.security.sameSite,
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7天
      path: '/'
    })

    // 不在响应中返回token，只返回用户信息
    const { accessToken, refreshToken, ...responseData } = result
    res.status(200).json(responseData)
  } catch (error) {
    next(error)
  }
}

/**
 * 钉钉登录接口
 */
export const dingTalkLogin = async (
  req: DingTalkLoginRequest,
  res: Response<LoginResponse | { message: string }>,
  next: NextFunction
): Promise<void> => {
  try {
    const { code } = req.body
    if (!code) {
      throw new AppError('授权码不能为空', 400)
    }

    // 第一步：通过code获取accessToken
    const tokenResponse = await axios.post(config.dingtalk.host + config.dingtalk.authToken, {
      clientId: config.dingtalk.clientId,
      clientSecret: config.dingtalk.clientSecret,
      code: code,
      grantType: 'authorization_code'
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    })

    const { accessToken } = tokenResponse.data
    if (!accessToken) {
      throw new AppError('获取钉钉访问令牌失败', 400)
    }

    // 第二步：通过accessToken获取用户信息
    const userResponse = await axios.get(config.dingtalk.host + config.dingtalk.userInfo, {
      headers: {
        'x-acs-dingtalk-access-token': accessToken,
        'Content-Type': 'application/json'
      }
    })

    const dingTalkUser = userResponse.data
    if (!dingTalkUser.openId) {
      throw new AppError('获取钉钉用户信息失败', 400)
    }

    // 事务操作：查找或创建用户并生成token
    const result = await sequelize.transaction(async t => {
      // 先通过钉钉openId查找用户
      let user = await User.findOne({
        where: { dingtalk_open_id: dingTalkUser.openId },
        transaction: t
      })

      if (!user) {
        // 用户不存在，创建新用户
        user = await User.create({
          username: dingTalkUser.nick || '钉钉用户',
          account: dingTalkUser.openId, // 使用openId作为账号
          password: 'dingtalk_user', // 钉钉用户设置默认密码
          role: 'user',
          dingtalk_open_id: dingTalkUser.openId,
          dingtalk_union_id: dingTalkUser.unionId,
          mobile: dingTalkUser.mobile,
          email: dingTalkUser.email,
          avatar_url: dingTalkUser.avatarUrl,
          created_at: new Date(),
          modified_at: new Date()
        }, { transaction: t })
      } else {
        // 用户存在，更新钉钉信息
        await user.update({
          username: dingTalkUser.nick || user.username,
          dingtalk_union_id: dingTalkUser.unionId,
          mobile: dingTalkUser.mobile,
          email: dingTalkUser.email,
          avatar_url: dingTalkUser.avatarUrl,
          modified_at: new Date()
        }, { transaction: t })
      }

      // 生成JWT token
      const token = jwt.sign(
        {
          id: String(user.id),
          username: user.account,
          role: user.role
        },
        config.jwtSecret,
        {
          expiresIn: '24h',
          algorithm: 'HS256',
          issuer: 'web-panel',
          audience: 'web-panel-client'
        }
      )

      // 更新用户token
      await user.update({ token }, { transaction: t })

      return {
        message: '钉钉登录成功',
        user: {
          id: String(user.id),
          username: user.account,
          name: user.username,
          role: user.role,
          avatar_url: user.avatar_url
        },
        token
      }
    })

    // 设置HttpOnly Cookie
    res.cookie('token', result.token, {
      httpOnly: true,
      secure: config.security.cookieSecure, // 通过环境变量控制
      sameSite: config.security.sameSite, // 开发环境使用lax，生产环境使用strict
      maxAge: 24 * 60 * 60 * 1000, // 24小时
      path: '/'
    })

    // 不在响应中返回token，只返回用户信息
    const { token, ...responseData } = result
    res.status(200).json(responseData)
  } catch (error) {
    console.error('钉钉登录失败:', error)
    if (axios.isAxiosError(error)) {
      // 处理axios错误
      const message = error.response?.data?.message || error.message || '钉钉登录失败'
      next(new AppError(message, 400))
    } else {
      next(error)
    }
  }
}

/**
 * 登出接口（优化：清除Cookie和数据库token）
 */
export const logout = async (
  req: LogoutRequest,
  res: Response<LogoutResponse | { message: string }>,
  next: NextFunction
): Promise<void> => {
  try {
    // 从Cookie或Authorization头获取token
    const token = req.cookies?.token || req.headers['authorization']?.split(' ')[1]
    if (token) {
      await User.update({ token: null }, { where: { token } })
    }

    // 清除Cookie
    res.clearCookie('token', {
      httpOnly: true,
      secure: config.security.cookieSecure, // 通过环境变量控制
      sameSite: config.security.sameSite,
      path: '/'
    })

    res.status(200).json({ message: '登出成功' })
  } catch (error) {
    next(error)
  }
}

/**
 * 获取当前用户信息
 */
export const getCurrentUser = async (
  req: GetCurrentUserRequest,
  res: Response<GetCurrentUserResponse | { message: string }>,
  next: NextFunction
): Promise<void> => {
  try {
    if (!req.user) {
      res.status(401).json({ message: '未授权' })
      return
    }

    const { id } = req.user
    // 从数据库中查找用户
    const user = await User.findByPk(id)

    if (!user) {
      res.status(404).json({ message: '用户不存在' })
      return
    }

    res.status(200).json({
      id: String(user.id),
      username: user.account,
      name: user.username,
      role: user.role
    })
  } catch (error) {
    console.error('获取用户信息出错:', error)
    next(error)
  }
}

export const register = async (
  req: LoginRequest,
  res: Response<LoginResponse | { message: string }>,
  next: NextFunction
): Promise<void> => {
  try {
    const { username, password, name = '未命名用户' } = req.body

    // 检查用户名是否已存在
    const existingUser = await User.findOne({ where: { account: username } })
    if (existingUser) {
      throw new AppError('用户名已存在', 400)
    }

    // 创建新用户
    const newUser = await User.create({
      username: name,
      account: username,
      password,
      role: 'user',
      created_at: new Date()
    })

    const token = jwt.sign(
      {
        id: String(newUser.id),
        username: newUser.account,
        role: newUser.role
      },
      config.jwtSecret,
      {
        expiresIn: '24h',
        algorithm: 'HS256',
        issuer: 'web-panel',
        audience: 'web-panel-client'
      }
    )

    // 更新用户token
    await newUser.update({ token })

    // 设置HttpOnly Cookie
    res.cookie('token', token, {
      httpOnly: true,
      secure: config.security.cookieSecure, // 通过环境变量控制
      sameSite: config.security.sameSite, // 通过环境变量控制
      maxAge: 24 * 60 * 60 * 1000, // 24小时
      path: '/'
    })

    res.status(201).json({
      message: '注册成功',
      user: {
        id: String(newUser.id),
        username: newUser.account,
        name: newUser.username,
        role: newUser.role,
        avatar_url: newUser.avatar_url
      }
    })
  } catch (error) {
    console.error('注册出错:', error)
    next(error)
  }
}

export const getProfile = async (
  req: GetCurrentUserRequest,
  res: Response<GetCurrentUserResponse | { message: string }>,
  next: NextFunction
): Promise<void> => {
  try {
    if (!req.user) {
      throw new AppError('未授权', 401)
    }

    const { id } = req.user
    // 从数据库中查找用户
    const user = await User.findByPk(id)
    if (!user) {
      throw new AppError('用户不存在', 404)
    }

    res.json({
      id: String(user.id),
      username: user.account,
      name: user.username,
      role: user.role,
      avatar_url: user.avatar_url
    })
  } catch (error) {
    console.error('获取用户信息出错:', error)
    next(error)
  }
}

export const updateProfile = async (
  req: GetCurrentUserRequest,
  res: Response<UpdateProfileResponse | { message: string }>,
  next: NextFunction
): Promise<void> => {
  try {
    if (!req.user) {
      throw new AppError('未授权', 401)
    }

    const { id } = req.user
    // 从数据库中查找用户
    const user = await User.findByPk(id)
    if (!user) {
      throw new AppError('用户不存在', 404)
    }

    const { name, password } = req.body

    // 更新用户资料
    if (name) {
      user.username = name
    }

    if (password) {
      user.password = password
    }

    // 保存更新后的用户信息
    await user.save()

    res.json({
      message: '更新成功',
      user: {
        id: String(user.id),
        username: user.account,
        name: user.username,
        role: user.role,
        avatar_url: user.avatar_url
      }
    })
  } catch (error) {
    console.error('更新用户资料失败:', error)
    next(error)
  }
}

/**
 * Token刷新接口
 */
export const refreshToken = async (
  req: any,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // 从Cookie中获取刷新令牌
    const refreshTokenFromCookie = req.cookies?.refreshToken

    if (!refreshTokenFromCookie) {
      throw new AppError('刷新令牌不存在', 401)
    }

    // 检查刷新令牌是否在黑名单中
    if (tokenBlacklist.isBlacklisted(refreshTokenFromCookie)) {
      throw new AppError('刷新令牌已失效', 401)
    }

    try {
      // 验证刷新令牌
      const decoded = jwt.verify(
        refreshTokenFromCookie,
        config.jwtRefreshSecret || config.jwtSecret
      ) as any

      // 检查令牌类型
      if (decoded.type !== 'refresh') {
        throw new AppError('无效的刷新令牌', 401)
      }

      // 查找用户
      const user = await User.findByPk(decoded.id)
      if (!user) {
        throw new AppError('用户不存在', 404)
      }

      // 生成新的访问令牌和刷新令牌
      const newAccessToken = generateAccessToken(user)
      const newRefreshToken = generateRefreshToken(user)

      // 将旧的刷新令牌加入黑名单
      tokenBlacklist.addToBlacklist(refreshTokenFromCookie, 'token_refresh')

      // 更新数据库中的token
      await User.update({ token: newAccessToken }, { where: { id: user.id } })

      // 设置新的Cookie
      res.cookie('accessToken', newAccessToken, {
        httpOnly: true,
        secure: config.security.cookieSecure,
        sameSite: config.security.sameSite,
        maxAge: 2 * 60 * 60 * 1000, // 2小时
        path: '/'
      })

      res.cookie('refreshToken', newRefreshToken, {
        httpOnly: true,
        secure: config.security.cookieSecure,
        sameSite: config.security.sameSite,
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7天
        path: '/'
      })

      res.status(200).json({
        message: 'Token刷新成功',
        user: {
          id: String(user.id),
          username: user.account,
          name: user.username,
          role: user.role,
          avatar_url: user.avatar_url
        }
      })

    } catch (jwtError: any) {
      // JWT验证失败
      if (jwtError.name === 'TokenExpiredError') {
        throw new AppError('刷新令牌已过期，请重新登录', 401)
      } else if (jwtError.name === 'JsonWebTokenError') {
        throw new AppError('无效的刷新令牌', 401)
      } else {
        throw jwtError
      }
    }

  } catch (error) {
    console.error('Token刷新失败:', error)
    next(error)
  }
}

/**
 * 增强的退出登录接口
 */
export const enhancedLogout = async (
  req: any,
  res: Response<LogoutResponse | { message: string }>,
  next: NextFunction
): Promise<void> => {
  try {
    // 获取访问令牌和刷新令牌
    const accessToken = req.cookies?.accessToken || req.headers.authorization?.replace('Bearer ', '')
    const refreshToken = req.cookies?.refreshToken

    // 将令牌加入黑名单
    if (accessToken) {
      tokenBlacklist.addToBlacklist(accessToken, 'logout')
    }

    if (refreshToken) {
      tokenBlacklist.addToBlacklist(refreshToken, 'logout')
    }

    // 清除用户数据库中的token
    if (req.user?.id) {
      await User.update({ token: null }, { where: { id: req.user.id } })
    }

    // 清除Cookie
    res.clearCookie('accessToken', { path: '/' })
    res.clearCookie('refreshToken', { path: '/' })
    res.clearCookie('token', { path: '/' }) // 兼容旧版本

    res.status(200).json({ message: '退出登录成功' })
  } catch (error) {
    console.error('退出登录失败:', error)
    next(error)
  }
}
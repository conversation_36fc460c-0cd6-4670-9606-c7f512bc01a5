import { Router } from 'express'
import {
  getDeviceList,
  getDeviceDetail,
  getDeviceListWithAlgorithm,
  updateDevice,
  getDeviceDigitalCore,
  getDeviceDigitalCoreDetail
} from '../controllers/device'
import { verifyToken, autoRefreshToken } from '../middleware/auth'
import { sqlInjectionProtection } from '../middleware/sqlInjectionProtection'
import { asyncHandler } from '../utils/asyncHandler'
import { deviceCacheMiddleware } from '../middleware/cache'
import { deviceCacheInvalidation } from '../middleware/cacheInvalidation'

const router = Router()

// 获取设备列表 - 5分钟缓存
router.get('/',
  sqlInjectionProtection(),
  asyncHandler(autoRefreshToken),
  asyncHandler(verifyToken),
  deviceCacheMiddleware,
  asyncHandler(getDeviceList)
)

// 获取带算法信息的设备列表 - 5分钟缓存
router.get('/with-algorithm',
  asyncHandler(autoRefreshToken),
  async<PERSON>and<PERSON>(verifyToken),
  deviceCacheMiddleware,
  asyncHandler(getDeviceListWithAlgorithm)
)

// 获取设备详情 - 5分钟缓存
router.get('/:id',
  sqlInjectionProtection(),
  asyncHandler(autoRefreshToken),
  asyncHandler(verifyToken),
  deviceCacheMiddleware,
  asyncHandler(getDeviceDetail)
)

// 获取设备数字岩芯信息（严格模式，因为涉及动态表查询）- 5分钟缓存
router.get('/:deviceId/digital-core',
  sqlInjectionProtection({ strictMode: true }),
  asyncHandler(autoRefreshToken),
  asyncHandler(verifyToken),
  deviceCacheMiddleware,
  asyncHandler(getDeviceDigitalCore)
)

// 获取设备数字岩芯详情（严格模式）- 5分钟缓存
router.get('/:deviceId/digital-core/:fileId',
  sqlInjectionProtection({ strictMode: true }),
  asyncHandler(autoRefreshToken),
  asyncHandler(verifyToken),
  deviceCacheMiddleware,
  asyncHandler(getDeviceDigitalCoreDetail)
)

// 更新设备信息 - 更新后清除设备缓存
router.put('/:id',
  asyncHandler(verifyToken),
  deviceCacheInvalidation,
  asyncHandler(updateDevice)
)

export default router

import { Router } from 'express'
import {
  getArithmeticList,
  getArithmeticDetail,
  createArithmetic,
  updateArithmetic,
  deleteArithmetic,
  executeAlgorithmMethod,
  executeMultipleAlgorithmMethods,
  executeChainedAlgorithmMethods,
  getAlgorithmMethods,
  downloadArithmetic,
  getAlgorithmStatus,
  analyzeCodeSecurity
} from '../controllers/arithmetic'
import { verifyToken, autoRefreshToken } from '../middleware/auth'
import { asyncHandler } from '../utils/asyncHandler'
import { algorithmCacheMiddleware } from '../middleware/cache'
import { algorithmCacheInvalidation } from '../middleware/cacheInvalidation'

import { validateArithmeticCreate, validateArithmeticUpdate } from '../middleware/validation'

const router = Router()

// 获取算法列表 - 30分钟缓存
router.get('/',
  asyncHandler(autoRefreshToken),
  asyncHandler(verifyToken),
  algorithmCacheMiddleware,
  asyncHandler(getArithmeticList)
)

// 下载算法 - 放在/:id之前，防止被/:id匹配到
router.get('/download/:id',
  asyncHandler(autoRefreshToken),
  asyncHandler(verifyToken),
  asyncHandler(downloadArithmetic)
)

// 获取算法详情 - 30分钟缓存
router.get('/:id',
  asyncHandler(autoRefreshToken),
  asyncHandler(verifyToken),
  algorithmCacheMiddleware,
  asyncHandler(getArithmeticDetail)
)

// 创建新算法 - 创建后自动清除算法缓存
router.post('/',
  asyncHandler(verifyToken),
  validateArithmeticCreate,
  algorithmCacheInvalidation,
  asyncHandler(createArithmetic)
)

// 更新算法 - 更新后自动清除算法缓存
router.put('/:id',
  asyncHandler(verifyToken),
  validateArithmeticUpdate,
  algorithmCacheInvalidation,
  asyncHandler(updateArithmetic)
)

// 删除算法 - 删除后自动清除算法缓存
router.delete('/:id',
  asyncHandler(verifyToken),
  algorithmCacheInvalidation,
  asyncHandler(deleteArithmetic)
)

// 执行算法方法
router.post('/:id/execute', asyncHandler(verifyToken), asyncHandler(executeAlgorithmMethod))

// 执行多个算法方法
router.post('/:id/execute-batch', asyncHandler(verifyToken), asyncHandler(executeMultipleAlgorithmMethods))

// 链式执行算法方法
router.post('/:id/execute-chain', asyncHandler(verifyToken), asyncHandler(executeChainedAlgorithmMethods))

// 获取算法方法列表 - 30分钟缓存
router.get('/:id/methods',
  asyncHandler(verifyToken),
  algorithmCacheMiddleware,
  asyncHandler(getAlgorithmMethods)
)

// 获取算法运行状态 - 不缓存（实时状态）
router.get('/:id/status',
  asyncHandler(verifyToken),
  asyncHandler(getAlgorithmStatus)
)

// 获取当前加载算法的状态 - 不缓存（实时状态）
router.get('/status',
  asyncHandler(verifyToken),
  asyncHandler(getAlgorithmStatus)
)

// 分析算法代码安全性
router.post('/:id/security-check', asyncHandler(verifyToken), asyncHandler(analyzeCodeSecurity))  // 分析指定算法
router.post('/security-check', asyncHandler(verifyToken), asyncHandler(analyzeCodeSecurity))      // 分析提交的代码

export default router

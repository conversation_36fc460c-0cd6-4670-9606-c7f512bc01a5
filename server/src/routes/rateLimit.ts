import { Router } from 'express'
import { verifyToken, isAdmin } from '../middleware/auth'
import { asyncHandler } from '../utils/asyncHandler'
import {
  getRateLimitStats,
  getRateLimitConfig,
  unbanIP,
  getBannedIPs,
  testRateLimit
} from '../controllers/rateLimit'

const router = Router()

// 速率限制管理路由 - 仅管理员可访问

// 获取速率限制统计信息
router.get('/stats', 
  asyncHandler(verifyToken), 
  asyncHandler(isAdmin), 
  asyncHandler(getRateLimitStats)
)

// 获取速率限制配置
router.get('/config',
  asyncHandler(verifyToken),
  asyncHandler(isAdmin),
  asyncHandler(getRateLimitConfig)
)

// 获取被封禁IP列表
router.get('/banned-ips', 
  asyncHandler(verifyToken), 
  asyncHand<PERSON>(isAdmin), 
  async<PERSON><PERSON><PERSON>(getBannedIPs)
)

// 手动解封IP
router.post('/unban/:ip', 
  asyncHandler(verifyToken), 
  asyncHand<PERSON>(isAdmin), 
  asyncHandler(unbanIP)
)

// 测试速率限制
router.post('/test', 
  asyncHandler(verifyToken), 
  asyncHandler(isAdmin), 
  asyncHandler(testRateLimit)
)



export default router

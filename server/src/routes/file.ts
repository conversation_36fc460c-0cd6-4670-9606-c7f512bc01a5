import { Router } from 'express'
import {
  getFileList,
  getFileDetail,
  uploadFile,
  updateFile,
  deleteFile,
  getStats,
  getDeviceFiles,
  getFileOriginalData
} from '../controllers/file'
import { verifyToken, autoRefreshToken } from '../middleware/auth'
import { sqlInjectionProtection } from '../middleware/sqlInjectionProtection'
import { asyncHandler } from '../utils/asyncHandler'
import { fileCacheMiddleware } from '../middleware/cache'
import { fileCacheInvalidation } from '../middleware/cacheInvalidation'

const router = Router()

// 获取统计数据 - 10分钟缓存
router.get('/stats', asyncHandler(autoRefreshToken), asyncHandler(verifyToken), fileCacheMiddleware, asyncHandler(getStats))

// 获取文件列表 - 10分钟缓存
router.get('/', asyncHandler(autoRefreshToken), asyncHand<PERSON>(verifyToken), fileCacheMiddleware, asyncHandler(getFileList))

// 获取设备文件列表 - 10分钟缓存
router.get('/device/:deviceId', async<PERSON>and<PERSON>(autoRefreshToken), asyncHandler(verifyToken), fileCacheMiddleware, asyncHandler(getDeviceFiles))

// 获取文件原始数据（严格模式，因为涉及动态表查询）
router.get('/:fileId/original-data', sqlInjectionProtection({ strictMode: true }), asyncHandler(autoRefreshToken), asyncHandler(verifyToken), asyncHandler(getFileOriginalData))

// 获取文件详情 - 10分钟缓存
router.get('/:id', asyncHandler(autoRefreshToken), asyncHandler(verifyToken), fileCacheMiddleware, asyncHandler(getFileDetail))

// 上传文件 - 清除文件缓存
router.post('/', asyncHandler(verifyToken), fileCacheInvalidation, asyncHandler(uploadFile))

// 更新文件信息 - 清除文件缓存
router.put('/:id', asyncHandler(verifyToken), fileCacheInvalidation, asyncHandler(updateFile))

// 删除文件 - 清除文件缓存
router.delete('/:id', asyncHandler(verifyToken), fileCacheInvalidation, asyncHandler(deleteFile))

export default router

import { Router } from 'express'
import {
  getAll<PERSON>harts,
  getChartById,
  create<PERSON>hart,
  update<PERSON>hart,
  deleteChart,
  getChartsByType,
  getChartsByProduct,
  setChartStatus,
  getChartStats
} from '../controllers/chartComponent'
import { asyncHandler } from '../utils/asyncHandler'
import { verifyToken } from '../middleware/auth'
import { templateCacheMiddleware } from '../middleware/cache'
import { templateCacheInvalidation } from '../middleware/cacheInvalidation'

const router = Router()

// 获取图表组件列表 - 1小时缓存
router.get('/',
  templateCacheMiddleware,
  asyncHandler(getAllCharts)
)

// 获取图表组件统计信息 - 1小时缓存
router.get('/stats',
  templateCacheMiddleware,
  asyncHandler(getChartStats)
)

// 根据图表类型获取图表组件 - 1小时缓存
router.get('/type',
  templateCacheMiddleware,
  asyncHandler(getChartsByType)
)

// 根据产品类型获取图表组件 - 1小时缓存
router.get('/product',
  templateCacheMiddleware,
  asyncHandler(getChartsByProduct)
)

// 获取图表组件详情 - 1小时缓存
router.get('/:id',
  templateCacheMiddleware,
  asyncHandler(getChartById)
)

// 创建图表组件 - 创建后清除模板缓存
router.post('/',
  asyncHandler(verifyToken),
  templateCacheInvalidation,
  asyncHandler(createChart)
)

// 更新图表组件 - 更新后清除模板缓存
router.put('/:id',
  asyncHandler(verifyToken),
  templateCacheInvalidation,
  asyncHandler(updateChart)
)

// 删除图表组件 - 删除后清除模板缓存
router.delete('/:id',
  asyncHandler(verifyToken),
  templateCacheInvalidation,
  asyncHandler(deleteChart)
)

// 启用/禁用图表组件 - 状态变更后清除模板缓存
router.patch('/:id/status',
  asyncHandler(verifyToken),
  templateCacheInvalidation,
  asyncHandler(setChartStatus)
)

export default router 
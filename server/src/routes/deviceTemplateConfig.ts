import { Router } from 'express'
import {
  getDeviceTemplateConfigs,
  getDeviceEnabledTemplates,
  addDeviceTemplateConfig,
  deleteDeviceTemplateConfig,
  batchConfigDeviceTemplates,
  getTemplateDeviceCount,
  unbindDeviceTemplate,
  getBatchDeviceTemplateConfigs,
  batchBindDevicesToTemplate,
  batchUnbindDevicesFromTemplate
} from '../controllers/deviceTemplateConfig'
import { asyncHandler } from '../utils/asyncHandler'
import { verifyToken } from '../middleware/auth'
import { deviceTemplateCacheInvalidation } from '../middleware/cacheInvalidation'

const router = Router()

// 获取设备的模板配置列表
router.get('/device/:deviceId', asyncHandler(getDeviceTemplateConfigs))

// 获取设备启用的模板列表
router.get('/device/:deviceId/enabled', asyncHandler(getDeviceEnabledTemplates))

// 获取模板绑定的设备数量
router.get('/template/:templateId/count', asyncHandler(getTemplateDeviceCount))

// 批量获取多个设备的模板配置状态
router.post('/batch-configs', asyncHandler(getBatchDeviceTemplateConfigs))

// 批量绑定多个设备到模板 - 清除设备和模板缓存
router.post('/template/:templateId/batch-bind',
  asyncHandler(verifyToken),
  deviceTemplateCacheInvalidation,
  asyncHandler(batchBindDevicesToTemplate)
)

// 批量解绑多个设备与模板的关系 - 清除设备和模板缓存
router.post('/template/:templateId/batch-unbind',
  asyncHandler(verifyToken),
  deviceTemplateCacheInvalidation,
  asyncHandler(batchUnbindDevicesFromTemplate)
)

// 添加设备模板配置 - 清除设备和模板缓存
router.post('/device/:deviceId',
  asyncHandler(verifyToken),
  deviceTemplateCacheInvalidation,
  asyncHandler(addDeviceTemplateConfig)
)

// 批量配置设备模板 - 清除设备和模板缓存
router.post('/device/:deviceId/batch',
  asyncHandler(verifyToken),
  deviceTemplateCacheInvalidation,
  asyncHandler(batchConfigDeviceTemplates)
)

// 解除设备与模板的绑定 - 清除设备和模板缓存
router.delete('/device/:deviceId/template/:templateId',
  asyncHandler(verifyToken),
  deviceTemplateCacheInvalidation,
  asyncHandler(unbindDeviceTemplate)
)

// 删除设备模板配置 - 清除设备和模板缓存
router.delete('/:id',
  asyncHandler(verifyToken),
  deviceTemplateCacheInvalidation,
  asyncHandler(deleteDeviceTemplateConfig)
)

export default router 
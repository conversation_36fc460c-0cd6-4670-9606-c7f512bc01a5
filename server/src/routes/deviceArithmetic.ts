import { Router } from 'express'
import {
  getDeviceArithmetic,
  setDeviceArithmetic,
  deleteDeviceArithmetic,
  batchSetDeviceArithmetic,
  batchDeleteDeviceArithmetic,
  getDeviceArithmeticRecords
} from '../controllers/deviceArithmetic'
import { verifyToken } from '../middleware/auth'
import { asyncHandler } from '../utils/asyncHandler'
import { deviceAlgorithmCacheInvalidation, delayedCacheInvalidationMiddleware } from '../middleware/cacheInvalidation'

const router = Router()

// 创建延迟的设备算法缓存失效中间件（500ms延迟，确保数据库事务完全提交）
const delayedDeviceAlgorithmCacheInvalidation = delayedCacheInvalidationMiddleware(['device', 'algorithm'], 500)

// 批量设置设备关联算法 - 延迟清除设备和算法缓存
router.post('/batch',
  asyncHandler(verifyToken),
  delayedDeviceAlgorithmCacheInvalidation,
  asyncHandler(batchSetDeviceArithmetic)
)

// 批量取消设备算法应用 - 延迟清除设备和算法缓存
router.delete('/batch',
  asyncHandler(verifyToken),
  delayedDeviceAlgorithmCacheInvalidation,
  asyncHandler(batchDeleteDeviceArithmetic)
)

// 获取设备算法设置历史记录
router.get(
  '/records/:deviceId',
  asyncHandler(verifyToken),
  asyncHandler(getDeviceArithmeticRecords)
)

// 获取设备关联的算法
router.get('/:deviceId', asyncHandler(verifyToken), asyncHandler(getDeviceArithmetic))

// 设置设备关联算法 - 延迟清除设备和算法缓存
router.post('/:deviceId',
  asyncHandler(verifyToken),
  delayedDeviceAlgorithmCacheInvalidation,
  asyncHandler(setDeviceArithmetic)
)

// 删除设备关联的算法 - 延迟清除设备和算法缓存
router.delete(
  '/:deviceId/:arithmeticType',
  asyncHandler(verifyToken),
  delayedDeviceAlgorithmCacheInvalidation,
  asyncHandler(deleteDeviceArithmetic)
)

export default router

import { Router } from 'express'
import jwt from 'jsonwebtoken'
import {
  login,
  register,
  getProfile,
  updateProfile,
  logout,
  getCurrentUser,
  dingTalkLogin,
  refreshToken,
  enhancedLogout
} from '../controllers/user'
import { verifyToken, autoRefreshToken } from '../middleware/auth'
import { asyncHandler } from '../utils/asyncHandler'
import { validateLogin, validateRegister } from '../middleware/validation'

const router = Router()

// 认证路由（添加输入验证）
router.post('/login', validateLogin, asyncHandler(login))
router.post('/register', validateRegister, asyncHandler(register))
router.post('/dingtalk-login', asyncHandler(dingTalkLogin))

// 用户信息路由（需要认证）
router.get('/profile', asyncHandler(autoRefreshToken), asyncHandler(verifyToken), asyncHandler(getProfile))
router.put('/profile', asyncHandler(autoRefreshToken), asyncHand<PERSON>(verifyToken), async<PERSON>and<PERSON>(updateProfile))

// Token管理路由
router.post('/refresh-token', asyncHandler(refreshToken)) // Token刷新不需要验证访问令牌
router.post('/logout', asyncHandler(autoRefreshToken), asyncHandler(verifyToken), asyncHandler(enhancedLogout)) // 使用增强的退出登录
router.post('/logout-legacy', asyncHandler(autoRefreshToken), asyncHandler(verifyToken), asyncHandler(logout)) // 保留旧版本兼容

// Token状态检查路由
router.get('/token-status', asyncHandler(async (req: any, res: any) => {
  const accessToken = req.cookies?.accessToken
  const refreshToken = req.cookies?.refreshToken

  if (!accessToken || !refreshToken) {
    return res.status(401).json({
      valid: false,
      needsRefresh: false,
      message: '未找到令牌'
    })
  }

  try {
    const decoded = jwt.decode(accessToken) as any
    if (decoded && decoded.exp) {
      const now = Math.floor(Date.now() / 1000)
      const timeUntilExpiry = decoded.exp - now

      return res.json({
        valid: timeUntilExpiry > 0,
        needsRefresh: timeUntilExpiry < 300, // 5分钟内过期需要刷新
        expiresIn: timeUntilExpiry,
        message: timeUntilExpiry > 0 ? '令牌有效' : '令牌已过期'
      })
    }
  } catch (error) {
    return res.status(400).json({
      valid: false,
      needsRefresh: false,
      message: '令牌格式错误'
    })
  }

  return res.status(400).json({
    valid: false,
    needsRefresh: false,
    message: '无法解析令牌'
  })
}))

// 获取当前用户信息路由 (需要验证令牌)
router.get('/me', asyncHandler(autoRefreshToken), asyncHandler(verifyToken), asyncHandler(getCurrentUser))

export default router

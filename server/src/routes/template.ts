import { Router } from 'express'
import {
  getAllTemplates,
  getTemplateById,
  createTemplate,
  updateTemplate,
  deleteTemplate,
  setTemplateStatus,
  getTemplateStats,
  copyTemplate
} from '../controllers/template'
import { asyncHandler } from '../utils/asyncHandler'
import { verifyToken } from '../middleware/auth'
import { templateCacheMiddleware } from '../middleware/cache'
import { templateCacheInvalidation } from '../middleware/cacheInvalidation'

const router = Router()

// 获取模板列表 - 1小时缓存
router.get('/',
  asyncHandler(verifyToken),
  templateCacheMiddleware,
  asyncHandler(getAllTemplates)
)

// 获取模板统计信息 - 1小时缓存
router.get('/stats',
  asyncHandler(verifyToken),
  templateCacheMiddleware,
  asyncHandler(getTemplateStats)
)

// 获取模板详情 - 1小时缓存
router.get('/:id',
  asyncHandler(verifyToken),
  templateCacheMiddleware,
  asyncHandler(getTemplateById)
)

// 创建模板 - 创建后清除模板缓存
router.post('/',
  asyncHandler(verifyToken),
  templateCacheInvalidation,
  asyncHandler(createTemplate)
)

// 更新模板 - 更新后清除模板缓存
router.put('/:id',
  asyncHandler(verifyToken),
  templateCacheInvalidation,
  asyncHandler(updateTemplate)
)

// 删除模板 - 删除后清除模板缓存
router.delete('/:id',
  asyncHandler(verifyToken),
  templateCacheInvalidation,
  asyncHandler(deleteTemplate)
)

// 启用/禁用模板 - 状态变更后清除模板缓存
router.patch('/:id/status',
  asyncHandler(verifyToken),
  templateCacheInvalidation,
  asyncHandler(setTemplateStatus)
)

// 复制模板 - 复制后清除模板缓存
router.post('/:id/copy',
  asyncHandler(verifyToken),
  templateCacheInvalidation,
  asyncHandler(copyTemplate)
)

export default router
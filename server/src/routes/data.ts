import { Router } from 'express'
import { verifyToken, autoRefreshToken } from '../middleware/auth'
import { sqlInjectionProtection } from '../middleware/sqlInjectionProtection'
import { sqlSecurityMiddleware } from '../middleware/sqlSecurityMonitor'
import { asyncHandler } from '../utils/asyncHandler'
import { validateDataQuery, validateDataQueryWithAlgorithms } from '../middleware/validation'
import { fieldMappingCacheMiddleware } from '../middleware/cache'

// 从controllers/data.ts导入当前实际使用的方法
import {
  getFieldMappings,
  queryData,
  queryDataWithAlgorithms,
  getLatestDeviceData,
  queryDataByDepth
} from '../controllers/data'

const router = Router()

// 使用安全中间件保护数据接口
router.use(sqlSecurityMiddleware) // SQL安全监控
router.use(sqlInjectionProtection({ strictMode: true })) // SQL注入防护（严格模式）
router.use(asyncHandler(autoRefreshToken)) // Token自动刷新
router.use(verifyToken) // 身份验证

// 获取字段映射表 - 1小时缓存
router.get('/field-mappings',
  fieldMappingCacheMiddleware,
  asyncHandler(getFieldMappings)
)

// 统一数据查询接口
router.post('/query', validateDataQuery, asyncHandler(queryData))

// 查询原始数据并自动调用指定算法
router.post('/query-with-algorithms', validateDataQueryWithAlgorithms, asyncHandler(queryDataWithAlgorithms))

// 基于深度范围查询数据
router.post('/query-by-depth', asyncHandler(queryDataByDepth))

// 获取设备最新一条数据
router.get('/latest', asyncHandler(getLatestDeviceData))

export default router 
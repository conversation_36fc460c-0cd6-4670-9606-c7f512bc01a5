import { Router } from 'express'
import { getExamples, createExample, updateExample, deleteExample } from '../controllers/example'
import { asyncHandler } from '../utils/asyncHandler'

const router = Router()

// 获取示例数据
router.get('/', asyncHandler(getExamples))
router.post('/', asyncHandler(createExample))
router.put('/:id', asyncHandler(updateExample))
router.delete('/:id', asyncHandler(deleteExample))

export default router

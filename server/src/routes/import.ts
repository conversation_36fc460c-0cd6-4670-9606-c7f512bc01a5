import { Router } from 'express'
import {
  getImportList,
  getImportDetail,
  createImport,
  updateImport,
  deleteImport,
  getImportDevices,
  checkFileNameExists,
  batchCheckFileNames
} from '../controllers/import'
import { verifyToken } from '../middleware/auth'
import { asyncHandler } from '../utils/asyncHandler'
import { importCacheMiddleware } from '../middleware/cache'
import { importCacheInvalidation } from '../middleware/cacheInvalidation'

const router = Router()

// 导入数据管理路由
// 获取导入数据列表 - 5分钟缓存
router.get('/', asyncHandler(verifyToken), importCacheMiddleware, asyncHandler(getImportList))

// 获取导入数据详情 - 5分钟缓存
router.get('/:id', asyncHandler(verifyToken), importCacheMiddleware, asyncHandler(getImportDetail))

// 获取导入关联的设备列表 - 5分钟缓存
router.get('/:importId/devices', asyncHandler(verifyToken), importCacheMiddleware, asyncHandler(getImportDevices))

// 检查文件名是否存在 - 5分钟缓存
router.get('/check/:fileName', asyncHandler(verifyToken), importCacheMiddleware, asyncHandler(checkFileNameExists))

// 批量检查文件名是否存在 - 5分钟缓存
router.post('/batch-check', asyncHandler(verifyToken), importCacheMiddleware, asyncHandler(batchCheckFileNames))

// 创建导入数据 - 清除导入缓存
router.post('/', asyncHandler(verifyToken), importCacheInvalidation, asyncHandler(createImport))

// 更新导入数据 - 清除导入缓存
router.put('/:id', asyncHandler(verifyToken), importCacheInvalidation, asyncHandler(updateImport))

// 删除导入数据 - 清除导入缓存
router.delete('/:id', asyncHandler(verifyToken), importCacheInvalidation, asyncHandler(deleteImport))

export default router

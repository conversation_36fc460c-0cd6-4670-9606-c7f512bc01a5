import { Router } from 'express'
import { verifyToken, isAdmin } from '../middleware/auth'
import { asyncHandler } from '../utils/asyncHandler'
import {
  getSecurityStats,
  getSecurityLogs,
  getSecurityAnalytics,
  triggerTestSecurityEvent
} from '../controllers/security'

const router = Router()

// 安全管理路由 - 仅管理员可访问

// 获取安全统计信息
router.get('/stats', 
  async<PERSON>and<PERSON>(verifyToken), 
  asyncHandler(isAdmin), 
  asyncHandler(getSecurityStats)
)

// 获取安全事件日志
router.get('/logs', 
  asyncHandler(verifyToken), 
  asyncHandler(isAdmin), 
  asyncHandler(getSecurityLogs)
)

// 获取安全分析数据
router.get('/analytics', 
  asyncHandler(verifyToken), 
  asyncHandler(isAdmin), 
  asyncHandler(getSecurityAnalytics)
)

// 触发测试安全事件（仅开发环境）
router.post('/test-event', 
  asyncHandler(verifyToken), 
  async<PERSON><PERSON><PERSON>(isAdmin), 
  asyncHand<PERSON>(triggerTestSecurityEvent)
)

export default router

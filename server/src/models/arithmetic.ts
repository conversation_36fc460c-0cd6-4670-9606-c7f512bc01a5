import { DataTypes, Model, Optional } from 'sequelize'
import sequelize from '../config/database'

// 算法接口
interface ArithmeticAttributes {
  id: number
  created_at: Date
  modified_at?: Date
  type?: number
  product_key?: string
  name?: string
  oss_path?: string
  content?: string
  md5?: string
  is_default?: boolean
}

// 创建时可选的属性
interface ArithmeticCreationAttributes
  extends Optional<
    ArithmeticAttributes,
    | 'id'
    | 'modified_at'
    | 'type'
    | 'product_key'
    | 'name'
    | 'oss_path'
    | 'content'
    | 'md5'
    | 'is_default'
  > {}

// 算法模型类
class Arithmetic
  extends Model<ArithmeticAttributes, ArithmeticCreationAttributes>
  implements ArithmeticAttributes
{
  public id!: number
  public created_at!: Date
  public modified_at?: Date
  public type?: number
  public product_key?: string
  public name?: string
  public oss_path?: string
  public content?: string
  public md5?: string
  public is_default?: boolean
}

// 初始化模型
Arithmetic.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
      comment: '主键ID，自增'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '记录创建时间'
    },
    modified_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '记录最后修改时间'
    },
    type: {
      type: DataTypes.SMALLINT,
      allowNull: true,
      defaultValue: 0,
      comment: '算法类型：0-清洗算法，1-分析算法'
    },
    product_key: {
      type: DataTypes.STRING(8),
      allowNull: true,
      comment: '算法适配的设备所属产品类型：WPD、PDF、AD'
    },
    name: {
      type: DataTypes.STRING(32),
      allowNull: true,
      comment: '算法名称'
    },
    oss_path: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '算法文件存储路径，阿里云oss'
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '算法内容'
    },
    md5: {
      type: DataTypes.STRING(32),
      allowNull: true,
      comment: '算法内容md5'
    },
    is_default: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false,
      comment: '是否为默认算法，一个类型只能有一个默认算法'
    }
  },
  {
    sequelize,
    tableName: 'dh_arithmetic',
    timestamps: false, // 不使用Sequelize的自动timestamps
    comment: '算法数据表'
  }
)

export default Arithmetic

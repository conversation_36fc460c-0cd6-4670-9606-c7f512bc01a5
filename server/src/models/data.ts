import { DataTypes, Model, Optional, QueryTypes } from 'sequelize'
import sequelize from '../config/database'
import { validateDeviceSerialNumber, validateTableName } from '../middleware/validation'
import { AppError } from '../middleware/error'

/**
 * 设备表名缓存 - 用于性能优化和安全验证
 */
const deviceTableCache = new Map<string, boolean>()

/**
 * 验证设备表是否存在于白名单中
 */
const isDeviceTableAllowed = async (tableName: string): Promise<boolean> => {
  // 检查缓存
  if (deviceTableCache.has(tableName)) {
    return deviceTableCache.get(tableName)!
  }

  try {
    // 查询数据库确认表是否存在
    const result = await sequelize.query(
      `SELECT EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_name = :tableName AND table_schema = 'public'
      )`,
      {
        replacements: { tableName },
        type: QueryTypes.SELECT,
        plain: true
      }
    ) as any

    const exists = result?.exists || false

    // 缓存结果（5分钟过期）
    deviceTableCache.set(tableName, exists)
    setTimeout(() => deviceTableCache.delete(tableName), 5 * 60 * 1000)

    return exists
  } catch (error) {
    console.error(`验证表存在性失败: ${tableName}`, error)
    return false
  }
}

// 设备原始数据接口
interface DeviceRawDataAttributes {
  id: number
  collection_at: Date
  device_sn: string
  heart?: number
  mode?: number
  strk_pct?: number
  "18b03"?: number
  wrm_cd?: number
  dpth?: number
  rtn_tq?: number
  frcst_kn?: number
  wtr_prs_h?: number
  rtn_spd?: number
  advnc_spd?: number
  hydr_prs?: number
  wtr_prs_l?: number
  hgh_wrk?: number
  lw_wrk?: number
  rtn_prs?: number
  frcst_prs?: number
  clct_type?: number
  clct_sts?: number
  tunnel_dpth?: number
  hl_num?: string
  hl_ang?: number
  rc0?: number
  rc1?: number
  rc2?: number
  rc3?: number
  rc4?: number
  led_08?: number
  led_af?: number
  tunnel_name?: string
  diameter?: number
}

// 创建时可选的属性
interface DeviceRawDataCreationAttributes extends Optional<DeviceRawDataAttributes, 'id'> {}

// 设备原始数据模型类
class DeviceRawData extends Model<DeviceRawDataAttributes, DeviceRawDataCreationAttributes> implements DeviceRawDataAttributes {
  public id!: number
  public collection_at!: Date
  public device_sn!: string
  public heart?: number
  public mode?: number
  public strk_pct?: number
  public "18b03"?: number
  public wrm_cd?: number
  public dpth?: number
  public rtn_tq?: number
  public frcst_kn?: number
  public wtr_prs_h?: number
  public rtn_spd?: number
  public advnc_spd?: number
  public hydr_prs?: number
  public wtr_prs_l?: number
  public hgh_wrk?: number
  public lw_wrk?: number
  public rtn_prs?: number
  public frcst_prs?: number
  public clct_type?: number
  public clct_sts?: number
  public tunnel_dpth?: number
  public hl_num?: string
  public hl_ang?: number
  public rc0?: number
  public rc1?: number
  public rc2?: number
  public rc3?: number
  public rc4?: number
  public led_08?: number
  public led_af?: number
  public tunnel_name?: string
  public diameter?: number
}

// 创建安全的模型工厂函数
export const createDeviceRawDataModel = (deviceSn: string) => {
  // 1. 验证设备序列号格式
  if (!validateDeviceSerialNumber(deviceSn)) {
    throw new AppError(`无效的设备序列号格式: ${deviceSn}`, 400)
  }

  // 2. 构建表名 - 确保使用小写并进行安全转换
  const sanitizedDeviceSn = deviceSn
    .replace(/[^a-zA-Z0-9\-_]/g, '') // 移除非法字符
    .replace(/-/g, '_') // 连字符转下划线
    .toLowerCase() // 转小写
    .substring(0, 50) // 限制长度

  const tableName = `dh_original_data_${sanitizedDeviceSn}`

  // 3. 验证最终的表名
  if (!validateTableName(tableName)) {
    throw new AppError(`生成的表名不符合安全规范: ${tableName}`, 400)
  }

  // 4. 检查表名长度
  if (tableName.length > 63) { // PostgreSQL表名长度限制
    throw new AppError(`表名过长: ${tableName}`, 400)
  }

  console.log(`安全验证通过，创建设备数据模型: ${tableName}`)

  // 5. 定义模型
  DeviceRawData.init(
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        comment: '主键ID，自增'
      },
      collection_at: {
        type: DataTypes.DATE,
        allowNull: false,
        comment: '采集时间，唯一值',
        get() {
          // 获取存储的日期时间值
          const date = this.getDataValue('collection_at');
          if (date) {
            // 检查是否为UTC时间格式(判断是否有Z后缀)
            //const dateStr = date.toISOString();
            let finalDate = date;
            // 不再转换UTC时间
            // if (dateStr.endsWith('Z')) {
            //   // 只有UTC时间才添加8小时转为北京时间
            //   finalDate = new Date(date.getTime() + (8 * 60 * 60 * 1000));
            // }
            
            // 格式化为 yyyy/MM/dd HH:mm:ss
            const year = finalDate.getFullYear();
            const month = String(finalDate.getMonth() + 1).padStart(2, '0');
            const day = String(finalDate.getDate()).padStart(2, '0');
            const hours = String(finalDate.getHours()).padStart(2, '0');
            const minutes = String(finalDate.getMinutes()).padStart(2, '0');
            const seconds = String(finalDate.getSeconds()).padStart(2, '0');
            
            return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`;
          }
          return date;
        }
      },
      device_sn: {
        type: DataTypes.STRING(32),
        allowNull: false,
        comment: '设备序列号'
      },
      heart: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '心跳号'
      },
      mode: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '钻机工作模式'
      },
      strk_pct: {
        type: DataTypes.FLOAT,
        allowNull: true,
        comment: '钻机行程百分比'
      },
      "18b03": {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '预留字段：18B03'
      },
      wrm_cd: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '告警码'
      },
      dpth: {
        type: DataTypes.FLOAT,
        allowNull: true,
        comment: '钻孔深度(CM)'
      },
      rtn_tq: {
        type: DataTypes.FLOAT,
        allowNull: true,
        comment: '旋转扭矩'
      },
      frcst_kn: {
        type: DataTypes.FLOAT,
        allowNull: true,
        comment: '推进力'
      },
      wtr_prs_h: {
        type: DataTypes.FLOAT,
        allowNull: true,
        comment: '高压水压力'
      },
      rtn_spd: {
        type: DataTypes.FLOAT,
        allowNull: true,
        comment: '旋转速度'
      },
      advnc_spd: {
        type: DataTypes.FLOAT,
        allowNull: true,
        comment: '钻进速度'
      },
      hydr_prs: {
        type: DataTypes.FLOAT,
        allowNull: true,
        comment: '液压压力'
      },
      wtr_prs_l: {
        type: DataTypes.FLOAT,
        allowNull: true,
        comment: '低压水压力'
      },
      hgh_wrk: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '工作时长-高位'
      },
      lw_wrk: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '工作时长-低位'
      },
      rtn_prs: {
        type: DataTypes.FLOAT,
        allowNull: true,
        comment: '旋转压力'
      },
      frcst_prs: {
        type: DataTypes.FLOAT,
        allowNull: true,
        comment: '推进压力'
      },
      clct_type: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '采集类型'
      },
      clct_sts: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '采集状态'
      },
      tunnel_dpth: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '隧道深度(标段号)'
      },
      hl_num: {
        type: DataTypes.STRING(32),
        allowNull: true,
        comment: '孔号'
      },
      hl_ang: {
        type: DataTypes.FLOAT,
        allowNull: true,
        comment: '孔倾角'
      },
      rc0: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: 'RC0'
      },
      rc1: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: 'RC1'
      },
      rc2: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: 'RC2'
      },
      rc3: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: 'RC3'
      },
      rc4: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: 'RC4'
      },
      led_08: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: 'LED_08'
      },
      led_af: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: 'LED_AF'
      },
      tunnel_name: {
        type: DataTypes.STRING(32),
        allowNull: true,
        comment: '隧道名称(项目名称)'
      },
      diameter: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '钻头直径，默认90'
      }
    },
    {
      sequelize,
      tableName,
      timestamps: false,
      comment: '设备原始数据表'
    }
  );
  
  return DeviceRawData;
};

/**
 * 安全验证设备表是否可用
 */
export const validateDeviceTable = async (deviceSn: string): Promise<boolean> => {
  try {
    // 验证设备序列号
    if (!validateDeviceSerialNumber(deviceSn)) {
      return false
    }

    // 构建表名
    const sanitizedDeviceSn = deviceSn
      .replace(/[^a-zA-Z0-9\-_]/g, '')
      .replace(/-/g, '_')
      .toLowerCase()
      .substring(0, 50)

    const tableName = `dh_original_data_${sanitizedDeviceSn}`

    // 验证表名格式
    if (!validateTableName(tableName)) {
      return false
    }

    // 验证表是否存在
    return await isDeviceTableAllowed(tableName)
  } catch (error) {
    console.error('设备表验证失败:', error)
    return false
  }
}

/**
 * 获取安全的设备表名
 */
export const getSecureDeviceTableName = (deviceSn: string): string => {
  if (!validateDeviceSerialNumber(deviceSn)) {
    throw new AppError(`无效的设备序列号: ${deviceSn}`, 400)
  }

  const sanitizedDeviceSn = deviceSn
    .replace(/[^a-zA-Z0-9\-_]/g, '')
    .replace(/-/g, '_')
    .toLowerCase()
    .substring(0, 50)

  const tableName = `dh_original_data_${sanitizedDeviceSn}`

  if (!validateTableName(tableName)) {
    throw new AppError(`生成的表名不安全: ${tableName}`, 400)
  }

  return tableName
}

export default DeviceRawData;
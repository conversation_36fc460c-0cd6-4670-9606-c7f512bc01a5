import sequelize from '../config/database'
import User from './user'
import Device from './device'
import File from './file'
import Arithmetic from './arithmetic'
import Import from './import'
import ChartComponent from './chartComponent'
import DeviceChartComponentConfig from './deviceChartComponentConfig'
import Template from './template'
import DeviceTemplateConfig from './deviceTemplateConfig'

// 定义模型之间的关联关系（如果有）
// 例如：
// User.hasMany(Device)
// Device.belongsTo(User)

// 表查询结果接口
interface TableExistsResult {
  exists: boolean
}

// 同步数据库模型
export const syncDatabase = async (force: boolean = false) => {
  try {
    // 检查dh_users表是否存在
    const userTableExists = await sequelize.query(
      "SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'dh_user')",
      { plain: true }
    )

    // 检查dh_chart_component表是否存在
    const chartTableExists = await sequelize.query(
      "SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'dh_chart_component')",
      { plain: true }
    )

    // 检查dh_device_chart_component_config表是否存在
    const deviceChartConfigExists = await sequelize.query(
      "SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'dh_device_chart_component_config')",
      { plain: true }
    )

    // 先转为unknown，再转为特定类型
    const isUserTableExisting = userTableExists && (userTableExists as unknown as TableExistsResult).exists
    const isChartTableExisting = chartTableExists && (chartTableExists as unknown as TableExistsResult).exists
    const isDeviceChartConfigExisting = deviceChartConfigExists && (deviceChartConfigExists as unknown as TableExistsResult).exists

    // 同步数据库模型
    await sequelize.sync({ force })
    console.log(`数据库模型${force ? '强制' : ''}同步成功`)

    // 如果用户表不存在或使用force模式，则创建默认用户
    if (!isUserTableExisting || force) {
      console.log('创建默认用户...')

      // 创建管理员用户
      await User.create({
        username: 'Admin User',
        account: 'admin',
        password: 'admin123', // 密码将通过beforeCreate钩子自动加密
        role: 'admin',
        created_at: new Date()
      })

      // 创建普通用户
      await User.create({
        username: 'Normal User',
        account: 'user',
        password: 'user123', // 密码将通过beforeCreate钩子自动加密
        role: 'user',
        created_at: new Date()
      })

      console.log('默认用户创建成功')
    } else {
      console.log('用户表已存在，跳过创建默认用户')
    }

    // 检查是否需要创建默认图表
    if (!isChartTableExisting || force) {
      console.log('创建默认图表组件数据...')

      const defaultCharts = [
        {
          name: '岩石性质分析',
          type: 'pie',
          component: 'RockPropertyCard',
          config: JSON.stringify({
            title: '岩石性质分析',
            description: '分析岩石的物理特性分布',
            thumbnail: 'https://mhp-data-hub.oss-cn-hangzhou.aliyuncs.com/web-panel/chart-component-thumbnail/rockPropertyCard.png',
            function: 'generateRockyNaturePieChartData'
          }),
          description: '通过饼图展示岩石性质分布情况',
          created_at: new Date(),
          modified_at: new Date(),
          status: 1
        },
        {
          name: '围岩统计',
          type: 'bar',
          component: 'RockStatsCard',
          config: JSON.stringify({
            title: '围岩统计',
            description: '统计围岩数据分布',
            thumbnail: 'https://mhp-data-hub.oss-cn-hangzhou.aliyuncs.com/web-panel/chart-component-thumbnail/rockStatsCard.png',
            function: 'generatePerimeterRockStatisticsBarChartData'
          }),
          description: '通过柱状图展示围岩统计数据',
          created_at: new Date(),
          modified_at: new Date(),
          status: 1
        },
        {
          name: '地质分析报告',
          type: 'table',
          component: 'GeologicAnalysisCard',
          config: JSON.stringify({
            title: '地质分析报告',
            description: '详细的地质分析报告数据',
            thumbnail: 'https://mhp-data-hub.oss-cn-hangzhou.aliyuncs.com/web-panel/chart-component-thumbnail/geologicAnalysisCard.png',
            function: 'generateGeologicAnalysisReport'
          }),
          description: '以表格形式展示地质分析报告',
          created_at: new Date(),
          modified_at: new Date(),
          status: 1
        },
        {
          name: '岩层分布',
          type: 'line',
          component: 'StrataDistributionCard',
          config: JSON.stringify({
            title: '岩层分布',
            description: '展示不同深度的岩层分布情况',
            thumbnail: 'https://mhp-data-hub.oss-cn-hangzhou.aliyuncs.com/web-panel/chart-component-thumbnail/strataDistributionCard.png',
            function: 'generateStrataDistributionLineChartData'
          }),
          description: '通过折线图展示岩层分布情况',
          created_at: new Date(),
          modified_at: new Date(),
          status: 1
        },
        {
          name: '钻进数据3D分析',
          type: '3d',
          component: 'Drilling3DAnalysisCard',
          config: JSON.stringify({
            title: '钻进数据3D分析',
            description: '钻进数据的3D可视化分析',
            thumbnail: 'https://mhp-data-hub.oss-cn-hangzhou.aliyuncs.com/web-panel/chart-component-thumbnail/drilling3DAnalysisCard.png',
            function: 'generateDrillingData'
          }),
          description: '通过3D图表展示钻进数据',
          created_at: new Date(),
          modified_at: new Date(),
          status: 1
        },
        {
          name: '钻进数据树状图',
          type: 'tree',
          component: 'DataTreeChartCard',
          config: JSON.stringify({
            title: '钻进数据树状图',
            description: '钻进数据的树状结构展示',
            thumbnail: 'https://mhp-data-hub.oss-cn-hangzhou.aliyuncs.com/web-panel/chart-component-thumbnail/dataTreeChartCard.png',
            function: 'generateDrillingData'
          }),
          description: '通过树状图展示钻进数据的层次结构',
          created_at: new Date(),
          modified_at: new Date(),
          status: 1
        },
        {
          name: '钻进数据曲线图',
          type: 'line',
          component: 'DrillCurveChartCard',
          config: JSON.stringify({
            title: '钻进数据曲线图',
            description: '钻进数据的曲线走势',
            thumbnail: 'https://mhp-data-hub.oss-cn-hangzhou.aliyuncs.com/web-panel/chart-component-thumbnail/drillCurveChartCard.png',
            function: 'generateDrillingData,generateDrillingCurveFilteringData,generateDetectStuckEventsData'
          }),
          description: '通过曲线图展示钻进数据的变化趋势',
          created_at: new Date(),
          modified_at: new Date(),
          status: 1
        },
        {
          name: '工况矩阵',
          type: 'matrix',
          component: 'ConditionMatrixCard',
          config: JSON.stringify({
            title: '工况矩阵',
            description: '展示工作状态矩阵数据',
            thumbnail: 'https://mhp-data-hub.oss-cn-hangzhou.aliyuncs.com/web-panel/chart-component-thumbnail/conditionMatrixCard.png',
            function: 'generateConditionMatrixChartData'
          }),
          description: '通过矩阵图展示各种工作状态',
          created_at: new Date(),
          modified_at: new Date(),
          status: 1
        },
        {
          name: '钻进数据表格',
          type: 'table',
          component: 'DrillingDataTableCard',
          config: JSON.stringify({
            title: '钻进数据表格',
            description: '展示所有钻进原始数据',
            thumbnail: 'https://mhp-data-hub.oss-cn-hangzhou.aliyuncs.com/web-panel/chart-component-thumbnail/drillingDataTableCard.png',
            function: 'generateDrillingData'
          }),
          description: '以表格形式展示钻进数据的详细信息',
          created_at: new Date(),
          modified_at: new Date(),
          status: 1
        },
        {
          name: '钻探记录表',
          type: 'record',
          component: 'DrillingRecordTableCard',
          config: JSON.stringify({
            title: '钻探记录表',
            description: '模拟传统纸质钻探记录表格式',
            thumbnail: 'https://mhp-data-hub.oss-cn-hangzhou.aliyuncs.com/web-panel/chart-component-thumbnail/drillingRecordTableCard.png',
            function: 'generateDrillingRecordData'
          }),
          description: '以传统纸质记录表格式展示钻探数据',
          created_at: new Date(),
          modified_at: new Date(),
          status: 1
        },
        {
          name: '钻进深度时序分析',
          type: 'timeseries',
          component: 'DrillingDepthTimeSeriesCard',
          config: JSON.stringify({
            title: '钻进深度时序分析',
            description: '展示不同采集时间的钻进深度变化，并标记卡钻和突进状态',
            thumbnail: 'https://mhp-data-hub.oss-cn-hangzhou.aliyuncs.com/web-panel/chart-component-thumbnail/drillingDepthTimeSeriesCard.png',
            function: 'generateDrillingDepthTimeSeriesData'
          }),
          description: '通过时序图表展示钻进深度随时间的变化趋势，并识别异常状态',
          created_at: new Date(),
          modified_at: new Date(),
          status: 1
        }
      ]

      // 创建默认图表
      for (const chartData of defaultCharts) {
        await ChartComponent.create(chartData)
      }

      console.log('默认图表组件数据创建成功')
    } else {
      console.log('图表组件表已存在，跳过创建默认图表组件')
    }

    // 输出设备图表组件配置表的状态
    if (!isDeviceChartConfigExisting) {
      console.log('设备图表组件配置表创建成功')
    } else {
      console.log('设备图表组件配置表已存在')
    }
  } catch (error) {
    console.error('数据库模型同步或创建默认数据失败:', error)
  }
}

// 导出所有模型
export { User, Device, File, Arithmetic, Import, ChartComponent, DeviceChartComponentConfig, Template, DeviceTemplateConfig }

export default sequelize

import { DataTypes, Model, Optional } from 'sequelize'
import sequelize from '../config/database'

// 模板接口
interface TemplateAttributes {
  id: number
  created_at: Date
  modified_at?: Date
  name: string
  config?: string
  description?: string
  status?: number
}

// 创建时可选的属性
interface TemplateCreationAttributes
  extends Optional<
    TemplateAttributes,
    | 'id'
    | 'modified_at'
    | 'config'
    | 'description'
    | 'status'
  > {}

// 模板模型类
class Template
  extends Model<TemplateAttributes, TemplateCreationAttributes>
  implements TemplateAttributes
{
  public id!: number
  public created_at!: Date
  public modified_at?: Date
  public name!: string
  public config?: string
  public description?: string
  public status?: number
}

// 初始化模型
Template.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
      comment: '主键ID，自增'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '模板创建时间'
    },
    modified_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '模板最后修改时间'
    },
    name: {
      type: DataTypes.STRING(64),
      allowNull: false,
      comment: '模板名称'
    },
    config: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '模板配置，JSON格式'
    },
    description: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '模板描述'
    },
    status: {
      type: DataTypes.SMALLINT,
      allowNull: true,
      defaultValue: 1,
      comment: '状态：0-禁用，1-启用'
    }
  },
  {
    sequelize,
    tableName: 'dh_template',
    timestamps: false, // 不使用Sequelize的自动timestamps
    comment: '模板信息表'
  }
)

export default Template 
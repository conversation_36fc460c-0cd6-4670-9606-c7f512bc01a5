import { DataTypes, Model, Optional } from 'sequelize'
import sequelize from '../config/database'
import bcrypt from 'bcryptjs'

// 用户接口
interface UserAttributes {
  id: number
  created_at: Date
  modified_at?: Date
  username: string
  account: string
  password: string
  role: 'admin' | 'user'
  token?: string | null
  // 钉钉相关字段
  dingtalk_open_id?: string | null
  dingtalk_union_id?: string | null
  mobile?: string | null
  email?: string | null
  avatar_url?: string | null
  createdAt?: Date
  updatedAt?: Date
}

// 创建时可选的属性
interface UserCreationAttributes extends Optional<UserAttributes, 'id'> {}

// 用户模型类
class User extends Model<UserAttributes, UserCreationAttributes> implements UserAttributes {
  public id!: number
  public created_at!: Date
  public modified_at?: Date
  public username!: string
  public account!: string
  public password!: string
  public role!: 'admin' | 'user'
  public token?: string | null
  // 钉钉相关字段
  public dingtalk_open_id?: string | null
  public dingtalk_union_id?: string | null
  public mobile?: string | null
  public email?: string | null
  public avatar_url?: string | null

  // 密码比较方法
  public async comparePassword(candidatePassword: string): Promise<boolean> {
    return bcrypt.compare(candidatePassword, this.password)
  }
}

// 初始化模型
User.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
      comment: '主键ID，自增'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '设备创建时间'
    },
    modified_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '设备信息最后修改时间'
    },
    username: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      comment: '用户名'
    },
    account: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      comment: '账号'
    },
    password: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: '密码'
    },
    role: {
      type: DataTypes.ENUM('admin', 'user'),
      defaultValue: 'user',
      comment: '用户角色 admin | user '
    },
    token: {
      type: DataTypes.STRING(512), // 增加长度以容纳JWT token
      allowNull: true,
      comment: 'token'
    },
    dingtalk_open_id: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: '钉钉openId'
    },
    dingtalk_union_id: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: '钉钉unionId'
    },
    mobile: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: '手机号'
    },
    email: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: '邮箱'
    },
    avatar_url: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: '头像URL'
    }
  },
  {
    sequelize,
    tableName: 'dh_user',
    timestamps: false, // 不使用Sequelize的自动timestamps
    comment: '用户信息表'
  }
)

// 钩子：保存前加密密码
User.beforeCreate(async user => {
  if (user.changed('password')) {
    const salt = await bcrypt.genSalt(10)
    user.password = await bcrypt.hash(user.password, salt)
  }
})

User.beforeUpdate(async user => {
  if (user.changed('password')) {
    const salt = await bcrypt.genSalt(10)
    user.password = await bcrypt.hash(user.password, salt)
  }
})

export default User

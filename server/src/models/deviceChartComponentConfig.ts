import { DataTypes, Model, Optional } from 'sequelize'
import sequelize from '../config/database'
import ChartComponent from './chartComponent'

// 设备图表组件配置接口
interface DeviceChartComponentConfigAttributes {
  id: number
  created_at: Date
  modified_at?: Date
  device_id: number
  chart_component_id: number
}

// 创建时可选的属性
interface DeviceChartComponentConfigCreationAttributes
  extends Optional<DeviceChartComponentConfigAttributes, 'id' | 'modified_at'> {}

// 设备图表组件配置模型类
class DeviceChartComponentConfig
  extends Model<DeviceChartComponentConfigAttributes, DeviceChartComponentConfigCreationAttributes>
  implements DeviceChartComponentConfigAttributes
{
  public id!: number
  public created_at!: Date
  public modified_at?: Date
  public device_id!: number
  public chart_component_id!: number

  // 静态方法：获取设备已启用的图表组件
  static async getEnabledChartsByDeviceId(deviceId: number) {
    try {
      // 从设备图表配置表中查询指定设备ID的所有记录
      // 同时关联图表组件表获取组件详情
      const chartConfigs = await DeviceChartComponentConfig.findAll({
        where: {
          device_id: deviceId
        },
        include: [
          {
            model: ChartComponent,
            as: 'chartComponent',
            attributes: ['id', 'name', 'type', 'component', 'config', 'description', 'status'],
            where: { status: 1 } // 只包含状态为启用的图表组件
          }
        ],
        order: [['id', 'ASC']]
      })
      
      // 转换结果格式
      const enabledCharts = chartConfigs.map(config => {
        const chartComponent = config.get('chartComponent') as any

        // 解析config字段以获取function属性
        let configObj: {[key: string]: any} = {}
        let functionNames: string[] = []

        try {
          if (chartComponent.config) {
            configObj = JSON.parse(chartComponent.config)
            const functionStr = configObj['function']
            if (functionStr) {
              // 处理逗号分隔的多个函数名
              functionNames = functionStr.split(',').map((fn: string) => fn.trim()).filter((fn: string) => fn.length > 0)
            }
          }
        } catch (e) {
          console.error('解析config字段失败:', e)
        }

        return {
          id: chartComponent.id,
          name: chartComponent.name,
          type: chartComponent.type,
          component: chartComponent.component,
          description: chartComponent.description,
          configId: config.id,
          enabled: true, // 由于我们只查询了已启用的，所以这里设为true
          functionNames: functionNames, // 新增：返回所有函数名数组
          configObj: configObj // 返回整个配置对象供后续使用
        }
      })
      
      return {
        success: true,
        data: {
          list: enabledCharts,
          total: enabledCharts.length
        }
      }
    } catch (error) {
      console.error('获取设备启用图表失败:', error)
      return {
        success: false,
        message: '获取设备启用图表失败'
      }
    }
  }
}

// 初始化模型
DeviceChartComponentConfig.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
      comment: '主键ID，自增'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '创建时间'
    },
    modified_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '最后修改时间'
    },
    device_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '设备ID'
    },
    chart_component_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '图表组件ID'
    }
  },
  {
    sequelize,
    tableName: 'dh_device_chart_component_config',
    timestamps: false, // 不使用Sequelize的自动timestamps
    comment: '设备图表组件配置表'
  }
)

export default DeviceChartComponentConfig 
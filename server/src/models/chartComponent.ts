import { DataTypes, Model, Optional } from 'sequelize'
import sequelize from '../config/database'

// 图表接口
interface ChartComponentAttributes {
  id: number
  created_at: Date
  modified_at?: Date
  name: string
  type: string
  component: string
  config?: string
  product_key?: string
  description?: string
  status?: number
}

// 创建时可选的属性
interface ChartComponentCreationAttributes
  extends Optional<
    ChartComponentAttributes,
    | 'id'
    | 'modified_at'
    | 'config'
    | 'product_key'
    | 'description'
    | 'status'
  > {}

// 图表模型类
class ChartComponent
  extends Model<ChartComponentAttributes, ChartComponentCreationAttributes>
  implements ChartComponentAttributes
{
  public id!: number
  public created_at!: Date
  public modified_at?: Date
  public name!: string
  public type!: string
  public component!: string
  public config?: string
  public product_key?: string
  public description?: string
  public status?: number
}

// 初始化模型
ChartComponent.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
      comment: '主键ID，自增'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '图表创建时间'
    },
    modified_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '图表最后修改时间'
    },
    name: {
      type: DataTypes.STRING(64),
      allowNull: false,
      comment: '图表名称'
    },
    type: {
      type: DataTypes.STRING(32),
      allowNull: false,
      comment: '图表类型，如：line, bar, pie, scatter等'
    },
    component: {
      type: DataTypes.STRING(64),
      allowNull: false,
      comment: '图表组件名称，如：RockPropertyCard, DrillCurveChartCard等'
    },
    config: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '图表配置，JSON格式'
    },
    product_key: {
      type: DataTypes.STRING(8),
      allowNull: true,
      comment: '图表适配的设备所属产品类型：WPD、PDF、AD等，为空表示通用'
    },
    description: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '图表描述'
    },
    status: {
      type: DataTypes.SMALLINT,
      allowNull: true,
      defaultValue: 1,
      comment: '状态：0-禁用，1-启用'
    }
  },
  {
    sequelize,
    tableName: 'dh_chart_component',
    timestamps: false, // 不使用Sequelize的自动timestamps
    comment: '图表组件信息表'
  }
)

export default ChartComponent 
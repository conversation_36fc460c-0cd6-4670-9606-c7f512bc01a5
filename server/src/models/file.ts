import { DataTypes, Model, Optional } from 'sequelize'
import sequelize from '../config/database'

// 文件接口
interface FileAttributes {
  id: number
  created_at: Date
  modified_at?: Date
  file_name?: string
  file_size?: number
  data_rows?: number
  first_data_time?: Date
  last_data_time?: Date
  local_path?: string
  oss_path?: string
  is_decrypted?: boolean
  device_sn?: string
  project_name?: string
  tunnel_section_no?: string
  hole_no?: string
  pad_mac?: string
}

// 创建时可选的属性
interface FileCreationAttributes
  extends Optional<
    FileAttributes,
    | 'id'
    | 'modified_at'
    | 'file_name'
    | 'file_size'
    | 'data_rows'
    | 'first_data_time'
    | 'last_data_time'
    | 'local_path'
    | 'oss_path'
    | 'is_decrypted'
    | 'device_sn'
    | 'project_name'
    | 'tunnel_section_no'
    | 'hole_no'
    | 'pad_mac'
  > {}

// 文件模型类
class File extends Model<FileAttributes, FileCreationAttributes> implements FileAttributes {
  public id!: number
  public created_at!: Date
  public modified_at?: Date
  public file_name?: string
  public file_size?: number
  public data_rows?: number
  public first_data_time?: Date
  public last_data_time?: Date
  public local_path?: string
  public oss_path?: string
  public is_decrypted?: boolean
  public device_sn?: string
  public project_name?: string
  public tunnel_section_no?: string
  public hole_no?: string
  public pad_mac?: string
}

// 初始化模型
File.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
      comment: '主键ID，自增'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '记录创建时间'
    },
    modified_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '记录最后修改时间'
    },
    file_name: {
      type: DataTypes.STRING(32),
      allowNull: true,
      comment: '文件名称'
    },
    file_size: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '文件大小（字节）'
    },
    data_rows: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '文件行数（解析后填入）'
    },
    first_data_time: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '数据最早时间'
    },
    last_data_time: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '数据最晚时间'
    },
    local_path: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '本地存储路径'
    },
    oss_path: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '云存储路径（如OSS）'
    },
    is_decrypted: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false,
      comment: '是否已解密 (true=已解密)'
    },
    device_sn: {
      type: DataTypes.STRING(32),
      allowNull: true,
      comment: '设备序列号'
    },
    project_name: {
      type: DataTypes.STRING(32),
      allowNull: true,
      comment: '所属项目名称'
    },
    tunnel_section_no: {
      type: DataTypes.STRING(32),
      allowNull: true,
      comment: '隧道段标识码'
    },
    hole_no: {
      type: DataTypes.STRING(32),
      allowNull: true,
      comment: '孔号（地质编号）'
    },
    pad_mac: {
      type: DataTypes.STRING(32),
      allowNull: true,
      comment: '上传文件的Pad MAC地址'
    }
  },
  {
    sequelize,
    tableName: 'dh_file',
    timestamps: false, // 不使用Sequelize的自动timestamps
    comment: '文件元数据表'
  }
)

export default File

import { DataTypes, Model, Optional } from 'sequelize'
import sequelize from '../config/database'

// 设备算法关联接口
interface DeviceArithmeticRefAttributes {
  id: number
  created_at: Date
  modified_at?: Date
  device_id: number
  arithmetic_id: number
  arithmetic_type: number
}

// 创建时可选的属性
interface DeviceArithmeticRefCreationAttributes
  extends Optional<DeviceArithmeticRefAttributes, 'id' | 'modified_at'> {}

// 设备算法关联模型类
class DeviceArithmeticRef
  extends Model<DeviceArithmeticRefAttributes, DeviceArithmeticRefCreationAttributes>
  implements DeviceArithmeticRefAttributes
{
  public id!: number
  public created_at!: Date
  public modified_at?: Date
  public device_id!: number
  public arithmetic_id!: number
  public arithmetic_type!: number
}

// 初始化模型
DeviceArithmeticRef.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
      comment: '主键ID，自增'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '记录创建时间'
    },
    modified_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '记录最后修改时间'
    },
    device_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '设备Id'
    },
    arithmetic_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '算法Id'
    },
    arithmetic_type: {
      type: DataTypes.SMALLINT,
      allowNull: false,
      comment: '算法类型：0-清洗算法，1-分析算法'
    }
  },
  {
    sequelize,
    tableName: 'dh_device_arithmetic_ref',
    timestamps: false,
    comment: '设备算法关联表'
  }
)

export default DeviceArithmeticRef

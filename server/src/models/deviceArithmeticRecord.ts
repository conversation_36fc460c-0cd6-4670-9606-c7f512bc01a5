import { DataTypes, Model, Optional } from 'sequelize'
import sequelize from '../config/database'

// 设备算法设置记录接口
interface DeviceArithmeticRecordAttributes {
  id: number
  created_at: Date
  device_id: number
  arithmetic_id: number
}

// 创建时可选的属性
interface DeviceArithmeticRecordCreationAttributes
  extends Optional<DeviceArithmeticRecordAttributes, 'id'> {}

// 设备算法设置记录模型类
class DeviceArithmeticRecord
  extends Model<DeviceArithmeticRecordAttributes, DeviceArithmeticRecordCreationAttributes>
  implements DeviceArithmeticRecordAttributes
{
  public id!: number
  public created_at!: Date
  public device_id!: number
  public arithmetic_id!: number
}

// 初始化模型
DeviceArithmeticRecord.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
      comment: '主键ID，自增'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '记录创建时间'
    },
    device_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '设备Id'
    },
    arithmetic_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '算法Id'
    }
  },
  {
    sequelize,
    tableName: 'dh_device_arithmetic_record',
    timestamps: false,
    comment: '设备算法设置记录表'
  }
)

export default DeviceArithmeticRecord

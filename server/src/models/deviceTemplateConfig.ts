import { DataTypes, Model, Optional } from 'sequelize'
import sequelize from '../config/database'
import Template from './template'

// 设备模板配置接口
interface DeviceTemplateConfigAttributes {
  id: number
  created_at: Date
  modified_at?: Date
  device_id: number
  template_id: number
}

// 创建时可选的属性
interface DeviceTemplateConfigCreationAttributes
  extends Optional<DeviceTemplateConfigAttributes, 'id' | 'modified_at'> {}

// 设备模板配置模型类
class DeviceTemplateConfig
  extends Model<DeviceTemplateConfigAttributes, DeviceTemplateConfigCreationAttributes>
  implements DeviceTemplateConfigAttributes
{
  public id!: number
  public created_at!: Date
  public modified_at?: Date
  public device_id!: number
  public template_id!: number

  // 静态方法：获取设备已启用的模板
  static async getEnabledTemplatesByDeviceId(deviceId: number) {
    try {
      // 从设备模板配置表中查询指定设备ID的所有记录
      // 同时关联模板表获取模板详情
      const templateConfigs = await DeviceTemplateConfig.findAll({
        where: {
          device_id: deviceId
        },
        include: [
          {
            model: Template,
            as: 'template',
            attributes: ['id', 'name', 'config', 'description', 'status'],
            where: { status: 1 } // 只包含状态为启用的模板
          }
        ],
        order: [['id', 'ASC']]
      })
      
      // 转换结果格式
      const enabledTemplates = templateConfigs.map(config => {
        const template = config.get('template') as any
        
        // 解析config字段
        let configObj: {[key: string]: any} = {}
        
        try {
          if (template.config) {
            configObj = JSON.parse(template.config)
          }
        } catch (e) {
          console.error('解析config字段失败:', e)
        }
        
        return {
          id: template.id,
          name: template.name,
          description: template.description,
          configId: config.id,
          enabled: true, // 由于我们只查询了已启用的，所以这里设为true
          configObj: configObj // 返回整个配置对象供后续使用
        }
      })
      
      return {
        success: true,
        data: {
          list: enabledTemplates,
          total: enabledTemplates.length
        }
      }
    } catch (error) {
      console.error('获取设备启用模板失败:', error)
      return {
        success: false,
        message: '获取设备启用模板失败'
      }
    }
  }
}

// 初始化模型
DeviceTemplateConfig.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
      comment: '主键ID，自增'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '创建时间'
    },
    modified_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '最后修改时间'
    },
    device_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '设备ID'
    },
    template_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '模板ID'
    }
  },
  {
    sequelize,
    tableName: 'dh_device_template_config',
    timestamps: false, // 不使用Sequelize的自动timestamps
    comment: '设备模板配置表'
  }
)

export default DeviceTemplateConfig 
import { DataTypes, Model, Optional } from 'sequelize'
import sequelize from '../config/database'

// 设备接口
interface DeviceAttributes {
  id: number
  created_at: Date
  modified_at?: Date
  product_key?: string
  device_name?: string
  device_sn?: string
  device_secret?: string
  device_mac?: string
  project_name?: string
  creation_method?: number
  import_id?: number
}

// 创建时可选的属性
interface DeviceCreationAttributes
  extends Optional<
    DeviceAttributes,
    | 'id'
    | 'modified_at'
    | 'product_key'
    | 'device_name'
    | 'device_sn'
    | 'device_secret'
    | 'device_mac'
    | 'project_name'
    | 'creation_method'
    | 'import_id'
  > {}

// 设备模型类
class Device extends Model<DeviceAttributes, DeviceCreationAttributes> implements DeviceAttributes {
  public id!: number
  public created_at!: Date
  public modified_at?: Date
  public product_key?: string
  public device_name?: string
  public device_sn?: string
  public device_secret?: string
  public device_mac?: string
  public project_name?: string
  public creation_method?: number
  public import_id?: number
}

// 初始化模型
Device.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
      comment: '主键ID，自增'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '设备创建时间'
    },
    modified_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '设备信息最后修改时间'
    },
    product_key: {
      type: DataTypes.STRING(8),
      allowNull: true,
      comment: '设备所属产品类型：WPD、PDF、AD'
    },
    device_name: {
      type: DataTypes.STRING(32),
      allowNull: true,
      comment: '设备名称'
    },
    device_sn: {
      type: DataTypes.STRING(32),
      allowNull: true,
      comment: '设备SN号',
      unique: true
    },
    device_secret: {
      type: DataTypes.STRING(10),
      allowNull: true,
      comment: '设备密钥，10位随机码；AES解密密钥=device_mac后6位+device_secret'
    },
    device_mac: {
      type: DataTypes.STRING(32),
      allowNull: true,
      comment: '设备Mac地址'
    },
    project_name: {
      type: DataTypes.STRING(32),
      allowNull: true,
      comment: '设备所属项目名称'
    },
    creation_method: {
      type: DataTypes.SMALLINT,
      allowNull: true,
      defaultValue: 1,
      comment: '设备创建方式：0-自动创建，1-生产导入'
    },
    import_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '生产导入批次ID'
    }
  },
  {
    sequelize,
    tableName: 'dh_device',
    timestamps: false, // 不使用Sequelize的自动timestamps
    comment: '设备信息数据表'
  }
)

export default Device

import OSS from 'ali-oss'
import config from '../config'

// OSS配置
const ossConfig = config.oss

// 初始化OSS客户端
let ossClient: OSS | null = null

try {
  ossClient = new OSS({
    region: ossConfig.region,
    accessKeyId: ossConfig.accessKeyId,
    accessKeySecret: ossConfig.accessKeySecret,
    bucket: ossConfig.bucket
  })
} catch (error) {
  console.error('OSS客户端初始化失败:', error)
}

/**
 * 上传算法内容到OSS
 * @param content 要上传的内容
 * @param name 算法名称
 * @param type 算法类型 (0: 清洗算法, 1: 分析算法)
 * @param productKey 产品类型 ('WPD': 水锤, 'PDF': 超前钻机, 'AD': 锚杆钻)
 * @returns 返回OSS路径
 */
export const uploadAlgorithmsContentToOSS = async (
  content: string,
  name: string,
  type?: number,
  productKey?: string
): Promise<string> => {
  if (!ossClient) {
    throw new Error('OSS客户端未初始化')
  }

  // 根据算法类型和产品类型构建路径
  let filePath: string

  if (type === 0) {
    // 清洗算法: uploads/algorithms/clean/[name].js
    filePath = `algorithms/clean/${name}.js`
  } else if (type === 1 && productKey) {
    // 分析算法: uploads/algorithms/process/[productKey]/[name].js
    // 将产品类型转为小写
    const productKeyLower = productKey.toLowerCase()
    filePath = `algorithms/process/${productKeyLower}/${name}.js`
  } else {
    // 默认路径
    filePath = `algorithms/${name}.js`
  }

  try {
    // 上传Buffer
    const buffer = Buffer.from(content)
    const result = await ossClient.put(filePath, buffer)

    // 返回OSS路径
    return result.name
  } catch (error) {
    console.error('上传到OSS失败:', error)
    throw new Error('上传到OSS失败')
  }
}

/**
 * 上传导入内容到OSS
 * @param content 要上传的内容
 * @param name 算法名称
 * @returns 返回OSS路径
 */
export const uploadImportContentToOSS = async (content: string, name: string): Promise<string> => {
  if (!ossClient) {
    throw new Error('OSS客户端未初始化')
  }

  // 根据算法类型和产品类型构建路径
  const filePath: string = `import/${name}.csv`

  try {
    // 上传Buffer
    const buffer = Buffer.from(content)
    const result = await ossClient.put(filePath, buffer)

    // 返回OSS路径
    return result.name
  } catch (error) {
    console.error('上传到OSS失败:', error)
    throw new Error('上传到OSS失败')
  }
}

/**
 * 从OSS删除文件
 * @param ossPath 要删除的OSS文件路径
 * @returns 是否删除成功
 */
export const deleteFileFromOSS = async (ossPath: string): Promise<boolean> => {
  if (!ossClient) {
    throw new Error('OSS客户端未初始化')
  }

  try {
    await ossClient.delete(ossPath)
    return true
  } catch (error) {
    console.error('从OSS删除文件失败:', error)
    return false
  }
}

export default {
  uploadAlgorithmsContentToOSS,
  uploadImportContentToOSS,
  deleteFileFromOSS
}

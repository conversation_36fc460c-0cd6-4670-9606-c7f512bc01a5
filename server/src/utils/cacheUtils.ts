import crypto from 'crypto'

/**
 * 生成标准化的缓存键
 * @param category 缓存类别 (device, algorithm, template等)
 * @param userId 用户ID
 * @param identifier 标识符 (通常是参数的哈希值)
 * @returns 标准化的缓存键
 */
export const generateCacheKey = (category: string, userId: number, identifier: string): string => {
  return `${category}:${userId}:${identifier}`
}

/**
 * 对参数进行哈希处理
 * @param params 需要哈希的参数对象
 * @returns MD5哈希值的前8位
 */
export const hashParams = (params: any): string => {
  return crypto.createHash('md5')
    .update(JSON.stringify(params))
    .digest('hex')
    .substring(0, 8)
}

/**
 * 生成请求的唯一标识符
 * @param req Express请求对象
 * @returns 请求的哈希标识符
 */
export const generateRequestIdentifier = (req: any): string => {
  const requestData = {
    path: req.path,
    query: req.query,
    body: req.body,
    method: req.method
  }
  return hashParams(requestData)
}

/**
 * 验证缓存键格式
 * @param key 缓存键
 * @returns 是否为有效的缓存键格式
 */
export const validateCacheKey = (key: string): boolean => {
  // 缓存键格式: category:userId:identifier
  const parts = key.split(':')
  return parts.length === 3 && 
         parts[0].length > 0 && 
         !isNaN(parseInt(parts[1])) && 
         parts[2].length > 0
}

/**
 * 从缓存键中提取信息
 * @param key 缓存键
 * @returns 解析后的缓存键信息
 */
export const parseCacheKey = (key: string): { category: string; userId: number; identifier: string } | null => {
  if (!validateCacheKey(key)) {
    return null
  }
  
  const parts = key.split(':')
  return {
    category: parts[0],
    userId: parseInt(parts[1]),
    identifier: parts[2]
  }
}

/**
 * 生成缓存模式匹配字符串
 * @param category 缓存类别，可选
 * @param userId 用户ID，可选
 * @returns 模式匹配字符串
 */
export const generateCachePattern = (category?: string, userId?: number): string => {
  if (category && userId) {
    return `${category}:${userId}:`
  } else if (category) {
    return `${category}:`
  } else if (userId) {
    return `:${userId}:`
  } else {
    return ''
  }
}

/**
 * 检查请求是否应该跳过缓存
 * @param req Express请求对象
 * @returns 是否应该跳过缓存
 */
export const shouldSkipCache = (req: any): boolean => {
  // 检查请求头中的缓存控制
  const cacheControl = req.headers['cache-control']
  if (cacheControl && cacheControl.includes('no-cache')) {
    return true
  }
  
  // 检查查询参数中的缓存控制
  if (req.query && req.query.nocache === 'true') {
    return true
  }
  
  // 检查请求对象上的跳过缓存标记
  if (req.skipCache === true) {
    return true
  }
  
  return false
}

/**
 * 格式化缓存统计信息
 * @param stats 原始统计信息
 * @returns 格式化后的统计信息
 */
export const formatCacheStats = (stats: any) => {
  return {
    totalKeys: stats.keys || 0,
    hitCount: stats.hits || 0,
    missCount: stats.misses || 0,
    hitRate: stats.hits && stats.misses ? 
      ((stats.hits / (stats.hits + stats.misses)) * 100).toFixed(2) + '%' : '0%',
    memoryUsage: {
      keySize: stats.ksize || 0,
      valueSize: stats.vsize || 0,
      total: (stats.ksize || 0) + (stats.vsize || 0)
    }
  }
}

/**
 * 生成缓存键的调试信息
 * @param category 缓存类别
 * @param userId 用户ID
 * @param identifier 标识符
 * @returns 调试信息字符串
 */
export const getCacheKeyDebugInfo = (category: string, userId: number, identifier: string): string => {
  const key = generateCacheKey(category, userId, identifier)
  return `Cache Key: ${key} | Category: ${category} | User: ${userId} | ID: ${identifier}`
}

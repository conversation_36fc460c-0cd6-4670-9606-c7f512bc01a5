/**
 * 通用工具函数集
 */

/**
 * 根据产品代码获取设备类型名称
 * @param productKey 产品代码
 * @returns 设备类型名称
 */
export const getDeviceTypeName = (productKey?: string): string => {
  if (!productKey) return '未知'

  switch (productKey) {
    case 'WPD':
      return '水锤'
    case 'PDF':
      return '超前钻机'
    case 'AD':
      return '锚杆钻'
    default:
      return '未知'
  }
}

/**
 * 根据算法类型获取算法类型名称
 * @param type 算法类型（0:清洗算法, 1:分析算法）
 * @returns 算法类型名称
 */
export const getAlgorithmTypeName = (type?: number): string => {
  if (type === undefined) return '未知'

  switch (type) {
    case 0:
      return '清洗算法'
    case 1:
      return '控件算法'
    default:
      return '未知'
  }
}

/**
 * 格式化日期时间
 * @param date 日期对象或日期字符串或时间戳
 * @param defaultValue 当日期无效时返回的默认值
 * @returns 格式化后的日期时间字符串
 */
export const formatDateTime = (
  date: Date | string | number | undefined | null,
  defaultValue = '未知'
): string => {
  if (!date) return defaultValue

  try {
    const dateObj = date instanceof Date ? date : new Date(date)
    // 检查日期是否有效
    if (isNaN(dateObj.getTime())) return defaultValue
    return dateObj.toLocaleString()
  } catch (error) {
    return defaultValue
  }
}

/**
 * 格式化文件大小
 * @param sizeInBytes 文件大小（字节）
 * @param defaultValue 当大小无效时返回的默认值
 * @returns 格式化后的文件大小字符串
 */
export const formatFileSize = (
  sizeInBytes: number | undefined | null,
  defaultValue = '未知'
): string => {
  if (sizeInBytes === undefined || sizeInBytes === null) return defaultValue

  if (sizeInBytes < 1024) {
    return sizeInBytes + ' B'
  } else if (sizeInBytes < 1024 * 1024) {
    return (sizeInBytes / 1024).toFixed(2) + ' KB'
  } else if (sizeInBytes < 1024 * 1024 * 1024) {
    return (sizeInBytes / (1024 * 1024)).toFixed(2) + ' MB'
  } else {
    return (sizeInBytes / (1024 * 1024 * 1024)).toFixed(2) + ' GB'
  }
}

/**
 * 将对象的键从蛇形命名（snake_case）转换为驼峰命名（camelCase）
 * @param obj 要转换的对象
 * @returns 转换后的对象
 */
export const convertToCamelCase = (obj: Record<string, any>): Record<string, any> => {
  // 如果不是对象或者是null，直接返回
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }

  // 如果是日期对象，直接返回
  if (obj instanceof Date) {
    return obj;
  }

  // 如果是数组，对数组中的每个元素递归调用
  if (Array.isArray(obj)) {
    return obj.map(item => convertToCamelCase(item));
  }

  // 处理对象
  return Object.keys(obj).reduce((acc: Record<string, any>, key: string) => {
    // 将键从 snake_case 转换为 camelCase
    const camelKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
    
    // 检查值是否为字符串格式的日期
    const value = obj[key];
    // 增强日期检测，包括标准ISO格式和PostgreSQL返回的日期格式
    if (typeof value === 'string' && (
      /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(.\d+)?Z$/.test(value) || // ISO格式
      /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(.\d+)?(\+|-)\d{2}:\d{2}$/.test(value) || // 带时区的ISO格式
      /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}(\.\d+)?$/.test(value) // PostgreSQL日期格式
    )) {
      // 如果是日期字符串，转换为本地时间格式 "YYYY-MM-DD HH:MM:SS"
      const date = new Date(value);
      
      // 检查日期是否有效
      if (!isNaN(date.getTime())) {
        // 获取本地时间部分
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
      
      acc[camelKey] = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      } else {
        // 如果日期无效，保留原始值
        acc[camelKey] = value;
      }
    } else {
      // 对其他值递归调用，处理嵌套对象
      acc[camelKey] = convertToCamelCase(value);
    }
    
    return acc;
  }, {});
};

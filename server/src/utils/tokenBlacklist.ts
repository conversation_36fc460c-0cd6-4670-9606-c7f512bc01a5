import { createHash } from 'crypto'

/**
 * Token黑名单管理器
 * 用于管理已失效的JWT Token，防止被恶意重复使用
 */
export class TokenBlacklist {
  private blacklist = new Map<string, BlacklistEntry>()
  private readonly maxSize: number
  private readonly cleanupInterval: number
  private cleanupTimer?: NodeJS.Timeout

  constructor(maxSize: number = 10000, cleanupIntervalMs: number = 60 * 60 * 1000) { // 1小时清理一次
    this.maxSize = maxSize
    this.cleanupInterval = cleanupIntervalMs
    this.startCleanupTimer()
  }

  /**
   * 将Token添加到黑名单
   * @param token JWT Token字符串
   * @param reason 加入黑名单的原因
   * @param expiresAt Token的过期时间
   */
  addToBlacklist(token: string, reason: string = 'logout', expiresAt?: Date): void {
    try {
      const tokenHash = this.hashToken(token)
      const entry: BlacklistEntry = {
        tokenHash,
        reason,
        addedAt: new Date(),
        expiresAt: expiresAt || new Date(Date.now() + 24 * 60 * 60 * 1000), // 默认24小时后过期
        originalTokenLength: token.length
      }

      this.blacklist.set(tokenHash, entry)

      // 如果黑名单过大，清理最旧的条目
      if (this.blacklist.size > this.maxSize) {
        this.cleanupOldEntries()
      }

      console.log(`Token已加入黑名单: ${reason}, 当前黑名单大小: ${this.blacklist.size}`)
    } catch (error) {
      console.error('添加Token到黑名单失败:', error)
    }
  }

  /**
   * 检查Token是否在黑名单中
   * @param token JWT Token字符串
   * @returns 是否在黑名单中
   */
  isBlacklisted(token: string): boolean {
    try {
      const tokenHash = this.hashToken(token)
      const entry = this.blacklist.get(tokenHash)

      if (!entry) {
        return false
      }

      // 检查是否已过期
      if (entry.expiresAt && entry.expiresAt < new Date()) {
        this.blacklist.delete(tokenHash)
        return false
      }

      return true
    } catch (error) {
      console.error('检查Token黑名单状态失败:', error)
      return false // 出错时默认不在黑名单中
    }
  }

  /**
   * 从黑名单中移除Token（通常不需要，因为Token会自动过期）
   * @param token JWT Token字符串
   */
  removeFromBlacklist(token: string): void {
    try {
      const tokenHash = this.hashToken(token)
      const removed = this.blacklist.delete(tokenHash)
      
      if (removed) {
        console.log('Token已从黑名单中移除')
      }
    } catch (error) {
      console.error('从黑名单移除Token失败:', error)
    }
  }

  /**
   * 获取黑名单统计信息
   */
  getStats(): BlacklistStats {
    const now = new Date()
    let expiredCount = 0
    let activeCount = 0

    for (const entry of this.blacklist.values()) {
      if (entry.expiresAt && entry.expiresAt < now) {
        expiredCount++
      } else {
        activeCount++
      }
    }

    return {
      totalEntries: this.blacklist.size,
      activeEntries: activeCount,
      expiredEntries: expiredCount,
      maxSize: this.maxSize
    }
  }

  /**
   * 清理过期的黑名单条目
   */
  cleanup(): number {
    const now = new Date()
    let cleanedCount = 0

    for (const [tokenHash, entry] of this.blacklist.entries()) {
      if (entry.expiresAt && entry.expiresAt < now) {
        this.blacklist.delete(tokenHash)
        cleanedCount++
      }
    }

    if (cleanedCount > 0) {
      console.log(`清理了 ${cleanedCount} 个过期的黑名单条目`)
    }

    return cleanedCount
  }

  /**
   * 清理最旧的条目（当黑名单过大时）
   */
  private cleanupOldEntries(): void {
    const entries = Array.from(this.blacklist.entries())
    entries.sort((a, b) => a[1].addedAt.getTime() - b[1].addedAt.getTime())

    const toRemove = Math.floor(this.maxSize * 0.1) // 移除10%的最旧条目
    for (let i = 0; i < toRemove && i < entries.length; i++) {
      this.blacklist.delete(entries[i][0])
    }

    console.log(`清理了 ${toRemove} 个最旧的黑名单条目`)
  }

  /**
   * 对Token进行哈希处理（安全存储）
   */
  private hashToken(token: string): string {
    return createHash('sha256').update(token).digest('hex')
  }

  /**
   * 启动定时清理器
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup()
    }, this.cleanupInterval)
  }

  /**
   * 停止定时清理器
   */
  stopCleanup(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = undefined
    }
  }

  /**
   * 销毁黑名单管理器
   */
  destroy(): void {
    this.stopCleanup()
    this.blacklist.clear()
  }
}

/**
 * 黑名单条目接口
 */
interface BlacklistEntry {
  tokenHash: string
  reason: string
  addedAt: Date
  expiresAt: Date
  originalTokenLength: number
}

/**
 * 黑名单统计信息接口
 */
interface BlacklistStats {
  totalEntries: number
  activeEntries: number
  expiredEntries: number
  maxSize: number
}

// 创建全局单例实例
export const tokenBlacklist = new TokenBlacklist()

// 优雅关闭时清理资源
process.on('SIGTERM', () => {
  tokenBlacklist.destroy()
})

process.on('SIGINT', () => {
  tokenBlacklist.destroy()
})

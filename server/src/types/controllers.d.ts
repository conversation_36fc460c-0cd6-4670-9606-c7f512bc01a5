import { Request } from 'express'

export interface LoginRequest extends Request {
  body: {
    username: string
    password: string
    name?: string
  }
}

export interface DingTalkLoginRequest extends Request {
  body: {
    code: string
  }
}

export interface LoginResponse {
  message: string
  user: {
    id: string
    username: string
    name: string
    role: string
    avatar_url?: string | null
  }
  token: string
}

export interface LogoutRequest extends Request {
  headers: {
    authorization?: string
  }
}

export interface LogoutResponse {
  message: string
}

export interface GetCurrentUserRequest extends Request {
  user?: {
    id: string
    username: string
    role: string
  }
  body: {
    name?: string
    password?: string
  }
}

export interface GetCurrentUserResponse {
  id: string
  username: string
  name: string
  role: string
  avatar_url?: string | null
}

export interface ExampleResponse {
  message: string
  timestamp: string
  data: Array<{
    id: string
    name: string
  }>
}

export interface UpdateProfileResponse {
  message: string
  user: {
    id: string
    username: string
    name: string
    role: string
    avatar_url?: string | null
  }
}

import { Request, Response, NextFunction } from 'express'

export interface JwtPayload {
  id: string
  username: string
  role: string
  type?: string // 添加type字段，用于区分access和refresh token
  jti?: string
  iat?: number
  exp?: number
}

export interface AuthRequest extends Request {
  user?: JwtPayload
  headers: {
    authorization?: string
  }
  cookies?: {
    token?: string // 兼容旧版本
    accessToken?: string // 新的访问令牌
    refreshToken?: string // 新的刷新令牌
  }
  // 支持Cookie和Header两种方式传递token
  skipCache?: boolean // 缓存跳过标记
}

export type AuthMiddleware = (
  req: AuthRequest,
  res: Response,
  next: NextFunction
) => void | Promise<void>

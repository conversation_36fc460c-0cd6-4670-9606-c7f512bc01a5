export interface User {
  id: string
  username: string
  email: string
  password: string
  role: 'admin' | 'user'
  createdAt: Date
  updatedAt: Date
}

export interface Device {
  id: string
  name: string
  type: string
  status: 'online' | 'offline' | 'error'
  lastSeen: Date
  createdAt: Date
  updatedAt: Date
}

export interface Example {
  id: string
  name: string
  description: string
  createdAt: Date
  updatedAt: Date
}

'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable(
      'dh_device_arithmetic_ref',
      {
        id: {
          allowNull: false,
          autoIncrement: true,
          primaryKey: true,
          type: Sequelize.INTEGER,
          comment: '主键ID，自增'
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('NOW()'),
          comment: '记录创建时间'
        },
        modified_at: {
          type: Sequelize.DATE,
          allowNull: true,
          comment: '记录最后修改时间'
        },
        device_id: {
          type: Sequelize.INTEGER,
          allowNull: false,
          comment: '设备Id'
        },
        arithmetic_id: {
          type: Sequelize.INTEGER,
          allowNull: false,
          comment: '算法Id'
        },
        arithmetic_type: {
          type: Sequelize.SMALLINT,
          allowNull: false,
          comment: '算法类型：0-清洗算法，1-分析算法'
        }
      },
      {
        comment: '设备算法关联表'
      }
    )

    // 添加索引
    await queryInterface.addIndex('dh_device_arithmetic_ref', ['device_id'])
    await queryInterface.addIndex('dh_device_arithmetic_ref', ['arithmetic_id'])
    await queryInterface.addIndex('dh_device_arithmetic_ref', ['device_id', 'arithmetic_type'], {
      unique: true,
      name: 'device_arithmetic_type_unique'
    })

    // 添加外键约束
    await queryInterface.addConstraint('dh_device_arithmetic_ref', {
      fields: ['device_id'],
      type: 'foreign key',
      name: 'fk_device_arithmetic_ref_device',
      references: {
        table: 'dh_device',
        field: 'id'
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE'
    })

    await queryInterface.addConstraint('dh_device_arithmetic_ref', {
      fields: ['arithmetic_id'],
      type: 'foreign key',
      name: 'fk_device_arithmetic_ref_arithmetic',
      references: {
        table: 'dh_arithmetic',
        field: 'id'
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE'
    })
  },

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('dh_device_arithmetic_ref')
  }
}

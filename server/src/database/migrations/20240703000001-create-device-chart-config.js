'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('dh_device_chart_component_config', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
        comment: '主键ID，自增'
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        comment: '创建时间'
      },
      modified_at: {
        allowNull: true,
        type: Sequelize.DATE,
        comment: '最后修改时间'
      },
      device_id: {
        allowNull: false,
        type: Sequelize.INTEGER,
        comment: '设备ID',
        references: {
          model: 'dh_device',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      chart_component_id: {
        allowNull: false,
        type: Sequelize.INTEGER,
        comment: '图表组件ID',
        references: {
          model: 'dh_chart_component',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      }
    }, {
      comment: '设备图表组件配置表'
    });

    // 添加复合唯一索引，确保一个设备不会对同一个图表组件有多个配置
    await queryInterface.addIndex(
      'dh_device_chart_component_config',
      ['device_id', 'chart_component_id'],
      {
        unique: true,
        name: 'idx_device_chart_config_unique'
      }
    );
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('dh_device_chart_component_config');
  }
}; 
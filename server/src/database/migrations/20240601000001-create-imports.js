'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable(
      'dh_import',
      {
        id: {
          allowNull: false,
          autoIncrement: true,
          primaryKey: true,
          type: Sequelize.INTEGER,
          comment: '主键ID，自增'
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('NOW()'),
          comment: '记录创建时间'
        },
        file_name: {
          type: Sequelize.STRING(255),
          allowNull: true,
          comment: '文件名称'
        },
        oss_path: {
          type: Sequelize.STRING(255),
          allowNull: true,
          comment: '云存储路径（如OSS）'
        },
        import_rows: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: '导入文件的行数'
        },
        content: {
          type: Sequelize.TEXT,
          allowNull: true,
          comment: '文件内容'
        }
      },
      {
        comment: '导入数据表'
      }
    )
  },
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('dh_import')
  }
}

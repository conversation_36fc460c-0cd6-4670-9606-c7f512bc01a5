'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable(
      'dh_device_arithmetic_record',
      {
        id: {
          allowNull: false,
          autoIncrement: true,
          primaryKey: true,
          type: Sequelize.INTEGER,
          comment: '主键ID，自增'
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('NOW()'),
          comment: '记录创建时间'
        },
        device_id: {
          type: Sequelize.INTEGER,
          allowNull: false,
          comment: '设备Id'
        },
        arithmetic_id: {
          type: Sequelize.INTEGER,
          allowNull: false,
          comment: '算法Id'
        }
      },
      {
        comment: '设备算法设置记录表'
      }
    )

    // 添加索引
    await queryInterface.addIndex('dh_device_arithmetic_record', ['device_id'])
    await queryInterface.addIndex('dh_device_arithmetic_record', ['arithmetic_id'])
    await queryInterface.addIndex('dh_device_arithmetic_record', ['created_at'])

    // 添加外键约束
    await queryInterface.addConstraint('dh_device_arithmetic_record', {
      fields: ['device_id'],
      type: 'foreign key',
      name: 'fk_device_arithmetic_record_device',
      references: {
        table: 'dh_device',
        field: 'id'
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE'
    })

    await queryInterface.addConstraint('dh_device_arithmetic_record', {
      fields: ['arithmetic_id'],
      type: 'foreign key',
      name: 'fk_device_arithmetic_record_arithmetic',
      references: {
        table: 'dh_arithmetic',
        field: 'id'
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE'
    })
  },

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('dh_device_arithmetic_record')
  }
}

'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      // 创建表
      await queryInterface.createTable(
        'dh_chart_component',
        {
          id: {
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            type: Sequelize.INTEGER,
            comment: '主键ID，自增'
          },
          created_at: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.literal('NOW()'),
            comment: '组件创建时间'
          },
          modified_at: {
            type: Sequelize.DATE,
            allowNull: true,
            comment: '组件最后修改时间'
          },
          name: {
            type: Sequelize.STRING(64),
            allowNull: false,
            comment: '组件名称'
          },
          type: {
            type: Sequelize.STRING(32),
            allowNull: false,
            comment: '图表类型，如：line, bar, pie, scatter等'
          },
          component: {
            type: Sequelize.STRING(64),
            allowNull: false,
            comment: '图表组件名称，如：RockPropertyCard, DrillCurveChartCard等'
          },
          config: {
            type: Sequelize.TEXT,
            allowNull: true,
            comment: '组件配置，JSON格式'
          },
          product_key: {
            type: Sequelize.STRING(8),
            allowNull: true,
            comment: '组件适配的设备所属产品类型：WPD、PDF、AD等'
          },
          description: {
            type: Sequelize.STRING(255),
            allowNull: true,
            comment: '组件描述'
          },
          status: {
            type: Sequelize.SMALLINT,
            allowNull: true,
            defaultValue: 1,
            comment: '状态：0-禁用，1-启用'
          }
        },
        {
          comment: '图表组件信息表'
        }
      )

      console.log('dh_chart_component表创建成功')
    } catch (error) {
      console.error('创建dh_chart_component表时出错:', error)
      throw error
    }
  },
  
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('dh_chart_component')
  }
} 
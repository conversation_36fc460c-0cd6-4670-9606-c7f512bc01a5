'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable(
      'dh_user',
      {
        id: {
          allowNull: false,
          autoIncrement: true,
          primaryKey: true,
          type: Sequelize.INTEGER,
          comment: '主键ID，自增'
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('NOW()'),
          comment: '设备创建时间'
        },
        modified_at: {
          type: Sequelize.DATE,
          allowNull: true,
          comment: '设备信息最后修改时间'
        },
        username: {
          type: Sequelize.STRING,
          allowNull: false,
          unique: true,
          comment: '用户名'
        },
        account: {
          type: Sequelize.STRING,
          allowNull: false,
          unique: true,
          comment: '账号'
        },
        password: {
          type: Sequelize.STRING,
          allowNull: false,
          comment: '密码'
        },
        role: {
          type: Sequelize.ENUM('admin', 'user'),
          defaultValue: 'user',
          comment: '用户角色 admin | user '
        },
        token: {
          type: Sequelize.STRING,
          allowNull: true,
          comment: 'token'
        }
      },
      {
        comment: '用户信息表'
      }
    )
  },
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('dh_user')
  }
}

'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable(
      'dh_arithmetic',
      {
        id: {
          allowNull: false,
          autoIncrement: true,
          primaryKey: true,
          type: Sequelize.INTEGER,
          comment: '主键ID，自增'
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('NOW()'),
          comment: '记录创建时间'
        },
        modified_at: {
          type: Sequelize.DATE,
          allowNull: true,
          comment: '记录最后修改时间'
        },
        type: {
          type: Sequelize.SMALLINT,
          allowNull: true,
          defaultValue: 0,
          comment: '算法类型：0-清洗算法，1-分析算法'
        },
        product_key: {
          type: Sequelize.STRING(8),
          allowNull: true,
          comment: '算法适配的设备所属产品类型：WPD、PDF、AD'
        },
        name: {
          type: Sequelize.STRING(32),
          allowNull: true,
          comment: '算法名称'
        },
        oss_path: {
          type: Sequelize.STRING(255),
          allowNull: true,
          comment: '算法文件存储路径，阿里云oss'
        },
        content: {
          type: Sequelize.TEXT,
          allowNull: true,
          comment: '算法内容'
        },
        md5: {
          type: Sequelize.STRING(32),
          allowNull: true,
          comment: '算法内容md5'
        },
        is_default: {
          type: Sequelize.BOOLEAN,
          allowNull: true,
          defaultValue: false,
          comment: '是否为默认算法，一个类型只能有一个默认算法'
        }
      },
      {
        comment: '算法数据表'
      }
    )
  },
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('dh_arithmetic')
  }
}

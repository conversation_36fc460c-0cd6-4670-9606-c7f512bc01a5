'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable(
      'dh_device',
      {
        id: {
          allowNull: false,
          autoIncrement: true,
          primaryKey: true,
          type: Sequelize.INTEGER,
          comment: '主键ID，自增'
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('NOW()'),
          comment: '设备创建时间'
        },
        modified_at: {
          type: Sequelize.DATE,
          allowNull: true,
          comment: '设备信息最后修改时间'
        },
        product_key: {
          type: Sequelize.STRING(8),
          allowNull: true,
          comment: '设备所属产品类型：WPD、PDF、AD'
        },
        device_name: {
          type: Sequelize.STRING(32),
          allowNull: true,
          comment: '设备名称'
        },
        device_sn: {
          type: Sequelize.STRING(32),
          allowNull: true,
          unique: true,
          comment: '设备SN号'
        },
        device_secret: {
          type: Sequelize.STRING(10),
          allowNull: true,
          comment: '设备密钥，10位随机码；AES解密密钥=device_mac后6位+device_secret'
        },
        device_mac: {
          type: Sequelize.STRING(32),
          allowNull: true,
          comment: '设备Mac地址'
        },
        project_name: {
          type: Sequelize.STRING(32),
          allowNull: true,
          comment: '设备所属项目名称'
        },
        creation_method: {
          type: Sequelize.SMALLINT,
          allowNull: true,
          defaultValue: 1,
          comment: '设备创建方式：0-自动创建，1-生产导入'
        },
        import_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: '生产导入批次ID'
        }
      },
      {
        comment: '设备信息数据表'
      }
    )
  },
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('dh_device')
  }
}

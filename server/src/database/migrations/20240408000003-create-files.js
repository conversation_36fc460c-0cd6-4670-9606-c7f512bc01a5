'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable(
      'dh_file',
      {
        id: {
          allowNull: false,
          autoIncrement: true,
          primaryKey: true,
          type: Sequelize.INTEGER,
          comment: '主键ID，自增'
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('NOW()'),
          comment: '记录创建时间'
        },
        modified_at: {
          type: Sequelize.DATE,
          allowNull: true,
          comment: '记录最后修改时间'
        },
        file_name: {
          type: Sequelize.STRING(32),
          allowNull: true,
          comment: '文件名称'
        },
        file_size: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: '文件大小（字节）'
        },
        data_rows: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: '文件行数（解析后填入）'
        },
        first_data_time: {
          type: Sequelize.DATE,
          allowNull: true,
          comment: '数据最早时间'
        },
        last_data_time: {
          type: Sequelize.DATE,
          allowNull: true,
          comment: '数据最晚时间'
        },
        local_path: {
          type: Sequelize.STRING(255),
          allowNull: true,
          comment: '本地存储路径'
        },
        oss_path: {
          type: Sequelize.STRING(255),
          allowNull: true,
          comment: '云存储路径（如OSS）'
        },
        is_decrypted: {
          type: Sequelize.BOOLEAN,
          allowNull: true,
          defaultValue: false,
          comment: '是否已解密 (true=已解密)'
        },
        device_sn: {
          type: Sequelize.STRING(32),
          allowNull: true,
          comment: '设备序列号'
        },
        project_name: {
          type: Sequelize.STRING(32),
          allowNull: true,
          comment: '所属项目名称'
        },
        tunnel_section_no: {
          type: Sequelize.STRING(32),
          allowNull: true,
          comment: '隧道段标识码'
        },
        hole_no: {
          type: Sequelize.STRING(32),
          allowNull: true,
          comment: '孔号（地质编号）'
        },
        pad_mac: {
          type: Sequelize.STRING(32),
          allowNull: true,
          comment: '上传文件的Pad MAC地址'
        }
      },
      {
        comment: '文件元数据表'
      }
    )
  },
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('dh_file')
  }
}

import { Request, Response, NextFunction } from 'express'
import fs from 'fs'
import path from 'path'
import { AppError } from './error'

/**
 * SQL安全事件类型
 */
export enum SqlSecurityEventType {
  SQL_INJECTION_ATTEMPT = 'SQL_INJECTION_ATTEMPT',
  SUSPICIOUS_QUERY = 'SUSPICIOUS_QUERY',
  INVALID_TABLE_ACCESS = 'INVALID_TABLE_ACCESS',
  PARAMETER_VALIDATION_FAILED = 'PARAMETER_VALIDATION_FAILED',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED'
}

/**
 * SQL安全事件接口
 */
export interface SqlSecurityEvent {
  id: string
  timestamp: string
  type: SqlSecurityEventType
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  ip: string
  userAgent: string
  url: string
  method: string
  userId?: string
  deviceSn?: string
  details: {
    violations: string[]
    blockedPatterns?: string[]
    requestData?: any
    stackTrace?: string
  }
  action: 'BLOCKED' | 'LOGGED' | 'ALLOWED'
}

/**
 * SQL安全监控器类
 */
export class SqlSecurityMonitor {
  private logDir: string
  private eventQueue: SqlSecurityEvent[] = []
  private rateLimitMap = new Map<string, { count: number; resetTime: number }>()
  
  // 配置参数
  private readonly MAX_EVENTS_PER_IP_PER_MINUTE = 10
  private readonly LOG_FLUSH_INTERVAL = 5000 // 5秒
  private readonly MAX_LOG_FILE_SIZE = 10 * 1024 * 1024 // 10MB

  constructor(logDir: string = './logs/security') {
    this.logDir = logDir
    this.ensureLogDirectory()
    this.startLogFlusher()
  }

  /**
   * 确保日志目录存在
   */
  private ensureLogDirectory(): void {
    if (!fs.existsSync(this.logDir)) {
      fs.mkdirSync(this.logDir, { recursive: true })
    }
  }

  /**
   * 生成事件ID
   */
  private generateEventId(): string {
    return `sql_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 检查IP速率限制
   */
  private checkRateLimit(ip: string): boolean {
    const now = Date.now()
    const key = ip
    const limit = this.rateLimitMap.get(key)

    if (!limit || now > limit.resetTime) {
      // 重置或创建新的限制记录
      this.rateLimitMap.set(key, {
        count: 1,
        resetTime: now + 60000 // 1分钟后重置
      })
      return true
    }

    if (limit.count >= this.MAX_EVENTS_PER_IP_PER_MINUTE) {
      return false
    }

    limit.count++
    return true
  }

  /**
   * 记录安全事件
   */
  public logSecurityEvent(
    type: SqlSecurityEventType,
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL',
    req: Request,
    details: SqlSecurityEvent['details'],
    action: 'BLOCKED' | 'LOGGED' | 'ALLOWED' = 'LOGGED'
  ): void {
    const ip = req.ip || req.connection.remoteAddress || 'unknown'
    
    // 检查速率限制
    if (!this.checkRateLimit(ip)) {
      // 如果超过速率限制，记录一个特殊事件
      this.logSecurityEvent(
        SqlSecurityEventType.RATE_LIMIT_EXCEEDED,
        'HIGH',
        req,
        { violations: ['IP超过安全事件记录速率限制'] },
        'BLOCKED'
      )
      return
    }

    const event: SqlSecurityEvent = {
      id: this.generateEventId(),
      timestamp: new Date().toISOString(),
      type,
      severity,
      ip,
      userAgent: req.get('User-Agent') || 'unknown',
      url: req.url,
      method: req.method,
      userId: (req as any).user?.id,
      deviceSn: req.params?.deviceSn || req.body?.deviceSn,
      details,
      action
    }

    // 添加到队列
    this.eventQueue.push(event)

    // 如果是高危事件，立即写入日志
    if (severity === 'CRITICAL' || severity === 'HIGH') {
      this.flushLogs()
    }

    // 控制台输出（开发环境）
    if (process.env.NODE_ENV === 'development') {
      console.warn(`[SQL安全监控] ${severity} - ${type}:`, {
        ip,
        url: req.url,
        violations: details.violations
      })
    }
  }

  /**
   * 刷新日志到文件
   */
  private flushLogs(): void {
    if (this.eventQueue.length === 0) {
      return
    }

    const today = new Date().toISOString().split('T')[0]
    const logFile = path.join(this.logDir, `sql-security-${today}.log`)

    try {
      // 检查文件大小
      if (fs.existsSync(logFile)) {
        const stats = fs.statSync(logFile)
        if (stats.size > this.MAX_LOG_FILE_SIZE) {
          // 文件过大，创建新文件
          const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
          const archiveFile = path.join(this.logDir, `sql-security-${today}-${timestamp}.log`)
          fs.renameSync(logFile, archiveFile)
        }
      }

      // 写入日志
      const logEntries = this.eventQueue.map(event => JSON.stringify(event)).join('\n') + '\n'
      fs.appendFileSync(logFile, logEntries, 'utf8')

      // 清空队列
      this.eventQueue = []
    } catch (error) {
      console.error('写入SQL安全日志失败:', error)
    }
  }

  /**
   * 启动日志刷新器
   */
  private startLogFlusher(): void {
    setInterval(() => {
      this.flushLogs()
    }, this.LOG_FLUSH_INTERVAL)
  }

  /**
   * 获取安全统计信息
   */
  public getSecurityStats(): {
    queueSize: number
    rateLimitEntries: number
    logDirectory: string
  } {
    return {
      queueSize: this.eventQueue.length,
      rateLimitEntries: this.rateLimitMap.size,
      logDirectory: this.logDir
    }
  }

  /**
   * 清理过期的速率限制记录
   */
  public cleanupRateLimits(): void {
    const now = Date.now()
    for (const [key, limit] of this.rateLimitMap.entries()) {
      if (now > limit.resetTime) {
        this.rateLimitMap.delete(key)
      }
    }
  }
}

// 创建全局监控器实例
export const sqlSecurityMonitor = new SqlSecurityMonitor()

/**
 * SQL安全监控中间件
 */
export const sqlSecurityMiddleware = (req: Request, res: Response, next: NextFunction): void => {
  // 在请求开始时记录
  const startTime = Date.now()
  
  // 监听响应结束事件
  res.on('finish', () => {
    const duration = Date.now() - startTime
    
    // 如果响应时间过长，可能是SQL注入攻击
    if (duration > 10000) { // 10秒
      sqlSecurityMonitor.logSecurityEvent(
        SqlSecurityEventType.SUSPICIOUS_QUERY,
        'MEDIUM',
        req,
        {
          violations: [`查询执行时间过长: ${duration}ms`],
          requestData: {
            params: req.params,
            query: req.query,
            body: req.body
          }
        },
        'LOGGED'
      )
    }
  })

  next()
}

// 定期清理速率限制记录
setInterval(() => {
  sqlSecurityMonitor.cleanupRateLimits()
}, 60000) // 每分钟清理一次

export default sqlSecurityMonitor

import { Response, NextFunction } from 'express'
import { AuthRequest } from '../types/middleware'
import { logger } from './logger'

/**
 * 通用缓存失效中间件
 * 在数据更新操作成功后自动清除相关缓存
 * @param categories 需要清除的缓存类别数组
 * @returns Express中间件函数
 */
export const cacheInvalidationMiddleware = (categories: string[]) => {
  return (req: AuthRequest, res: Response, next: NextFunction) => {

    const originalJson = res.json
    const originalSend = res.send

    // 缓存清除函数
    const clearCache = (data: any) => {
      if (data && data.success) {
        const userId = req.user?.id
        if (userId && global.cacheManager) {
          categories.forEach(category => {
            global.cacheManager.clearByPattern(`${category}:${userId}`)
          })
          logger.info(`已清除用户 ${userId} 的缓存类别: ${categories.join(', ')}`)
        }
      }
    }

    // 拦截 res.json
    res.json = function(data) {
      clearCache(data)
      return originalJson.call(this, data)
    }

    // 拦截 res.send (以防某些情况下使用 send 而不是 json)
    res.send = function(data) {
      try {
        const parsedData = typeof data === 'string' ? JSON.parse(data) : data
        clearCache(parsedData)
      } catch (e) {
        // 如果不是JSON格式，忽略缓存清除
      }
      return originalSend.call(this, data)
    }

    next()
  }
}

/**
 * 预定义的缓存失效中间件实例
 */

// 设备缓存失效中间件
export const deviceCacheInvalidation = cacheInvalidationMiddleware(['device'])

// 算法缓存失效中间件
export const algorithmCacheInvalidation = cacheInvalidationMiddleware(['algorithm'])

// 模板缓存失效中间件
export const templateCacheInvalidation = cacheInvalidationMiddleware(['template'])

// 字段映射缓存失效中间件
export const fieldMappingCacheInvalidation = cacheInvalidationMiddleware(['field_mapping'])

// 文件缓存失效中间件
export const fileCacheInvalidation = cacheInvalidationMiddleware(['file'])

// 导入缓存失效中间件
export const importCacheInvalidation = cacheInvalidationMiddleware(['import'])

/**
 * 多类别缓存失效中间件
 * 用于影响多个缓存类别的操作
 */

// 设备和算法缓存失效（设备算法关联操作）
export const deviceAlgorithmCacheInvalidation = cacheInvalidationMiddleware(['device', 'algorithm'])

// 设备和模板缓存失效（设备模板配置操作）
export const deviceTemplateCacheInvalidation = cacheInvalidationMiddleware(['device', 'template'])

// 全部缓存失效（影响所有缓存的操作）
export const allCacheInvalidation = cacheInvalidationMiddleware(['device', 'algorithm', 'template', 'field_mapping', 'file', 'import'])

/**
 * 条件缓存失效中间件
 * 根据条件决定是否清除缓存
 * @param categories 需要清除的缓存类别数组
 * @param condition 清除缓存的条件函数
 * @returns Express中间件函数
 */
export const conditionalCacheInvalidationMiddleware = (
  categories: string[], 
  condition: (req: AuthRequest, data: any) => boolean
) => {
  return (req: AuthRequest, res: Response, next: NextFunction) => {
    const originalJson = res.json
    
    res.json = function(data) {
      // 检查操作是否成功且满足条件
      if (data && data.success && condition(req, data)) {
        const userId = req.user?.id
        if (userId && global.cacheManager) {
          categories.forEach(category => {
            global.cacheManager.clearByPattern(`${category}:${userId}`)
          })
          logger.info(`条件缓存失效 - 已清除用户 ${userId} 的缓存类别: ${categories.join(', ')}`)
        }
      }
      
      return originalJson.call(this, data)
    }
    
    next()
  }
}

/**
 * 延迟缓存失效中间件
 * 在指定延迟后清除缓存，用于处理异步操作
 * @param categories 需要清除的缓存类别数组
 * @param delay 延迟时间(毫秒)
 * @returns Express中间件函数
 */
export const delayedCacheInvalidationMiddleware = (categories: string[], delay: number = 1000) => {
  return (req: AuthRequest, res: Response, next: NextFunction) => {
    const originalJson = res.json

    res.json = function(data) {
      // 智能判断是否需要清除缓存
      let shouldClearCache = false

      if (data && data.success) {
        // 检查是否有批量操作结果
        if (data.data && typeof data.data.success === 'number') {
          // 批量操作：只有在有成功记录时才清除缓存
          shouldClearCache = data.data.success > 0

          if (shouldClearCache) {
            logger.info(`批量操作部分/全部成功 (${data.data.success}/${data.data.total})，将清除缓存`)
          } else {
            logger.info(`批量操作全部失败 (0/${data.data.total})，跳过缓存清除`)
          }
        } else {
          // 非批量操作：按原逻辑处理
          shouldClearCache = true
        }
      }

      if (shouldClearCache) {
        const userId = req.user?.id
        if (userId && global.cacheManager) {
          setTimeout(() => {
            categories.forEach(category => {
              global.cacheManager.clearByPattern(`${category}:${userId}`)
            })
            logger.info(`延迟缓存失效 - 已清除用户 ${userId} 的缓存类别: ${categories.join(', ')}`)
          }, delay)
        }
      }

      return originalJson.call(this, data)
    }

    next()
  }
}

/**
 * 手动缓存失效函数
 * 用于在业务逻辑中手动清除缓存
 * @param userId 用户ID（支持字符串和数字类型）
 * @param categories 需要清除的缓存类别数组
 */
export const invalidateCache = (userId: string | number, categories: string[]) => {
  if (global.cacheManager) {
    categories.forEach(category => {
      global.cacheManager.clearByPattern(`${category}:${userId}`)
    })
    logger.info(`手动缓存失效 - 已清除用户 ${userId} 的缓存类别: ${categories.join(', ')}`)
  }
}

/**
 * 批量用户缓存失效函数
 * 用于清除多个用户的指定类别缓存
 * @param userIds 用户ID数组（支持字符串和数字类型）
 * @param categories 需要清除的缓存类别数组
 */
export const invalidateCacheForUsers = (userIds: (string | number)[], categories: string[]) => {
  if (global.cacheManager) {
    userIds.forEach(userId => {
      categories.forEach(category => {
        global.cacheManager.clearByPattern(`${category}:${userId}`)
      })
    })
    logger.info(`批量缓存失效 - 已清除 ${userIds.length} 个用户的缓存类别: ${categories.join(', ')}`)
  }
}

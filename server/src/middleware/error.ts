import { Request, Response, NextFunction } from 'express'
import jwt from 'jsonwebtoken'

export class AppError extends Error {
  statusCode: number
  status: string
  isOperational: boolean
  code?: number

  constructor(message: string, statusCode: number, code?: number) {
    super(message)
    this.statusCode = statusCode
    this.status = `${statusCode}`.startsWith('4') ? 'fail' : 'error'
    this.isOperational = true
    this.code = code
    Error.captureStackTrace(this, this.constructor)
  }
}

export const errorHandler = (err: Error | AppError, req: Request, res: Response, next: NextFunction) => {
  // 记录错误日志（生产环境不暴露敏感信息）
  const isProduction = process.env.NODE_ENV === 'production'

  if (err instanceof AppError) {
    // 记录应用错误
    console.error(`AppError: ${err.message}`, {
      statusCode: err.statusCode,
      code: err.code,
      url: req.url,
      method: req.method,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    })

    return res.status(err.statusCode).json({
      status: err.status,
      message: err.message,
      code: err.code ?? -1
    })
  }

  if (err instanceof jwt.JsonWebTokenError) {
    console.error('JWT Error:', err.message, {
      url: req.url,
      method: req.method,
      ip: req.ip
    })

    return res.status(401).json({
      status: 'fail',
      message: '无效的认证令牌',
      code: -10086
    })
  }

  // 处理Sequelize数据库错误
  if (err.name === 'SequelizeValidationError') {
    return res.status(400).json({
      status: 'fail',
      message: '数据验证失败',
      code: -1
    })
  }

  if (err.name === 'SequelizeUniqueConstraintError') {
    return res.status(409).json({
      status: 'fail',
      message: '数据已存在',
      code: -1
    })
  }

  // 处理其他未知错误
  console.error('UNHANDLED ERROR 💥', {
    error: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    timestamp: new Date().toISOString()
  })

  return res.status(500).json({
    status: 'error',
    message: isProduction ? '服务器内部错误' : err.message,
    code: -1
  })
}

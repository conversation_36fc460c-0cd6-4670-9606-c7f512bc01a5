import { Request, Response, NextFunction } from 'express'
import { AppError } from './error'

/**
 * DDoS攻击类型
 */
export enum DDoSAttackType {
  VOLUME_ATTACK = 'VOLUME_ATTACK', // 流量型攻击
  PROTOCOL_ATTACK = 'PROTOCOL_ATTACK', // 协议型攻击
  APPLICATION_ATTACK = 'APPLICATION_ATTACK', // 应用层攻击
  SLOWLORIS = 'SLOWLORIS', // 慢速连接攻击
  HTTP_FLOOD = 'HTTP_FLOOD', // HTTP洪水攻击
  SUSPICIOUS_PATTERN = 'SUSPICIOUS_PATTERN' // 可疑模式
}

/**
 * DDoS防护配置
 */
export interface DDoSProtectionConfig {
  // 基础配置
  enabled: boolean
  
  // 流量阈值
  maxRequestsPerSecond: number // 每秒最大请求数
  maxRequestsPerMinute: number // 每分钟最大请求数
  maxConcurrentConnections: number // 最大并发连接数
  
  // 检测窗口
  detectionWindowMs: number // 检测时间窗口
  
  // 封禁配置
  banDurationMs: number // 封禁时长
  maxBanCount: number // 最大封禁次数
  
  // 白名单和黑名单
  whitelist: string[] // IP白名单
  blacklist: string[] // IP黑名单
  
  // 地理位置过滤
  allowedCountries?: string[] // 允许的国家代码
  blockedCountries?: string[] // 禁止的国家代码
  
  // 用户代理过滤
  suspiciousUserAgents: string[] // 可疑的用户代理
  
  // 回调函数
  onAttackDetected?: (attackInfo: DDoSAttackInfo) => void
  onIpBanned?: (ip: string, reason: string) => void
}

/**
 * DDoS攻击信息
 */
export interface DDoSAttackInfo {
  type: DDoSAttackType
  sourceIp: string
  requestCount: number
  timeWindow: number
  userAgent?: string
  url?: string
  method?: string
  timestamp: number
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
}

/**
 * IP统计信息
 */
interface IpStats {
  requestCount: number
  lastRequestTime: number
  firstRequestTime: number
  concurrentConnections: number
  bannedUntil?: number
  banCount: number
  requestTimes: number[]
  suspiciousActivity: string[]
  userAgents: Set<string>
  urls: Set<string>
  methods: Set<string>
}

/**
 * 获取默认配置（从统一配置读取）
 */
function getDefaultConfig(): DDoSProtectionConfig {
  try {
    const { securityConfig } = require('../config/security')
    return securityConfig.rateLimit.ddos
  } catch (error) {
    // 如果配置加载失败，使用最基本的默认值
    console.warn('Failed to load DDoS protection config, using fallback defaults')
    return {
      enabled: true,
      maxRequestsPerSecond: 10,
      maxRequestsPerMinute: 100,
      maxConcurrentConnections: 50,
      detectionWindowMs: 60000,
      banDurationMs: 15 * 60 * 1000,
      maxBanCount: 3,
      whitelist: ['127.0.0.1', '::1'],
      blacklist: [],
      suspiciousUserAgents: [
        'bot', 'crawler', 'spider', 'scraper', 'scanner',
        'curl', 'wget', 'python', 'java', 'go-http-client'
      ]
    }
  }
}

/**
 * DDoS防护系统
 */
export class DDoSProtectionSystem {
  private config: DDoSProtectionConfig
  private ipStats = new Map<string, IpStats>()
  private cleanupInterval?: NodeJS.Timeout
  private activeConnections = new Map<string, number>()

  constructor(config: Partial<DDoSProtectionConfig> = {}) {
    this.config = { ...getDefaultConfig(), ...config }
    
    // 启动清理定时器
    this.cleanupInterval = setInterval(() => {
      this.cleanup()
    }, this.config.detectionWindowMs)
  }

  /**
   * 获取客户端IP
   */
  private getClientIp(req: Request): string {
    return req.ip || 
           req.connection.remoteAddress || 
           req.socket.remoteAddress ||
           (req.headers['x-forwarded-for'] as string)?.split(',')[0]?.trim() ||
           'unknown'
  }

  /**
   * 检查IP是否在白名单中
   */
  private isWhitelisted(ip: string): boolean {
    return this.config.whitelist.includes(ip)
  }

  /**
   * 检查IP是否在黑名单中
   */
  private isBlacklisted(ip: string): boolean {
    return this.config.blacklist.includes(ip)
  }

  /**
   * 检查IP是否被封禁
   */
  private isBanned(ip: string): boolean {
    const stats = this.ipStats.get(ip)
    if (!stats || !stats.bannedUntil) {
      return false
    }
    
    const now = Date.now()
    if (stats.bannedUntil > now) {
      return true
    }
    
    // 解除过期的封禁
    delete stats.bannedUntil
    return false
  }

  /**
   * 封禁IP
   */
  private banIp(ip: string, reason: string, attackType: DDoSAttackType): void {
    let stats = this.ipStats.get(ip)
    if (!stats) {
      stats = this.createIpStats()
      this.ipStats.set(ip, stats)
    }

    stats.banCount++
    stats.bannedUntil = Date.now() + this.config.banDurationMs * Math.pow(2, stats.banCount - 1) // 指数退避

    console.warn(`IP ${ip} 已被封禁`, {
      reason,
      attackType,
      banCount: stats.banCount,
      bannedUntil: new Date(stats.bannedUntil).toISOString()
    })

    // 触发回调
    if (this.config.onIpBanned) {
      this.config.onIpBanned(ip, reason)
    }
  }

  /**
   * 创建IP统计信息
   */
  private createIpStats(): IpStats {
    return {
      requestCount: 0,
      lastRequestTime: Date.now(),
      firstRequestTime: Date.now(),
      concurrentConnections: 0,
      banCount: 0,
      requestTimes: [],
      suspiciousActivity: [],
      userAgents: new Set(),
      urls: new Set(),
      methods: new Set()
    }
  }

  /**
   * 检测流量型攻击
   */
  private detectVolumeAttack(ip: string, stats: IpStats): DDoSAttackInfo | null {
    const now = Date.now()
    const windowStart = now - this.config.detectionWindowMs

    // 清理过期的请求时间
    stats.requestTimes = stats.requestTimes.filter(time => time > windowStart)

    // 检查每秒请求数
    const lastSecond = now - 1000
    const requestsInLastSecond = stats.requestTimes.filter(time => time > lastSecond).length
    
    if (requestsInLastSecond > this.config.maxRequestsPerSecond) {
      return {
        type: DDoSAttackType.VOLUME_ATTACK,
        sourceIp: ip,
        requestCount: requestsInLastSecond,
        timeWindow: 1000,
        timestamp: now,
        severity: 'HIGH'
      }
    }

    // 检查每分钟请求数
    if (stats.requestTimes.length > this.config.maxRequestsPerMinute) {
      return {
        type: DDoSAttackType.HTTP_FLOOD,
        sourceIp: ip,
        requestCount: stats.requestTimes.length,
        timeWindow: this.config.detectionWindowMs,
        timestamp: now,
        severity: 'CRITICAL'
      }
    }

    return null
  }

  /**
   * 检测应用层攻击
   */
  private detectApplicationAttack(ip: string, stats: IpStats, req: Request): DDoSAttackInfo | null {
    const now = Date.now()
    const userAgent = req.get('User-Agent') || ''

    // 检查可疑的用户代理
    const isSuspiciousUA = this.config.suspiciousUserAgents.some(pattern => 
      userAgent.toLowerCase().includes(pattern.toLowerCase())
    )

    if (isSuspiciousUA) {
      stats.suspiciousActivity.push(`Suspicious User-Agent: ${userAgent}`)
      
      return {
        type: DDoSAttackType.APPLICATION_ATTACK,
        sourceIp: ip,
        requestCount: stats.requestCount,
        timeWindow: this.config.detectionWindowMs,
        userAgent,
        url: req.url,
        method: req.method,
        timestamp: now,
        severity: 'MEDIUM'
      }
    }

    // 检查请求模式异常
    stats.userAgents.add(userAgent)
    stats.urls.add(req.url)
    stats.methods.add(req.method)

    // 如果同一IP使用了过多不同的用户代理
    if (stats.userAgents.size > 10) {
      return {
        type: DDoSAttackType.SUSPICIOUS_PATTERN,
        sourceIp: ip,
        requestCount: stats.requestCount,
        timeWindow: this.config.detectionWindowMs,
        timestamp: now,
        severity: 'HIGH'
      }
    }

    // 检查URL访问模式
    if (stats.urls.size > 50) {
      return {
        type: DDoSAttackType.SUSPICIOUS_PATTERN,
        sourceIp: ip,
        requestCount: stats.requestCount,
        timeWindow: this.config.detectionWindowMs,
        timestamp: now,
        severity: 'MEDIUM'
      }
    }

    return null
  }

  /**
   * 检测慢速连接攻击
   */
  private detectSlowlorisAttack(ip: string, stats: IpStats): DDoSAttackInfo | null {
    if (stats.concurrentConnections > this.config.maxConcurrentConnections) {
      return {
        type: DDoSAttackType.SLOWLORIS,
        sourceIp: ip,
        requestCount: stats.concurrentConnections,
        timeWindow: this.config.detectionWindowMs,
        timestamp: Date.now(),
        severity: 'HIGH'
      }
    }

    return null
  }

  /**
   * 分析请求并检测攻击
   */
  public analyzeRequest(req: Request): { allowed: boolean; attackInfo?: DDoSAttackInfo } {
    if (!this.config.enabled) {
      return { allowed: true }
    }

    const ip = this.getClientIp(req)
    const now = Date.now()

    // 检查白名单
    if (this.isWhitelisted(ip)) {
      return { allowed: true }
    }

    // 检查黑名单
    if (this.isBlacklisted(ip)) {
      return { 
        allowed: false, 
        attackInfo: {
          type: DDoSAttackType.SUSPICIOUS_PATTERN,
          sourceIp: ip,
          requestCount: 0,
          timeWindow: 0,
          timestamp: now,
          severity: 'CRITICAL'
        }
      }
    }

    // 检查是否被封禁
    if (this.isBanned(ip)) {
      return { allowed: false }
    }

    // 获取或创建IP统计信息
    let stats = this.ipStats.get(ip)
    if (!stats) {
      stats = this.createIpStats()
      this.ipStats.set(ip, stats)
    }

    // 更新统计信息
    stats.requestCount++
    stats.lastRequestTime = now
    stats.requestTimes.push(now)
    stats.concurrentConnections++

    // 检测各种类型的攻击
    let attackInfo: DDoSAttackInfo | null = null

    // 1. 检测流量型攻击
    attackInfo = this.detectVolumeAttack(ip, stats)
    if (attackInfo) {
      this.banIp(ip, `Volume attack detected: ${attackInfo.requestCount} requests`, attackInfo.type)
      if (this.config.onAttackDetected) {
        this.config.onAttackDetected(attackInfo)
      }
      return { allowed: false, attackInfo }
    }

    // 2. 检测应用层攻击
    attackInfo = this.detectApplicationAttack(ip, stats, req)
    if (attackInfo && attackInfo.severity === 'HIGH') {
      this.banIp(ip, `Application layer attack detected`, attackInfo.type)
      if (this.config.onAttackDetected) {
        this.config.onAttackDetected(attackInfo)
      }
      return { allowed: false, attackInfo }
    }

    // 3. 检测慢速连接攻击
    attackInfo = this.detectSlowlorisAttack(ip, stats)
    if (attackInfo) {
      this.banIp(ip, `Slowloris attack detected: ${attackInfo.requestCount} connections`, attackInfo.type)
      if (this.config.onAttackDetected) {
        this.config.onAttackDetected(attackInfo)
      }
      return { allowed: false, attackInfo }
    }

    // 如果检测到中低风险攻击，记录但不封禁
    if (attackInfo && this.config.onAttackDetected) {
      this.config.onAttackDetected(attackInfo)
    }

    return { allowed: true, attackInfo: attackInfo || undefined }
  }

  /**
   * 请求完成时调用
   */
  public onRequestComplete(req: Request): void {
    const ip = this.getClientIp(req)
    const stats = this.ipStats.get(ip)
    
    if (stats && stats.concurrentConnections > 0) {
      stats.concurrentConnections--
    }
  }

  /**
   * 创建中间件
   */
  public middleware() {
    return (req: Request, res: Response, next: NextFunction): void => {
      try {
        const result = this.analyzeRequest(req)

        if (!result.allowed) {
          const ip = this.getClientIp(req)
          
          console.warn(`DDoS protection blocked request from ${ip}`, {
            url: req.url,
            method: req.method,
            userAgent: req.get('User-Agent'),
            attackInfo: result.attackInfo
          })

          // 设置响应头
          res.set({
            'X-DDoS-Protection': 'blocked',
            'X-Block-Reason': result.attackInfo?.type || 'suspicious_activity'
          })

          throw new AppError('请求被DDoS防护系统阻止', 429)
        }

        // 监听响应完成事件
        res.on('finish', () => {
          this.onRequestComplete(req)
        })

        next()
      } catch (error) {
        next(error)
      }
    }
  }

  /**
   * 清理过期数据
   */
  private cleanup(): void {
    const now = Date.now()
    const windowStart = now - this.config.detectionWindowMs
    const keysToDelete: string[] = []

    for (const [ip, stats] of this.ipStats.entries()) {
      // 清理过期的请求时间
      stats.requestTimes = stats.requestTimes.filter(time => time > windowStart)
      
      // 清理过期的可疑活动记录
      stats.suspiciousActivity = stats.suspiciousActivity.slice(-10) // 只保留最近10条
      
      // 如果IP长时间没有活动且未被封禁，删除记录
      if (stats.lastRequestTime < windowStart && !stats.bannedUntil) {
        keysToDelete.push(ip)
      }
      
      // 清理过期的用户代理和URL记录
      if (stats.userAgents.size > 100) {
        stats.userAgents.clear()
      }
      if (stats.urls.size > 200) {
        stats.urls.clear()
      }
    }

    keysToDelete.forEach(ip => this.ipStats.delete(ip))
    
    if (keysToDelete.length > 0) {
      console.log(`DDoS Protection: Cleaned up ${keysToDelete.length} expired IP records`)
    }
  }

  /**
   * 获取统计信息
   */
  public getStats(): {
    totalIps: number
    bannedIps: number
    activeConnections: number
    config: DDoSProtectionConfig
  } {
    const bannedIps = Array.from(this.ipStats.values()).filter(stats => 
      stats.bannedUntil && stats.bannedUntil > Date.now()
    ).length

    const activeConnections = Array.from(this.ipStats.values()).reduce((sum, stats) => 
      sum + stats.concurrentConnections, 0
    )

    return {
      totalIps: this.ipStats.size,
      bannedIps,
      activeConnections,
      config: this.config
    }
  }

  /**
   * 手动解封IP
   */
  public unbanIp(ip: string): boolean {
    const stats = this.ipStats.get(ip)
    if (stats && stats.bannedUntil) {
      delete stats.bannedUntil
      console.log(`IP ${ip} has been manually unbanned`)
      return true
    }
    return false
  }

  /**
   * 销毁防护系统
   */
  public destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
    }
    this.ipStats.clear()
    this.activeConnections.clear()
  }
}

/**
 * 创建DDoS防护中间件的便捷函数
 */
export const createDDoSProtection = (config: Partial<DDoSProtectionConfig> = {}) => {
  const protection = new DDoSProtectionSystem(config)
  return protection.middleware()
}

export default DDoSProtectionSystem

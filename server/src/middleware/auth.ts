import jwt from 'jsonwebtoken'
import { Response, NextFunction } from 'express'
import { AuthRequest, AuthMiddleware, JwtPayload } from '../types/middleware'
import config from '../config'
import User from '../models/user'
import { AppError } from '../middleware/error'
import { tokenBlacklist } from '../utils/tokenBlacklist'

/**
 * 验证 JWT 令牌的中间件（增强版，支持Token刷新和黑名单检查）
 */
export const verifyToken: AuthMiddleware = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  // 优先从Cookie获取accessToken，其次从Authorization头获取（向后兼容）
  const accessToken = req.cookies?.accessToken || req.cookies?.token || req.headers['authorization']?.split(' ')[1]

  // 如果没有token，直接返回错误
  if (!accessToken) {
    return next(new AppError('未提供认证令牌，请登录后重试', 401, -10086))
  }

  // 验证token格式（基本长度检查）
  if (accessToken.length < 20) {
    return next(new AppError('令牌格式错误', 401, -10086))
  }

  // 检查Token是否在黑名单中
  if (tokenBlacklist.isBlacklisted(accessToken)) {
    return next(new AppError('令牌已失效，请重新登录', 401, -10086))
  }

  try {
    // 验证token并获取解码后的payload
    const decoded = jwt.verify(accessToken, config.jwtSecret, {
      algorithms: ['HS256'],
      issuer: 'web-panel',
      audience: 'web-panel-client'
    }) as JwtPayload

    // 检查token是否过期
    if (decoded.exp && decoded.exp < Math.floor(Date.now() / 1000)) {
      return next(new AppError('令牌已过期', 401, -10086))
    }

    // 检查是否有用户ID
    if (!decoded.id) {
      return next(new AppError('令牌格式错误', 401, -10086))
    }

    // 检查是否为刷新令牌（刷新令牌不能用于API访问）
    if ((decoded as any).type === 'refresh') {
      return next(new AppError('无效的令牌类型', 401, -10086))
    }

    try {
      // 优化：使用缓存验证用户记录，减少数据库查询
      // 使用用户ID作为缓存键，避免token变化导致缓存失效
      const userCacheKey = `auth:user:${decoded.id}`
      let cachedUser = null

      if (global.cacheManager) {
        cachedUser = await global.cacheManager.get(userCacheKey)
      }

      // 检查缓存的用户信息是否与当前token匹配
      const isTestClient = req.get('User-Agent')?.includes('TestClient')
      if (cachedUser && cachedUser.token === accessToken) {
        // 缓存命中且token匹配，直接使用缓存
        if (!isTestClient) {
          console.log(`🎯 认证缓存命中: 用户${decoded.id}`)
        }
      } else {
        // 缓存未命中或token不匹配，查询数据库
        if (!isTestClient) {
          console.log(`💾 认证缓存${cachedUser ? 'token不匹配' : '未命中'}: 用户${decoded.id}`)
        }

        const user = await User.findOne({
          where: {
            id: parseInt(decoded.id, 10),
            token: accessToken
          }
        })

        if (!user) {
          return next(new AppError('令牌已失效', 401, -10086))
        }

        // 将用户信息缓存2分钟（较短时间，平衡性能和安全性）
        if (global.cacheManager) {
          await global.cacheManager.set(userCacheKey, user, 120)
          if (!isTestClient) {
            console.log(`✅ 认证缓存已更新: 用户${decoded.id}`)
          }
        }
      }

      // 验证通过，将用户信息添加到请求对象
      req.user = decoded
      next()
    } catch (dbError) {
      // 数据库查询错误
      console.error('数据库查询失败:', dbError)
      return next(new AppError('验证令牌时发生错误', 500, -1))
    }
  } catch (jwtError) {
    // JWT验证失败
    if (jwtError instanceof jwt.JsonWebTokenError) {
      const message =
        jwtError.message === 'jwt expired'
          ? '令牌已过期'
          : jwtError.message === 'invalid signature'
            ? '无效的令牌签名'
            : jwtError.message === 'jwt malformed'
              ? '令牌格式错误'
              : '无效的认证令牌'
      return next(new AppError(message, 401, -10086))
    }

    // 其他未知错误
    console.error('验证token时发生未知错误:', jwtError)
    return next(new AppError('验证令牌时发生错误', 500, -1))
  }
}

/**
 * 检查用户是否为管理员的中间件
 */
export const isAdmin: AuthMiddleware = (
  req: AuthRequest,
  res: Response,
  next: NextFunction
): void => {
  try {
    if (!req.user) {
      return next(new AppError('未认证', 401, -10086))
    }

    if (req.user.role !== 'admin') {
      return next(new AppError('需要管理员权限', 403, -1))
    }

    next()
  } catch (error) {
    console.error('权限检查失败:', error)
    next(new AppError('权限检查失败', 500, -1))
  }
}

/**
 * 可选的Token自动刷新中间件
 * 当访问令牌即将过期时，自动尝试刷新
 */
export const autoRefreshToken: AuthMiddleware = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const accessToken = req.cookies?.accessToken
    const refreshToken = req.cookies?.refreshToken

    if (!accessToken || !refreshToken) {
      return next()
    }

    try {
      // 解码访问令牌检查过期时间
      const decoded = jwt.decode(accessToken) as any

      if (decoded && decoded.exp) {
        const now = Math.floor(Date.now() / 1000)
        const timeUntilExpiry = decoded.exp - now

        // 如果令牌在5分钟内过期，设置刷新提示
        if (timeUntilExpiry < 300 && timeUntilExpiry > 0) {
          res.setHeader('X-Token-Refresh-Needed', 'true')
        }
      }
    } catch (error) {
      // 解码失败，忽略错误继续处理
      console.warn('Token解码失败:', error)
    }

    next()
  } catch (error) {
    console.error('自动刷新Token检查失败:', error)
    next() // 不阻止请求继续
  }
}

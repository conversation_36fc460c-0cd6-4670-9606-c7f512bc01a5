import { Request, Response, NextFunction } from 'express'
import { AppError } from './error'
import { sqlSecurityMonitor, SqlSecurityEventType } from './sqlSecurityMonitor'

/**
 * SQL注入攻击检测结果接口
 */
interface SqlInjectionDetectionResult {
  isSafe: boolean
  violations: string[]
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  blockedPatterns: string[]
}

/**
 * SQL注入防护中间件类
 */
export class SqlInjectionProtector {
  // SQL注入危险模式 - 使用更严格的检测
  private readonly CRITICAL_SQL_PATTERNS = [
    // 经典SQL注入模式
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|EXECUTE)\b.*\b(FROM|INTO|SET|WHERE|VALUES)\b)/gi,
    
    // UNION注入
    /\b(UNION\s+(ALL\s+)?SELECT)\b/gi,
    
    // 注释注入
    /(--|\#|\/\*|\*\/)/g,
    
    // 字符串终止和拼接
    /('|\")(\s*;\s*|\s*\|\|\s*|\s*\+\s*)('|\")/g,
    
    // 布尔盲注
    /\b(AND|OR)\s+\d+\s*=\s*\d+/gi,
    /\b(AND|OR)\s+('|\")\w+('|\")\s*=\s*('|\")\w+('|\")/gi,
    
    // 时间盲注
    /\b(SLEEP|WAITFOR|DELAY)\s*\(/gi,
    /\b(BENCHMARK|pg_sleep)\s*\(/gi,
    
    // 信息泄露
    /\b(INFORMATION_SCHEMA|SYSOBJECTS|SYSCOLUMNS|pg_tables)\b/gi,
    
    // 存储过程执行
    /\b(xp_|sp_|fn_)/gi,
    
    // 堆叠查询
    /;\s*(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER)/gi
  ]

  // 中等风险模式
  private readonly MEDIUM_SQL_PATTERNS = [
    // 单引号和双引号
    /['"]/g,
    
    // SQL函数
    /\b(COUNT|SUM|AVG|MIN|MAX|CONCAT|SUBSTRING|CHAR|ASCII)\s*\(/gi,
    
    // 条件语句
    /\b(CASE|WHEN|THEN|ELSE|END|IF|IFNULL|ISNULL)\b/gi,
    
    // 比较操作符
    /\b(LIKE|RLIKE|REGEXP|BETWEEN|IN|EXISTS)\b/gi
  ]

  // 数据库标识符白名单模式
  private readonly VALID_IDENTIFIER_PATTERN = /^[a-zA-Z][a-zA-Z0-9_]*$/
  private readonly VALID_DEVICE_SN_PATTERN = /^[a-zA-Z0-9\-_]+$/
  private readonly VALID_TABLE_NAME_PATTERN = /^dh_[a-zA-Z0-9_]+$/

  // 允许的数据库字段名白名单
  private readonly ALLOWED_FIELD_NAMES = new Set([
    'id', 'created_at', 'modified_at', 'collection_at', 'device_sn', 'hl_num', 'dpth',
    'advnc_spd', 'rtn_tq', 'wtr_prs_h', 'frcst_kn', 'rtn_spd', 'pump_prs',
    'first_data_time', 'last_data_time', 'file_name', 'file_size', 'data_count',
    'name', 'type', 'status', 'config', 'description', 'product_key'
  ])

  /**
   * 检测SQL注入攻击
   */
  public detectSqlInjection(input: string): SqlInjectionDetectionResult {
    const violations: string[] = []
    const blockedPatterns: string[] = []
    let riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' = 'LOW'

    // 检查关键SQL注入模式
    for (const pattern of this.CRITICAL_SQL_PATTERNS) {
      const matches = input.match(pattern)
      if (matches) {
        violations.push(`检测到关键SQL注入模式: ${matches[0]}`)
        blockedPatterns.push(pattern.toString())
        riskLevel = 'CRITICAL'
      }
    }

    // 检查中等风险模式
    if (riskLevel !== 'CRITICAL') {
      for (const pattern of this.MEDIUM_SQL_PATTERNS) {
        const matches = input.match(pattern)
        if (matches) {
          violations.push(`检测到可疑SQL模式: ${matches[0]}`)
          blockedPatterns.push(pattern.toString())
          riskLevel = riskLevel === 'LOW' ? 'MEDIUM' : riskLevel
        }
      }
    }

    return {
      isSafe: violations.length === 0,
      violations,
      riskLevel,
      blockedPatterns
    }
  }

  /**
   * 验证数据库标识符（表名、字段名等）
   */
  public validateDatabaseIdentifier(identifier: string, type: 'table' | 'field' | 'device_sn'): boolean {
    if (!identifier || typeof identifier !== 'string') {
      return false
    }

    switch (type) {
      case 'table':
        return this.VALID_TABLE_NAME_PATTERN.test(identifier)
      case 'field':
        return this.ALLOWED_FIELD_NAMES.has(identifier.toLowerCase()) || 
               this.VALID_IDENTIFIER_PATTERN.test(identifier)
      case 'device_sn':
        return this.VALID_DEVICE_SN_PATTERN.test(identifier) && identifier.length <= 50
      default:
        return this.VALID_IDENTIFIER_PATTERN.test(identifier)
    }
  }

  /**
   * 清理和转义用户输入
   */
  public sanitizeInput(input: string): string {
    if (!input || typeof input !== 'string') {
      return ''
    }

    return input
      .trim()
      // 移除SQL注释
      .replace(/(--|\#|\/\*|\*\/)/g, '')
      // 转义单引号和双引号
      .replace(/'/g, "''")
      .replace(/"/g, '""')
      // 移除分号
      .replace(/;/g, '')
      // 限制长度
      .substring(0, 1000)
  }

  /**
   * 验证查询参数
   */
  public validateQueryParams(params: Record<string, any>): { isValid: boolean; errors: string[] } {
    const errors: string[] = []

    for (const [key, value] of Object.entries(params)) {
      if (value === null || value === undefined) {
        continue
      }

      const stringValue = String(value)
      
      // 检查参数名
      if (!this.validateDatabaseIdentifier(key, 'field')) {
        errors.push(`无效的参数名: ${key}`)
        continue
      }

      // 检查参数值
      const detection = this.detectSqlInjection(stringValue)
      if (!detection.isSafe) {
        errors.push(`参数 ${key} 包含可疑内容: ${detection.violations.join(', ')}`)
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }
}

// 创建全局实例
const sqlProtector = new SqlInjectionProtector()

/**
 * SQL注入防护中间件
 */
export const sqlInjectionProtection = (options: {
  checkBody?: boolean
  checkQuery?: boolean
  checkParams?: boolean
  strictMode?: boolean
} = {}) => {
  const {
    checkBody = true,
    checkQuery = true,
    checkParams = true,
    strictMode = false
  } = options

  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      const violations: string[] = []

      // 检查URL参数
      if (checkParams && req.params) {
        const paramValidation = sqlProtector.validateQueryParams(req.params)
        if (!paramValidation.isValid) {
          violations.push(...paramValidation.errors)
        }
      }

      // 检查查询字符串
      if (checkQuery && req.query) {
        const queryValidation = sqlProtector.validateQueryParams(req.query as Record<string, any>)
        if (!queryValidation.isValid) {
          violations.push(...queryValidation.errors)
        }
      }

      // 检查请求体
      if (checkBody && req.body) {
        const bodyValidation = sqlProtector.validateQueryParams(req.body)
        if (!bodyValidation.isValid) {
          violations.push(...bodyValidation.errors)
        }
      }

      // 如果发现违规
      if (violations.length > 0) {
        // 记录安全事件
        sqlSecurityMonitor.logSecurityEvent(
          SqlSecurityEventType.SQL_INJECTION_ATTEMPT,
          strictMode ? 'CRITICAL' : 'HIGH',
          req,
          {
            violations,
            requestData: {
              params: req.params,
              query: req.query,
              body: req.body
            }
          },
          strictMode ? 'BLOCKED' : 'LOGGED'
        )

        if (strictMode) {
          throw new AppError('检测到可疑的SQL注入攻击', 400, -1)
        } else {
          // 非严格模式下记录日志但允许继续
          console.warn('SQL注入防护: 检测到可疑活动但允许继续')
        }
      }

      next()
    } catch (error) {
      next(error)
    }
  }
}

// 导出工具函数
export { sqlProtector }
export default sqlInjectionProtection

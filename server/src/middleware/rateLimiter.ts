import { Request, Response, NextFunction } from 'express'
import { AppError } from './error'

/**
 * 速率限制算法类型
 */
export enum RateLimitAlgorithm {
  TOKEN_BUCKET = 'TOKEN_BUCKET',
  SLIDING_WINDOW = 'SLIDING_WINDOW',
  FIXED_WINDOW = 'FIXED_WINDOW',
  LEAKY_BUCKET = 'LEAKY_BUCKET'
}

/**
 * 速率限制配置
 */
export interface RateLimitConfig {
  algorithm: RateLimitAlgorithm
  windowMs: number // 时间窗口（毫秒）
  maxRequests: number // 最大请求数
  keyGenerator?: (req: Request) => string // 自定义键生成器
  skipSuccessfulRequests?: boolean // 是否跳过成功请求
  skipFailedRequests?: boolean // 是否跳过失败请求
  message?: string // 自定义错误消息
  standardHeaders?: boolean // 是否返回标准头部
  legacyHeaders?: boolean // 是否返回旧版头部
  onLimitReached?: (req: Request, res: Response) => void // 限制触发回调
  whitelist?: string[] // IP白名单
  blacklist?: string[] // IP黑名单
}

/**
 * 请求记录
 */
interface RequestRecord {
  count: number
  resetTime: number
  tokens?: number // Token bucket算法使用
  lastRefill?: number // Token bucket算法使用
  requests?: number[] // Sliding window算法使用
}

/**
 * 速率限制结果
 */
interface RateLimitResult {
  allowed: boolean
  remaining: number
  resetTime: number
  retryAfter?: number
}

/**
 * 获取默认配置（从统一配置读取）
 */
function getDefaultConfig(): Partial<RateLimitConfig> {
  try {
    const { securityConfig } = require('../config/security')
    return {
      algorithm: RateLimitAlgorithm.SLIDING_WINDOW,
      windowMs: securityConfig.rateLimit.api.windowMs,
      maxRequests: securityConfig.rateLimit.api.maxRequests,
      message: '请求过于频繁，请稍后再试',
      standardHeaders: true,
      legacyHeaders: false,
      skipSuccessfulRequests: false,
      skipFailedRequests: false
    }
  } catch (error) {
    // 如果配置加载失败，使用最基本的默认值
    console.warn('Failed to load rate limiter config, using fallback defaults')
    return {
      algorithm: RateLimitAlgorithm.SLIDING_WINDOW,
      windowMs: 15 * 60 * 1000,
      maxRequests: 100,
      message: '请求过于频繁，请稍后再试',
      standardHeaders: true,
      legacyHeaders: false,
      skipSuccessfulRequests: false,
      skipFailedRequests: false
    }
  }
}

/**
 * 高级速率限制器类
 */
export class AdvancedRateLimiter {
  private config: RateLimitConfig
  private store = new Map<string, RequestRecord>()
  private cleanupInterval?: NodeJS.Timeout

  constructor(config: Partial<RateLimitConfig> = {}) {
    this.config = { ...getDefaultConfig(), ...config } as RateLimitConfig
    
    // 启动清理定时器
    this.cleanupInterval = setInterval(() => {
      this.cleanup()
    }, this.config.windowMs)
  }

  /**
   * 生成请求键
   */
  private generateKey(req: Request): string {
    if (this.config.keyGenerator) {
      return this.config.keyGenerator(req)
    }
    
    // 默认使用IP地址
    return req.ip || req.connection.remoteAddress || 'unknown'
  }

  /**
   * 检查IP是否在白名单中
   */
  private isWhitelisted(ip: string): boolean {
    return this.config.whitelist?.includes(ip) || false
  }

  /**
   * 检查IP是否在黑名单中
   */
  private isBlacklisted(ip: string): boolean {
    return this.config.blacklist?.includes(ip) || false
  }

  /**
   * Token Bucket算法
   */
  private tokenBucket(key: string, now: number): RateLimitResult {
    let record = this.store.get(key)
    
    if (!record) {
      record = {
        count: 0,
        resetTime: now + this.config.windowMs,
        tokens: this.config.maxRequests,
        lastRefill: now
      }
      this.store.set(key, record)
    }

    // 计算需要添加的token数量
    const timePassed = now - (record.lastRefill || now)
    const tokensToAdd = Math.floor(timePassed / this.config.windowMs * this.config.maxRequests)
    
    if (tokensToAdd > 0) {
      record.tokens = Math.min(this.config.maxRequests, (record.tokens || 0) + tokensToAdd)
      record.lastRefill = now
    }

    // 检查是否有可用token
    if ((record.tokens || 0) >= 1) {
      record.tokens = (record.tokens || 0) - 1
      record.count++
      
      return {
        allowed: true,
        remaining: record.tokens,
        resetTime: record.resetTime
      }
    }

    return {
      allowed: false,
      remaining: 0,
      resetTime: record.resetTime,
      retryAfter: Math.ceil(this.config.windowMs / this.config.maxRequests)
    }
  }

  /**
   * Sliding Window算法
   */
  private slidingWindow(key: string, now: number): RateLimitResult {
    let record = this.store.get(key)
    
    if (!record) {
      record = {
        count: 0,
        resetTime: now + this.config.windowMs,
        requests: []
      }
      this.store.set(key, record)
    }

    // 清理过期请求
    const windowStart = now - this.config.windowMs
    record.requests = (record.requests || []).filter(time => time > windowStart)

    // 检查是否超过限制
    if (record.requests.length >= this.config.maxRequests) {
      const oldestRequest = Math.min(...record.requests)
      const retryAfter = Math.ceil((oldestRequest + this.config.windowMs - now) / 1000)
      
      return {
        allowed: false,
        remaining: 0,
        resetTime: oldestRequest + this.config.windowMs,
        retryAfter
      }
    }

    // 添加当前请求
    record.requests.push(now)
    record.count++

    return {
      allowed: true,
      remaining: this.config.maxRequests - record.requests.length,
      resetTime: now + this.config.windowMs
    }
  }

  /**
   * Fixed Window算法
   */
  private fixedWindow(key: string, now: number): RateLimitResult {
    const windowStart = Math.floor(now / this.config.windowMs) * this.config.windowMs
    const windowKey = `${key}:${windowStart}`
    
    let record = this.store.get(windowKey)
    
    if (!record) {
      record = {
        count: 0,
        resetTime: windowStart + this.config.windowMs
      }
      this.store.set(windowKey, record)
    }

    // 检查是否超过限制
    if (record.count >= this.config.maxRequests) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: record.resetTime,
        retryAfter: Math.ceil((record.resetTime - now) / 1000)
      }
    }

    record.count++

    return {
      allowed: true,
      remaining: this.config.maxRequests - record.count,
      resetTime: record.resetTime
    }
  }

  /**
   * Leaky Bucket算法
   */
  private leakyBucket(key: string, now: number): RateLimitResult {
    let record = this.store.get(key)
    
    if (!record) {
      record = {
        count: 0,
        resetTime: now + this.config.windowMs,
        lastRefill: now
      }
      this.store.set(key, record)
    }

    // 计算泄漏的请求数
    const timePassed = now - (record.lastRefill || now)
    const leakRate = this.config.maxRequests / this.config.windowMs
    const leaked = Math.floor(timePassed * leakRate)
    
    if (leaked > 0) {
      record.count = Math.max(0, record.count - leaked)
      record.lastRefill = now
    }

    // 检查桶是否已满
    if (record.count >= this.config.maxRequests) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: record.resetTime,
        retryAfter: Math.ceil(1 / leakRate)
      }
    }

    record.count++

    return {
      allowed: true,
      remaining: this.config.maxRequests - record.count,
      resetTime: record.resetTime
    }
  }

  /**
   * 检查速率限制
   */
  public checkLimit(req: Request): RateLimitResult {
    const now = Date.now()
    const key = this.generateKey(req)
    const ip = req.ip || req.connection.remoteAddress || 'unknown'

    // 检查黑名单
    if (this.isBlacklisted(ip)) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: now + this.config.windowMs,
        retryAfter: Math.ceil(this.config.windowMs / 1000)
      }
    }

    // 检查白名单
    if (this.isWhitelisted(ip)) {
      return {
        allowed: true,
        remaining: this.config.maxRequests,
        resetTime: now + this.config.windowMs
      }
    }

    // 根据算法类型执行相应的限制检查
    switch (this.config.algorithm) {
      case RateLimitAlgorithm.TOKEN_BUCKET:
        return this.tokenBucket(key, now)
      case RateLimitAlgorithm.SLIDING_WINDOW:
        return this.slidingWindow(key, now)
      case RateLimitAlgorithm.FIXED_WINDOW:
        return this.fixedWindow(key, now)
      case RateLimitAlgorithm.LEAKY_BUCKET:
        return this.leakyBucket(key, now)
      default:
        return this.slidingWindow(key, now)
    }
  }

  /**
   * 设置响应头
   */
  private setHeaders(res: Response, result: RateLimitResult): void {
    if (this.config.standardHeaders) {
      res.set({
        'X-RateLimit-Limit': this.config.maxRequests.toString(),
        'X-RateLimit-Remaining': result.remaining.toString(),
        'X-RateLimit-Reset': new Date(result.resetTime).toISOString()
      })

      if (!result.allowed && result.retryAfter) {
        res.set('Retry-After', result.retryAfter.toString())
      }
    }

    if (this.config.legacyHeaders) {
      res.set({
        'X-Rate-Limit-Limit': this.config.maxRequests.toString(),
        'X-Rate-Limit-Remaining': result.remaining.toString(),
        'X-Rate-Limit-Reset': Math.ceil(result.resetTime / 1000).toString()
      })
    }
  }

  /**
   * 创建中间件
   */
  public middleware() {
    return (req: Request, res: Response, next: NextFunction): void => {
      try {
        const result = this.checkLimit(req)
        
        // 设置响应头
        this.setHeaders(res, result)

        if (!result.allowed) {
          // 触发限制回调
          if (this.config.onLimitReached) {
            this.config.onLimitReached(req, res)
          }

          // 记录限制事件
          console.warn(`Rate limit exceeded for ${this.generateKey(req)}`, {
            ip: req.ip,
            url: req.url,
            method: req.method,
            userAgent: req.get('User-Agent'),
            algorithm: this.config.algorithm,
            limit: this.config.maxRequests,
            window: this.config.windowMs
          })

          throw new AppError(this.config.message || '请求过于频繁', 429)
        }

        next()
      } catch (error) {
        next(error)
      }
    }
  }

  /**
   * 清理过期记录
   */
  private cleanup(): void {
    const now = Date.now()
    const keysToDelete: string[] = []

    for (const [key, record] of this.store.entries()) {
      // 清理过期的固定窗口记录
      if (key.includes(':') && record.resetTime < now) {
        keysToDelete.push(key)
        continue
      }

      // 清理过期的滑动窗口记录
      if (record.requests) {
        const windowStart = now - this.config.windowMs
        record.requests = record.requests.filter(time => time > windowStart)
        
        if (record.requests.length === 0 && record.resetTime < now) {
          keysToDelete.push(key)
        }
      }

      // 清理过期的其他记录
      if (!record.requests && record.resetTime < now) {
        keysToDelete.push(key)
      }
    }

    keysToDelete.forEach(key => this.store.delete(key))
    
    if (keysToDelete.length > 0) {
      console.log(`Cleaned up ${keysToDelete.length} expired rate limit records`)
    }
  }

  /**
   * 获取统计信息
   */
  public getStats(): {
    totalKeys: number
    algorithm: RateLimitAlgorithm
    config: RateLimitConfig
  } {
    return {
      totalKeys: this.store.size,
      algorithm: this.config.algorithm,
      config: this.config
    }
  }

  /**
   * 重置特定键的限制
   */
  public reset(key: string): boolean {
    return this.store.delete(key)
  }

  /**
   * 销毁限制器
   */
  public destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
    }
    this.store.clear()
  }
}

/**
 * 创建速率限制中间件的便捷函数
 */
export const createRateLimiter = (config: Partial<RateLimitConfig> = {}) => {
  const limiter = new AdvancedRateLimiter(config)
  return limiter.middleware()
}

export default AdvancedRateLimiter

import { Request, Response, NextFunction } from 'express'
import { body, param, query, validationResult } from 'express-validator'
import { AppError } from './error'

/**
 * 处理验证结果的中间件
 */
export const handleValidationErrors = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const errors = validationResult(req)
  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => error.msg).join(', ')
    return next(new AppError(`输入验证失败: ${errorMessages}`, 400))
  }
  next()
}

/**
 * 用户登录验证规则
 */
export const validateLogin = [
  body('username')
    .trim()
    .isLength({ min: 3, max: 50 })
    .withMessage('用户名长度必须在3-50个字符之间')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('用户名只能包含字母、数字和下划线'),
  
  body('password')
    .isLength({ min: 6, max: 128 })
    .withMessage('密码长度必须在6-128个字符之间'),
    // 暂时禁用复杂密码验证
    // .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    // .withMessage('密码必须包含至少一个小写字母、一个大写字母和一个数字'),
  
  handleValidationErrors
]

/**
 * 用户注册验证规则
 */
export const validateRegister = [
  body('account')
    .trim()
    .isLength({ min: 3, max: 50 })
    .withMessage('账号长度必须在3-50个字符之间')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('账号只能包含字母、数字和下划线'),
  
  body('password')
    .isLength({ min: 6, max: 128 })
    .withMessage('密码长度必须在6-128个字符之间'),
    // 暂时禁用复杂密码验证
    // .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    // .withMessage('密码必须包含至少一个小写字母、一个大写字母、一个数字和一个特殊字符'),
  
  body('username')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('用户名长度必须在1-100个字符之间')
    .escape(), // 防止XSS
  
  handleValidationErrors
]

/**
 * ID参数验证规则
 */
export const validateId = [
  param('id')
    .isInt({ min: 1 })
    .withMessage('ID必须是正整数'),
  
  handleValidationErrors
]

/**
 * 设备ID验证规则
 */
export const validateDeviceId = [
  param('deviceId')
    .isInt({ min: 1 })
    .withMessage('设备ID必须是正整数'),
  
  handleValidationErrors
]

/**
 * 文件上传验证规则
 */
export const validateFileUpload = [
  body('file_name')
    .trim()
    .isLength({ min: 1, max: 255 })
    .withMessage('文件名长度必须在1-255个字符之间')
    .matches(/^[a-zA-Z0-9._-]+$/)
    .withMessage('文件名只能包含字母、数字、点、下划线和连字符'),
  
  body('content')
    .optional()
    .isLength({ max: 10485760 }) // 10MB
    .withMessage('文件内容不能超过10MB'),
  
  handleValidationErrors
]

/**
 * 算法创建验证规则
 */
export const validateArithmeticCreate = [
  body('name')
    .trim()
    .isLength({ min: 1, max: 32 })
    .withMessage('算法名称长度必须在1-32个字符之间')
    .matches(/^[a-zA-Z0-9_\u4e00-\u9fa5]+$/)
    .withMessage('算法名称只能包含字母、数字、下划线和中文'),

  body('type')
    .isInt({ min: 0, max: 1 })
    .withMessage('算法类型必须是0或1'),

  body('product_key')
    .optional()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('产品类型长度必须在1-50个字符之间')
    .matches(/^[A-Z]+$/)
    .withMessage('产品类型只能包含大写字母'),

  body('content')
    .optional()
    .isLength({ max: 1048576 }) // 1MB
    .withMessage('算法内容不能超过1MB'),

  handleValidationErrors
]

/**
 * 算法更新验证规则
 */
export const validateArithmeticUpdate = [
  body('name')
    .optional()
    .trim()
    .isLength({ min: 1, max: 32 })
    .withMessage('算法名称长度必须在1-32个字符之间')
    .matches(/^[a-zA-Z0-9_\u4e00-\u9fa5]+$/)
    .withMessage('算法名称只能包含字母、数字、下划线和中文'),

  body('type')
    .optional()
    .isInt({ min: 0, max: 1 })
    .withMessage('算法类型必须是0或1'),

  body('product_key')
    .optional()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('产品类型长度必须在1-50个字符之间')
    .matches(/^[A-Z]+$/)
    .withMessage('产品类型只能包含大写字母'),

  body('content')
    .optional()
    .isLength({ max: 1048576 }) // 1MB
    .withMessage('算法内容不能超过1MB'),

  handleValidationErrors
]

/**
 * 算法验证规则（兼容旧版本）
 */
export const validateArithmetic = validateArithmeticUpdate

/**
 * 数据查询验证规则
 */
export const validateDataQuery = [
  body('deviceId')
    .isInt({ min: 1 })
    .withMessage('设备ID必须是正整数'),

  body('fileId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('文件ID必须是正整数'),

  body('startTime')
    .optional()
    .isISO8601()
    .withMessage('开始时间必须是有效的ISO8601格式'),

  body('endTime')
    .optional()
    .isISO8601()
    .withMessage('结束时间必须是有效的ISO8601格式'),

  body('limit')
    .optional()
    .isInt({ min: 1, max: 10000 })
    .withMessage('限制数量必须在1-10000之间'),

  body('offset')
    .optional()
    .isInt({ min: 0 })
    .withMessage('偏移量必须是非负整数'),

  handleValidationErrors
]

/**
 * 带算法的数据查询验证规则
 */
export const validateDataQueryWithAlgorithms = [
  body('deviceId')
    .isInt({ min: 1 })
    .withMessage('设备ID必须是正整数'),

  body('functions')
    .notEmpty()
    .withMessage('functions参数不能为空')
    .isString()
    .withMessage('functions参数必须是字符串')
    .isLength({ min: 1, max: 1000 })
    .withMessage('functions参数长度必须在1-1000字符之间')
    .matches(/^[a-zA-Z0-9_,\s]+$/)
    .withMessage('functions参数只能包含字母、数字、下划线和逗号'),

  body('fileId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('文件ID必须是正整数'),

  body('startTime')
    .optional()
    .isISO8601()
    .withMessage('开始时间必须是有效的ISO8601格式'),

  body('endTime')
    .optional()
    .isISO8601()
    .withMessage('结束时间必须是有效的ISO8601格式'),

  body('holeNo')
    .optional()
    .isString()
    .withMessage('孔号必须是字符串')
    .isLength({ min: 1, max: 50 })
    .withMessage('孔号长度必须在1-50字符之间')
    .matches(/^[a-zA-Z0-9_-]+$/)
    .withMessage('孔号只能包含字母、数字、下划线和连字符'),

  body('limit')
    .optional()
    .isInt({ min: 1, max: 10000 })
    .withMessage('限制数量必须在1-10000之间'),

  body('offset')
    .optional()
    .isInt({ min: 0 })
    .withMessage('偏移量必须是非负整数'),

  handleValidationErrors
]

/**
 * 分页查询验证规则
 */
export const validatePagination = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是正整数'),
  
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每页数量必须在1-100之间'),
  
  handleValidationErrors
]

/**
 * 通用字符串清理函数
 */
export const sanitizeString = (str: string): string => {
  return str
    .trim()
    .replace(/[<>]/g, '') // 移除潜在的HTML标签
    .replace(/javascript:/gi, '') // 移除JavaScript协议
    .replace(/on\w+=/gi, '') // 移除事件处理器
}

/**
 * 增强的SQL注入防护 - 使用更严格的检测和清理
 */
export const sanitizeSqlInput = (str: string): string => {
  if (!str || typeof str !== 'string') {
    return ''
  }

  // 更全面的SQL关键字列表
  const sqlKeywords = [
    // 基本SQL语句
    'SELECT', 'INSERT', 'UPDATE', 'DELETE', 'DROP', 'CREATE', 'ALTER', 'TRUNCATE',
    // 联合查询
    'UNION', 'UNION ALL',
    // 逻辑操作符
    'OR', 'AND', 'NOT', 'XOR',
    // 子句
    'WHERE', 'FROM', 'JOIN', 'INNER JOIN', 'LEFT JOIN', 'RIGHT JOIN', 'FULL JOIN',
    'GROUP BY', 'ORDER BY', 'HAVING', 'LIMIT', 'OFFSET',
    // 函数
    'COUNT', 'SUM', 'AVG', 'MIN', 'MAX', 'CONCAT', 'SUBSTRING', 'CHAR', 'ASCII',
    // 条件语句
    'CASE', 'WHEN', 'THEN', 'ELSE', 'END', 'IF', 'IFNULL', 'ISNULL',
    // 存储过程和函数
    'EXEC', 'EXECUTE', 'CALL', 'PROCEDURE', 'FUNCTION',
    // 系统表和视图
    'INFORMATION_SCHEMA', 'SYSOBJECTS', 'SYSCOLUMNS', 'pg_tables', 'mysql.user',
    // 注释和特殊字符
    '--', '#', '/*', '*/', ';',
    // 时间函数（用于盲注）
    'SLEEP', 'WAITFOR', 'DELAY', 'BENCHMARK', 'pg_sleep'
  ]

  let cleaned = str.trim()

  // 移除SQL关键字
  sqlKeywords.forEach(keyword => {
    const regex = new RegExp(`\\b${keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'gi')
    cleaned = cleaned.replace(regex, '')
  })

  // 移除危险的特殊字符组合
  cleaned = cleaned
    // 移除SQL注释
    .replace(/(--|\#|\/\*|\*\/)/g, '')
    // 移除分号
    .replace(/;/g, '')
    // 移除可能的字符串拼接
    .replace(/(\|\||&&)/g, '')
    // 移除十六进制编码
    .replace(/0x[0-9a-fA-F]+/g, '')
    // 限制连续的引号
    .replace(/['"]{2,}/g, '"')

  return cleaned.trim()
}

/**
 * 验证设备序列号格式
 */
export const validateDeviceSerialNumber = (deviceSn: string): boolean => {
  if (!deviceSn || typeof deviceSn !== 'string') {
    return false
  }

  // 设备序列号只能包含字母、数字、连字符和下划线，长度限制
  const pattern = /^[a-zA-Z0-9\-_]{1,50}$/
  return pattern.test(deviceSn)
}

/**
 * 验证数据库表名
 */
export const validateTableName = (tableName: string): boolean => {
  if (!tableName || typeof tableName !== 'string') {
    return false
  }

  // 表名必须以dh_开头，只能包含字母、数字和下划线
  const pattern = /^dh_[a-zA-Z0-9_]{1,100}$/
  return pattern.test(tableName)
}

/**
 * 验证数据库字段名
 */
export const validateFieldName = (fieldName: string): boolean => {
  if (!fieldName || typeof fieldName !== 'string') {
    return false
  }

  // 字段名白名单
  const allowedFields = new Set([
    'id', 'created_at', 'modified_at', 'collection_at', 'device_sn', 'hl_num', 'dpth',
    'advnc_spd', 'rtn_tq', 'wtr_prs_h', 'frcst_kn', 'rtn_spd', 'pump_prs',
    'first_data_time', 'last_data_time', 'file_name', 'file_size', 'data_count',
    'name', 'type', 'status', 'config', 'description', 'product_key', 'username',
    'account', 'password', 'role', 'token', 'avatar_url', 'device_name', 'device_type'
  ])

  return allowedFields.has(fieldName.toLowerCase())
}

/**
 * 安全的数字验证
 */
export const validateNumericInput = (input: any): boolean => {
  if (input === null || input === undefined || input === '') {
    return false
  }

  const num = Number(input)
  return !isNaN(num) && isFinite(num)
}

/**
 * 验证时间格式
 */
export const validateTimeInput = (timeStr: string): boolean => {
  if (!timeStr || typeof timeStr !== 'string') {
    return false
  }

  // 验证ISO时间格式或常见时间格式
  const timePattern = /^\d{4}-\d{2}-\d{2}(\s|T)\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$/
  return timePattern.test(timeStr) && !isNaN(Date.parse(timeStr))
}

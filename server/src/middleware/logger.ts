import { Request, Response, NextFunction } from 'express'
import winston from 'winston'
import DailyRotateFile from 'winston-daily-rotate-file'
import path from 'path'
import fs from 'fs'

// 自定义日志级别
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4
}

const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white'
}

winston.addColors(colors)

// 从环境变量获取日志配置
const logLevel = process.env.LOG_LEVEL || 'info'
const logPath = process.env.LOG_FILE_PATH || './logs'

// 确保日志目录存在
if (!fs.existsSync(logPath)) {
  fs.mkdirSync(logPath, { recursive: true })
}

// 控制台格式（带颜色）
const consoleFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.colorize({ all: true }),
  winston.format.printf(info => `${info.timestamp} ${info.level}: ${info.message}`)
)

// 文件格式（JSON格式，便于日志分析）
const fileFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.json()
)

// 错误日志轮转配置
const errorRotateTransport = new DailyRotateFile({
  filename: path.join(logPath, 'error-%DATE%.log'),
  datePattern: 'YYYY-MM-DD',
  level: 'error',
  format: fileFormat,
  maxSize: '20m',
  maxFiles: '30d',
  createSymlink: true,
  symlinkName: 'error.log'
})

// 信息日志轮转配置
const infoRotateTransport = new DailyRotateFile({
  filename: path.join(logPath, 'info-%DATE%.log'),
  datePattern: 'YYYY-MM-DD',
  level: 'info',
  format: fileFormat,
  maxSize: '20m',
  maxFiles: '30d',
  createSymlink: true,
  symlinkName: 'info.log'
})

// 综合日志轮转配置
const combinedRotateTransport = new DailyRotateFile({
  filename: path.join(logPath, 'combined-%DATE%.log'),
  datePattern: 'YYYY-MM-DD',
  format: fileFormat,
  maxSize: '20m',
  maxFiles: '30d',
  createSymlink: true,
  symlinkName: 'combined.log'
})

// 创建传输器数组
const transports: winston.transport[] = [
  errorRotateTransport,
  infoRotateTransport,
  combinedRotateTransport
]

// 在开发环境下同时输出到控制台
if (process.env.NODE_ENV !== 'production') {
  transports.push(
    new winston.transports.Console({
      format: consoleFormat
    })
  )
}

// 创建 Winston 日志记录器
const logger = winston.createLogger({
  level: logLevel,
  levels,
  transports
})

// 监听日志轮转事件
errorRotateTransport.on('rotate', (oldFilename: string, newFilename: string) => {
  console.log(`错误日志轮转: ${oldFilename} -> ${newFilename}`)
})

infoRotateTransport.on('rotate', (oldFilename: string, newFilename: string) => {
  console.log(`信息日志轮转: ${oldFilename} -> ${newFilename}`)
})

combinedRotateTransport.on('rotate', (oldFilename: string, newFilename: string) => {
  console.log(`综合日志轮转: ${oldFilename} -> ${newFilename}`)
})

// 监听新文件创建事件
errorRotateTransport.on('new', (newFilename: string) => {
  console.log(`创建新的错误日志文件: ${newFilename}`)
})

infoRotateTransport.on('new', (newFilename: string) => {
  console.log(`创建新的信息日志文件: ${newFilename}`)
})

combinedRotateTransport.on('new', (newFilename: string) => {
  console.log(`创建新的综合日志文件: ${newFilename}`)
})

// 导出统一的logger实例
export { logger }

// 请求日志中间件
export const requestLogger = (req: Request, _res: Response, next: NextFunction) => {
  logger.info({
    method: req.method,
    path: req.path,
    ip: req.ip,
    userAgent: req.get('user-agent')
  })
  next()
}

/**
 * 在生产环境中重定向console方法到winston
 * 解决console.log输出到PM2 out.log而不是winston info.log的问题
 */
export const setupConsoleRedirection = (): void => {
  const isProduction = process.env.NODE_ENV === 'production'

  if (isProduction) {

    // 重定向console.log到winston.info
    console.log = (...args: any[]) => {
      const message = args.map(arg =>
        typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
      ).join(' ')
      logger.info(message)
    }

    // 重定向console.info到winston.info
    console.info = (...args: any[]) => {
      const message = args.map(arg =>
        typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
      ).join(' ')
      logger.info(message)
    }

    // 重定向console.warn到winston.warn
    console.warn = (...args: any[]) => {
      const message = args.map(arg =>
        typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
      ).join(' ')
      logger.warn(message)
    }

    // 重定向console.error到winston.error
    console.error = (...args: any[]) => {
      const message = args.map(arg =>
        typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
      ).join(' ')
      logger.error(message)
    }

    // 重定向console.debug到winston.debug
    console.debug = (...args: any[]) => {
      const message = args.map(arg =>
        typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
      ).join(' ')
      logger.debug(message)
    }

    logger.info('✅ 生产环境console重定向已启用，所有console输出将写入winston日志文件')
  }
}

/**
 * 便捷的日志方法
 */
export const logInfo = (message: string, meta?: any) => logger.info(message, meta)
export const logWarn = (message: string, meta?: any) => logger.warn(message, meta)
export const logError = (message: string, meta?: any) => logger.error(message, meta)
export const logDebug = (message: string, meta?: any) => logger.debug(message, meta)

/**
 * 专用日志方法
 */
export const logDataProcessing = (operation: string, beforeCount: number, afterCount: number, deviceId?: string) => {
  const message = deviceId
    ? `设备${deviceId} ${operation}，处理前: ${beforeCount} 条，处理后: ${afterCount} 条`
    : `${operation}，处理前: ${beforeCount} 条，处理后: ${afterCount} 条`

  logger.info(message)
}

export const logAlgorithmExecution = (algorithmId: string, methodName: string, status: 'start' | 'success' | 'error', error?: any) => {
  const message = `算法${algorithmId} 方法${methodName} ${status === 'start' ? '开始执行' : status === 'success' ? '执行成功' : '执行失败'}`

  if (status === 'error') {
    logger.error(message, { error: error?.message || error })
  } else {
    logger.info(message)
  }
}

export const logPerformance = (operation: string, duration: number, details?: any) => {
  const message = `${operation} 耗时: ${duration}ms`
  logger.info(message, details)
}

import NodeCache from 'node-cache'
import config from '../config'

/**
 * 缓存统计信息接口
 */
interface CacheStats {
  keys: number
  hits: number
  misses: number
  ksize: number
  vsize: number
}

/**
 * 内存缓存管理器
 * 基于Node-Cache实现，提供统一的缓存操作接口
 */
export class CacheManager {
  private cache: NodeCache
  private stats: { hits: number; misses: number }

  constructor() {
    this.cache = new NodeCache({
      stdTTL: config.cache?.defaultTTL || 600, // 默认10分钟
      checkperiod: 60, // 每分钟检查过期键
      useClones: false, // 不克隆对象，提高性能
      maxKeys: config.cache?.maxKeys || 10000 // 最大键数量
    })

    this.stats = {
      hits: 0,
      misses: 0
    }

    // 监听缓存事件
    this.cache.on('set', (key, value) => {
      console.log(`缓存设置: ${key}`)
    })

    this.cache.on('del', (key, value) => {
      console.log(`缓存删除: ${key}`)
    })

    this.cache.on('expired', (key, value) => {
      console.log(`缓存过期: ${key}`)
    })
  }

  /**
   * 生成标准化的缓存键
   * @param category 缓存类别 (device, algorithm, template等)
   * @param userId 用户ID（支持字符串和数字类型）
   * @param identifier 标识符 (通常是参数的哈希值)
   * @returns 标准化的缓存键
   */
  generateKey(category: string, userId: string | number, identifier: string): string {
    return `${category}:${userId}:${identifier}`
  }

  /**
   * 获取缓存值
   * @param key 缓存键
   * @returns 缓存值或null
   */
  async get(key: string): Promise<any> {
    const value = this.cache.get(key)
    if (value !== undefined) {
      this.stats.hits++
      return value
    } else {
      this.stats.misses++
      return null
    }
  }

  /**
   * 设置缓存值
   * @param key 缓存键
   * @param value 缓存值
   * @param ttl 过期时间(秒)，可选
   */
  async set(key: string, value: any, ttl?: number): Promise<void> {
    if (ttl !== undefined) {
      this.cache.set(key, value, ttl)
    } else {
      this.cache.set(key, value)
    }
  }

  /**
   * 删除单个缓存键
   * @param key 缓存键
   */
  async del(key: string): Promise<void> {
    this.cache.del(key)
  }

  /**
   * 根据模式清除缓存
   * @param pattern 匹配模式，支持部分匹配
   */
  clearByPattern(pattern: string): void {
    const keys = this.cache.keys().filter(key => key.includes(pattern))
    if (keys.length > 0) {
      this.cache.del(keys)
      console.log(`已清除 ${keys.length} 个匹配模式 "${pattern}" 的缓存键`)
    }
  }

  /**
   * 批量清除缓存模式
   * @param patterns 模式数组
   */
  clearByPatterns(patterns: string[]): void {
    patterns.forEach(pattern => {
      this.clearByPattern(pattern)
    })
  }

  /**
   * 清除指定用户的所有缓存
   * @param userId 用户ID（支持字符串和数字类型）
   * @param categories 可选的类别过滤，如果提供则只清除指定类别
   */
  clearUserCache(userId: string | number, categories?: string[]): void {
    const userPattern = `:${userId}:`
    let keys = this.cache.keys().filter(key => key.includes(userPattern))

    // 如果指定了类别，进一步过滤
    if (categories && categories.length > 0) {
      keys = keys.filter(key => {
        return categories.some(category => key.startsWith(`${category}:`))
      })
    }

    if (keys.length > 0) {
      this.cache.del(keys)
      const categoryInfo = categories ? ` (类别: ${categories.join(', ')})` : ''
      console.log(`已清除用户 ${userId} 的缓存${categoryInfo}，共 ${keys.length} 个键`)
    }
  }

  /**
   * 清除指定类别的所有缓存
   * @param category 缓存类别
   */
  clearCategoryCache(category: string): void {
    const categoryPattern = `${category}:`
    const keys = this.cache.keys().filter(key => key.startsWith(categoryPattern))
    if (keys.length > 0) {
      this.cache.del(keys)
      console.log(`已清除类别 "${category}" 的所有缓存，共 ${keys.length} 个键`)
    }
  }

  /**
   * 清除所有缓存
   */
  clearAll(): void {
    this.cache.flushAll()
    console.log('已清除所有缓存')
  }

  /**
   * 获取缓存统计信息
   * @returns 缓存统计信息
   */
  getStats(): CacheStats {
    const cacheStats = this.cache.getStats()
    return {
      keys: cacheStats.keys,
      hits: this.stats.hits,
      misses: this.stats.misses,
      ksize: cacheStats.ksize,
      vsize: cacheStats.vsize
    }
  }

  /**
   * 获取所有缓存键
   * @returns 缓存键数组
   */
  getKeys(): string[] {
    return this.cache.keys()
  }

  /**
   * 检查缓存键是否存在
   * @param key 缓存键
   * @returns 是否存在
   */
  has(key: string): boolean {
    return this.cache.has(key)
  }

  /**
   * 获取缓存键的TTL
   * @param key 缓存键
   * @returns TTL值(秒)，0表示永不过期，undefined表示键不存在
   */
  getTtl(key: string): number | undefined {
    return this.cache.getTtl(key)
  }
}

// 导出单例实例
export const cacheManager = new CacheManager()
export default cacheManager

#!/usr/bin/env node

/**
 * 通用并发性能测试脚本
 * 支持多种接口测试，包括认证缓存、模板、设备等
 */

const axios = require('axios')
const fs = require('fs')
const path = require('path')

// 默认配置
const config = {
  baseURL: 'http://localhost:3001',
  concurrency: 100,
  totalRequests: 100,
  timeout: 10000,
  // 可以写死cookie或通过参数传入
  defaultCookie: 'accessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjEiLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIiwiaWF0IjoxNzUzNzc0NTEwLCJqdGkiOiJiYjRkMWZlNi0wZDJiLTQ5OWMtYWNhMy01MGZkZDM5NTU5ZmEiLCJleHAiOjE3NTM3ODE3MTAsImF1ZCI6IndlYi1wYW5lbC1jbGllbnQiLCJpc3MiOiJ3ZWItcGFuZWwifQ.mHEfHyd-7rEDl4UleRzwwGXxslxlBj-iyKhP7Ddswhw; refreshToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjEiLCJ1c2VybmFtZSI6ImFkbWluIiwidHlwZSI6InJlZnJlc2giLCJpYXQiOjE3NTM3NzQ1MTAsImp0aSI6ImIwMWRmNTQ5LWNkNTgtNGVjZS05ODRhLTBkOTFiMjViYzVhZSIsImV4cCI6MTc1NDM3OTMxMCwiYXVkIjoid2ViLXBhbmVsLWNsaWVudCIsImlzcyI6IndlYi1wYW5lbCJ9.vaBbPn35_FIilLA5zP0d2VSCdhW8M9BnpWHuTyTB1ok',
  endpoint: '/api/templates',
  // 支持的测试接口
  endpoints: {
    templates: '/api/templates',
    devices: '/api/devices',
    algorithms: '/api/algorithms',
    users: '/api/users/profile',
    files: '/api/files'
  }
}

// 解析命令行参数
function parseArgs() {
  const args = process.argv.slice(2)

  for (let i = 0; i < args.length; i++) {
    switch (args[i]) {
      case '--concurrent':
      case '-c':
        config.concurrency = parseInt(args[i + 1])
        i++
        break
      case '--requests':
      case '-r':
        config.totalRequests = parseInt(args[i + 1])
        i++
        break
      case '--cookie':
        config.defaultCookie = args[i + 1]
        i++
        break
      case '--endpoint':
      case '-e':
        config.endpoint = args[i + 1]
        i++
        break
      case '--type':
      case '-t':
        const type = args[i + 1]
        if (config.endpoints[type]) {
          config.endpoint = config.endpoints[type]
          console.log(`✅ 选择接口类型: ${type} -> ${config.endpoint}`)
        } else {
          console.error(`❌ 不支持的接口类型: ${type}`)
          console.error(`支持的类型: ${Object.keys(config.endpoints).join(', ')}`)
          process.exit(1)
        }
        i++
        break
      case '--report':
        config.generateReport = true
        break
      case '--help':
      case '-h':
        showHelp()
        process.exit(0)
    }
  }
}

function showHelp() {
  console.log(`
🚀 通用并发性能测试脚本

用法: node test-cache-performance.js [选项]

选项:
  -c, --concurrent <数量>    并发数 (默认: 30)
  -r, --requests <数量>      总请求数 (默认: 100)
  --cookie <cookie值>        指定cookie (可选，不指定则自动登录)
  -e, --endpoint <路径>      测试接口路径 (默认: /api/templates)
  -t, --type <类型>          快速选择接口类型: templates|devices|algorithms|users|files
  --report                   生成详细测试报告
  -h, --help                显示帮助信息

支持的接口类型:
  templates    模板接口 (/api/templates)
  devices      设备接口 (/api/devices)
  algorithms   算法接口 (/api/algorithms)
  users        用户接口 (/api/users/profile)
  files        文件接口 (/api/files)

示例:
  node test-cache-performance.js -c 50 -r 200
  node test-cache-performance.js -t devices -c 20 -r 50
  node test-cache-performance.js --cookie "accessToken=eyJhbGciOiJIUzI1NiIs..." --report
  node test-cache-performance.js -e /api/custom-endpoint -c 10 -r 30
`)
}

// 统计数据
const stats = {
  requests: 0,
  responses: 0,
  errors: 0,
  responseTimes: [],
  startTime: 0,
  endTime: 0,
  cacheHits: 0,
  cacheMisses: 0,
  authCacheHits: 0,
  authCacheMisses: 0
}

// 自动登录获取cookie
async function login() {
  try {
    console.log('🔐 正在自动登录获取cookie...')
    const response = await axios.post(`${config.baseURL}/api/users/login`, {
      username: 'admin',
      password: 'admin123'
    }, { timeout: config.timeout })

    if (response.data.message === '登录成功') {
      const cookies = response.headers['set-cookie']
      if (cookies) {
        const accessTokenCookie = cookies.find(cookie => cookie.includes('accessToken='))
        if (accessTokenCookie) {
          const cookieValue = accessTokenCookie.split(';')[0] // 只取cookie值部分
          console.log('✅ 登录成功，获得cookie')
          return cookieValue
        }
      }
      throw new Error('未找到accessToken cookie')
    } else {
      throw new Error('登录失败: ' + response.data.message)
    }
  } catch (error) {
    console.error('❌ 登录错误:', error.message)
    if (error.response) {
      console.error('响应状态:', error.response.status)
      console.error('响应数据:', error.response.data)
    }
    return null
  }
}

// 执行单个请求
async function makeRequest(cookie, requestId) {
  const startTime = Date.now()

  try {
    const response = await axios.get(`${config.baseURL}${config.endpoint}`, {
      headers: {
        'Cookie': cookie,
        'User-Agent': `TestClient-${requestId}`
      },
      timeout: config.timeout
    })

    const responseTime = Date.now() - startTime
    stats.responses++
    stats.responseTimes.push(responseTime)

    // 检查是否有缓存相关信息
    const data = response.data
    if (data && typeof data === 'object') {
      if (data.fromCache === true) {
        stats.cacheHits++
      } else if (data.fromCache === false) {
        stats.cacheMisses++
      }
    }

    // 获取数据大小和数量信息
    let dataCount = 0
    if (data && data.data) {
      if (Array.isArray(data.data)) {
        dataCount = data.data.length
      } else if (typeof data.data === 'object') {
        dataCount = 1
      }
    }

    return {
      success: true,
      statusCode: response.status,
      responseTime,
      requestId,
      dataSize: JSON.stringify(response.data).length,
      dataCount,
      fromCache: data?.fromCache
    }
  } catch (error) {
    const responseTime = Date.now() - startTime
    stats.errors++
    stats.responseTimes.push(responseTime)

    return {
      success: false,
      statusCode: error.response?.status || 0,
      responseTime,
      requestId,
      error: error.message,
      errorCode: error.code
    }
  }
}

// 执行并发测试
async function runConcurrentTest(cookie) {
  console.log('\n🔥 开始并发测试...')
  console.log(`📊 配置: ${config.concurrency}并发, ${config.totalRequests}总请求`)
  console.log(`🎯 目标: ${config.baseURL}${config.endpoint}`)
  console.log('=' * 60)

  stats.startTime = Date.now()
  const results = []

  // 分批执行请求
  const batches = Math.ceil(config.totalRequests / config.concurrency)

  for (let batch = 0; batch < batches; batch++) {
    const batchStart = batch * config.concurrency
    const batchEnd = Math.min(batchStart + config.concurrency, config.totalRequests)
    const batchSize = batchEnd - batchStart

    console.log(`\n📦 批次 ${batch + 1}/${batches}: 请求 ${batchStart + 1}-${batchEnd}`)

    const promises = []
    for (let i = 0; i < batchSize; i++) {
      const requestId = batchStart + i + 1
      stats.requests++
      promises.push(makeRequest(cookie, requestId))
    }

    const batchResults = await Promise.all(promises)
    results.push(...batchResults)

    // 显示批次结果
    const batchSuccess = batchResults.filter(r => r.success).length
    const batchFailed = batchResults.filter(r => !r.success).length
    const batchAvgTime = batchResults.reduce((sum, r) => sum + r.responseTime, 0) / batchResults.length
    const batchMinTime = Math.min(...batchResults.map(r => r.responseTime))
    const batchMaxTime = Math.max(...batchResults.map(r => r.responseTime))

    console.log(`   ✅ 成功: ${batchSuccess} | ❌ 失败: ${batchFailed}`)
    console.log(`   ⏱️  响应时间: 平均 ${batchAvgTime.toFixed(2)}ms | 最小 ${batchMinTime}ms | 最大 ${batchMaxTime}ms`)

    // 批次间稍微延迟
    if (batch < batches - 1) {
      await new Promise(resolve => setTimeout(resolve, 100))
    }
  }

  stats.endTime = Date.now()
  return results
}

// 生成测试报告
function generateReport(results) {
  const totalTime = stats.endTime - stats.startTime
  const successCount = results.filter(r => r.success).length
  const failureCount = results.filter(r => !r.success).length

  // 计算响应时间统计
  const responseTimes = stats.responseTimes.sort((a, b) => a - b)
  const avgResponseTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length
  const minResponseTime = Math.min(...responseTimes)
  const maxResponseTime = Math.max(...responseTimes)
  const p50 = responseTimes[Math.floor(responseTimes.length * 0.5)]
  const p90 = responseTimes[Math.floor(responseTimes.length * 0.9)]
  const p95 = responseTimes[Math.floor(responseTimes.length * 0.95)]
  const p99 = responseTimes[Math.floor(responseTimes.length * 0.99)]

  const successRate = (successCount / config.totalRequests * 100).toFixed(2)
  const throughput = (config.totalRequests / (totalTime / 1000)).toFixed(2)

  // 控制台报告
  console.log('\n' + '=' * 60)
  console.log('📊 测试结果报告')
  console.log('=' * 60)
  console.log(`🕐 测试时间: ${new Date().toISOString()}`)
  console.log(`⚙️  测试配置: ${config.concurrency}并发 × ${config.totalRequests}请求`)
  console.log(`🎯 测试接口: ${config.endpoint}`)
  console.log('')
  console.log('📈 基础统计:')
  console.log(`   总请求数: ${config.totalRequests}`)
  console.log(`   成功请求: ${successCount}`)
  console.log(`   失败请求: ${failureCount}`)
  console.log(`   成功率: ${successRate}%`)
  console.log(`   总耗时: ${totalTime}ms`)
  console.log(`   吞吐量: ${throughput} 请求/秒`)
  console.log('')
  console.log('⏱️  响应时间分布:')
  console.log(`   平均值: ${avgResponseTime.toFixed(2)}ms`)
  console.log(`   最小值: ${minResponseTime}ms`)
  console.log(`   最大值: ${maxResponseTime}ms`)
  console.log(`   P50: ${p50}ms`)
  console.log(`   P90: ${p90}ms`)
  console.log(`   P95: ${p95}ms`)
  console.log(`   P99: ${p99}ms`)

  // 缓存统计
  if (stats.cacheHits > 0 || stats.cacheMisses > 0) {
    const cacheHitRate = (stats.cacheHits / (stats.cacheHits + stats.cacheMisses) * 100).toFixed(2)
    console.log('')
    console.log('🎯 缓存统计:')
    console.log(`   缓存命中: ${stats.cacheHits}`)
    console.log(`   缓存未命中: ${stats.cacheMisses}`)
    console.log(`   缓存命中率: ${cacheHitRate}%`)
  }

  // 错误统计
  if (failureCount > 0) {
    console.log('')
    console.log('❌ 错误统计:')
    const errorTypes = {}
    results.filter(r => !r.success).forEach(r => {
      const key = `${r.statusCode} - ${r.error}`
      errorTypes[key] = (errorTypes[key] || 0) + 1
    })

    Object.entries(errorTypes).forEach(([error, count]) => {
      console.log(`   ${error}: ${count}次`)
    })
  }

  console.log('=' * 60)

  return {
    timestamp: new Date().toISOString(),
    config: { ...config },
    stats: {
      totalRequests: config.totalRequests,
      successCount,
      failureCount,
      successRate: parseFloat(successRate),
      totalTime,
      throughput: parseFloat(throughput),
      responseTime: {
        avg: parseFloat(avgResponseTime.toFixed(2)),
        min: minResponseTime,
        max: maxResponseTime,
        p50,
        p90,
        p95,
        p99
      },
      cache: {
        hits: stats.cacheHits,
        misses: stats.cacheMisses,
        hitRate: stats.cacheHits > 0 ? parseFloat((stats.cacheHits / (stats.cacheHits + stats.cacheMisses) * 100).toFixed(2)) : 0
      }
    },
    errors: failureCount > 0 ? results.filter(r => !r.success).map(r => ({
      requestId: r.requestId,
      statusCode: r.statusCode,
      error: r.error,
      responseTime: r.responseTime
    })) : []
  }
}

// 保存报告到文件
function saveReportToFile(report) {
  try {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    const filename = `test-report-${timestamp}.json`
    const filepath = path.join(__dirname, 'test-reports', filename)

    // 确保目录存在
    const dir = path.dirname(filepath)
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true })
    }

    fs.writeFileSync(filepath, JSON.stringify(report, null, 2))
    console.log(`\n📄 测试报告已保存: ${filename}`)

    // 同时保存一个最新的报告
    const latestPath = path.join(__dirname, 'test-reports', 'latest-report.json')
    fs.writeFileSync(latestPath, JSON.stringify(report, null, 2))

    return filepath
  } catch (error) {
    console.error('❌ 保存报告失败:', error.message)
    return null
  }
}

// 主函数
async function main() {
  try {
    // 解析命令行参数
    parseArgs()

    console.log('🚀 认证缓存并发性能测试')
    console.log(`📊 测试配置: ${config.concurrency}并发, ${config.totalRequests}总请求`)
    console.log(`🎯 测试接口: ${config.endpoint}`)

    // 获取cookie
    let cookie = config.defaultCookie
    if (!cookie) {
      cookie = await login()
      if (!cookie) {
        console.error('❌ 无法获取cookie，测试终止')
        process.exit(1)
      }
    } else {
      console.log('✅ 使用指定的cookie')
    }

    // 预热请求
    console.log('\n🔥 预热请求（建立缓存）...')
    const warmupResult = await makeRequest(cookie, 'WARMUP')
    if (warmupResult.success) {
      console.log(`✅ 预热成功 | 响应时间: ${warmupResult.responseTime}ms`)
    } else {
      console.log(`❌ 预热失败: ${warmupResult.error}`)
    }

    // 等待1秒确保缓存生效
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 执行并发测试
    const results = await runConcurrentTest(cookie)

    // 生成并保存报告
    const report = generateReport(results)
    saveReportToFile(report)

    // 性能评估
    console.log('\n🔍 性能评估:')
    if (report.stats.successRate < 95) {
      console.log('❌ 成功率低于95%，系统可能不稳定')
    } else if (report.stats.responseTime.p95 > report.stats.responseTime.avg * 3) {
      console.log('⚠️  P95响应时间过高，可能存在性能问题')
    } else {
      console.log('✅ 系统性能表现良好')
    }

    console.log('\n✅ 测试完成！')

  } catch (error) {
    console.error('❌ 测试执行失败:', error.message)
    process.exit(1)
  }
}

// 处理程序退出
process.on('SIGINT', () => {
  console.log('\n🛑 测试被中断')
  if (stats.endTime === 0) {
    stats.endTime = Date.now()
  }
  process.exit(0)
})

// 启动测试
if (require.main === module) {
  main()
}

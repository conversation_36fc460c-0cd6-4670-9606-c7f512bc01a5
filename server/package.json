{"name": "web-panel-server", "version": "1.0.0", "description": "海聚科技设备管理系统服务端", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "security-check": "node scripts/security-check.js", "generate-jwt-secret": "node -e \"console.log(require('crypto').randomBytes(64).toString('hex'))\"", "audit-security": "npm audit && npm run security-check", "fix-security": "npm audit fix", "setup-logs": "node scripts/setup-logs.js", "clean-logs": "node scripts/clean-logs.js", "clean-logs-dry": "node scripts/clean-logs.js --dry-run", "logs:pm2": "pm2 logs device-management", "logs:winston": "tail -f logs/combined.log", "logs:error": "tail -f logs/error.log"}, "dependencies": {"@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.9", "acorn": "^8.15.0", "acorn-walk": "^8.3.4", "ali-oss": "^6.22.0", "axios": "^1.8.4", "bcryptjs": "^3.0.2", "body-parser": "^1.20.2", "compression": "^1.8.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "isolated-vm": "^5.0.4", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "node-cache": "^5.1.2", "pg": "^8.14.1", "pg-hstore": "^2.3.4", "sequelize": "^6.37.7", "uuid": "^11.1.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@types/ali-oss": "^6.16.11", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/jsonwebtoken": "^9.0.9", "@types/morgan": "^1.9.9", "@types/node": "^20.17.30", "@types/uuid": "^10.0.0", "@types/winston": "^2.4.4", "jest": "^29.7.0", "nodemon": "^3.1.9", "sequelize-cli": "^6.6.2", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}
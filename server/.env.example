# 服务器配置
PORT=3001
NODE_ENV=development

# JWT配置 - 生产环境必须使用强密钥（至少64个字符）
JWT_SECRET=your_jwt_secret_key_should_be_at_least_64_characters_long_and_very_complex_with_random_characters_123456789
JWT_REFRESH_SECRET=different_refresh_secret_key_should_be_at_least_64_characters_long_and_different_from_access_token_secret_987654321
JWT_ACCESS_TOKEN_EXPIRY=2h
JWT_REFRESH_TOKEN_EXPIRY=7d

# PostgreSQL数据库配置
DB_NAME=your_database_name
DB_USER=your_database_user
DB_PASSWORD=your_strong_database_password_with_special_chars_123!@#
DB_HOST=localhost
DB_PORT=5432

# 阿里云OSS配置
OSS_REGION=oss-cn-hangzhou
OSS_ACCESS_KEY_ID=your_oss_access_key_id
OSS_ACCESS_KEY_SECRET=your_oss_access_key_secret
OSS_BUCKET=your_oss_bucket_name

# 安全配置
CORS_ORIGINS=http://localhost:5173,http://localhost:3000,https://yourdomain.com
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX=1000
LOGIN_RATE_LIMIT_MAX=5
SESSION_TIMEOUT=900000

# 是否启用DDoS防护 - 默认启用
DDOS_PROTECTION_ENABLED=true
# 每秒最大请求数 - 默认20
DDOS_MAX_REQUESTS_PER_SECOND=20
# 每分钟最大请求数 - 默认300
DDOS_MAX_REQUESTS_PER_MINUTE=300
# 最大并发连接数 - 默认100
DDOS_MAX_CONCURRENT_CONNECTIONS=100
# 检测时间窗口（毫秒）- 默认1分钟
DDOS_DETECTION_WINDOW_MS=60000
# 封禁时长（毫秒）- 默认10分钟
DDOS_BAN_DURATION_MS=600000
# 最大封禁次数 - 默认3次
DDOS_MAX_BAN_COUNT=3
# IP白名单（逗号分隔）- 默认本地IP
DDOS_IP_WHITELIST=127.0.0.1,::1
# IP黑名单（逗号分隔）- 默认为空
DDOS_IP_BLACKLIST=


# Cookie安全设置 - 当前生产环境使用HTTP，所以设置为false
COOKIE_SECURE=false
# Cookie SameSite策略 - 开发环境建议使用lax，生产环境使用strict
COOKIE_SAME_SITE=lax

# 日志配置
LOG_LEVEL=info
LOG_FILE_PATH=./logs

# 算法执行安全配置
ALGORITHM_MAX_EXECUTION_TIME=30000
ALGORITHM_MAX_MEMORY=134217728
ALGORITHM_MAX_CODE_SIZE=1048576

# 钉钉授权配置
DINGTALK_CLIENT_ID=
DINGTALK_CLIENT_SECRET=
DINGTALK_HOST=
DINGTALK_AUTH_TOKEN=
DINGTALK_USER_INFO=

# 缓存配置
CACHE_DEFAULT_TTL=600
CACHE_DEVICE_TTL=300
CACHE_ALGORITHM_TTL=1800
CACHE_TEMPLATE_TTL=3600
CACHE_MAX_KEYS=10000
CACHE_LOCK_TIMEOUT=30000
CACHE_ENABLE_LOCK=true

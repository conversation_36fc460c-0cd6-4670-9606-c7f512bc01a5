# 开发指南

## 开发环境要求
- Node.js >= 16
- npm >= 8
- PostgreSQL >= 12

## 开发流程
1. 克隆项目
2. 安装依赖
3. 配置环境变量
4. 启动开发服务器
5. 编写代码
6. 运行测试
7. 提交代码

## 代码规范
- 使用TypeScript
- 使用ESLint和Prettier
- 遵循Vue 3组合式API风格
- 遵循RESTful API设计规范

## 目录结构
```
web-panel/
├── client/            # 前端源码
│   ├── src/          # 源代码
│   ├── tests/        # 测试文件
│   └── types/        # 类型定义
├── server/           # 后端服务
│   ├── src/          # 源代码
│   ├── tests/        # 测试文件
│   └── types/        # 类型定义
└── docs/             # 文档
``` 
# 测试文档

## 测试类型
- 单元测试: 使用Jest测试独立的功能模块
- 集成测试: 测试多个模块的交互
- E2E测试: 测试完整的用户流程
- 性能测试: 测试系统性能和负载能力

## 测试工具
- Jest: 单元测试框架
- Vue Test Utils: Vue组件测试
- Supertest: API测试
- Cypress: E2E测试

## 测试命令
```bash
# 运行所有测试
npm test

# 运行单元测试
npm run test:unit

# 运行E2E测试
npm run test:e2e

# 运行性能测试
npm run test:performance
```

## 测试覆盖率
- 单元测试覆盖率要求: > 80%
- 集成测试覆盖率要求: > 60%
- E2E测试覆盖率要求: > 40%

## 目录说明
- [单元测试](./unit.md)
- [E2E测试](./e2e.md)
- [性能测试](./performance.md) 
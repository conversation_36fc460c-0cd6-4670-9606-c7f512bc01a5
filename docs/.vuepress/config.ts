import { defineUserConfig } from 'vuepress'
import { defaultTheme } from '@vuepress/theme-default'

export default defineUserConfig({
  lang: 'zh-CN',
  title: '海聚科技设备管理系统',
  description: '海聚科技设备管理系统文档',
  theme: defaultTheme({
    navbar: [
      {
        text: '首页',
        link: '/'
      },
      {
        text: '开发指南',
        link: '/guide/'
      },
      {
        text: 'API文档',
        link: '/api/'
      },
      {
        text: '部署文档',
        link: '/deploy/'
      },
      {
        text: '测试文档',
        link: '/test/'
      }
    ],
    sidebar: {
      '/guide/': [
        {
          text: '开发指南',
          children: [
            '/guide/README.md',
            '/guide/development.md',
            '/guide/architecture.md',
            '/guide/contribution.md'
          ]
        }
      ],
      '/api/': [
        {
          text: 'API文档',
          children: ['/api/README.md', '/api/auth.md', '/api/device.md', '/api/user.md']
        }
      ],
      '/deploy/': [
        {
          text: '部署文档',
          children: [
            '/deploy/README.md',
            '/deploy/environment.md',
            '/deploy/nginx.md',
            '/deploy/mongodb.md'
          ]
        }
      ],
      '/test/': [
        {
          text: '测试文档',
          children: ['/test/README.md', '/test/unit.md', '/test/e2e.md', '/test/performance.md']
        }
      ]
    }
  })
})

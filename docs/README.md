# 海聚科技设备管理系统文档

## 项目介绍
海聚科技设备管理系统是一个前后端一体化的设备管理平台，采用 Vue 3 + Node.js Express 技术栈构建。

## 技术架构
- 前端: Vue 3 + Element Plus + Axios
- 后端: Node.js + Express
- 数据库: MongoDB

## 快速开始

### 安装依赖
```bash
pnpm install
```

### 开发环境启动
```bash
pnpm dev
```

### 生产环境部署
```bash
pnpm build
pnpm start
```

## 文档目录

### 📚 核心文档
- [开发指南](./guide/) - 项目开发规范和指南
- [API文档](./api/) - 后端API接口文档
- [部署文档](./deploy/) - 系统部署和运维指南
- [测试文档](./test/) - 测试策略和用例
- [安全文档](./security/) - 系统安全配置和最佳实践

### ⚡ 性能优化文档
- [防抖节流优化](./performance/debounce/) - 完整的防抖节流性能优化文档体系
  - [优化总览](./performance/debounce/README.md) - 防抖优化项目概述
  - [性能优化详情](./performance/debounce/performance-optimization.md) - 详细的技术实现和效果
  - [架构设计](./performance/debounce/architecture-design.md) - 系统架构和设计原理
  - [开发指南](./performance/debounce/development-guide.md) - 开发规范和最佳实践
  - [功能路线图](./performance/debounce/roadmap.md) - 下一步功能发展计划
  - [项目总结](./performance/debounce/project-summary.md) - 项目完成情况总结

### 🔬 算法文档
- [算法使用指南](./algorithm/) - 数据处理算法相关文档
  - [数据清洗算法](./algorithm/data-cleaning-algorithm-usage.md)
  - [控制算法](./algorithm/control-algorithm-usage.md)
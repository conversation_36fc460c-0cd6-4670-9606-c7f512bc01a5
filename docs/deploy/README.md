# 部署文档

## 环境要求
- Node.js >= 16
- MongoDB >= 4.4 或 PostgreSQL >= 12
- Nginx >= 1.18
- PM2 >= 5

## 部署架构说明

### 推荐部署架构（同源部署）

本项目推荐使用同源部署架构，即前端和后端部署在同一服务器上：

```
┌─────────────────────────────────────┐
│           同一服务器                  │
│  ┌─────────────┐  ┌─────────────┐   │
│  │   Nginx     │  │  Node.js    │   │
│  │  (静态文件)  │  │   (API)     │   │
│  │   :80/443   │  │   :3001     │   │
│  └─────────────┘  └─────────────┘   │
└─────────────────────────────────────┘
```

**优势：**
- ✅ 无 CORS 问题（所有请求都是同源）
- ✅ 更好的性能和安全性
- ✅ 简化的部署流程
- ✅ 减少网络延迟

**工作原理：**
- Nginx 提供前端静态文件服务
- API 请求通过 Nginx 反向代理到 Node.js
- 用户看到的所有请求都来自同一个域名和端口

### CORS 配置说明

由于采用同源部署，大部分请求不会有 Origin 头，这是正常现象：
- 浏览器对同源请求不发送 Origin 头
- 直接访问 API 也不会有 Origin 头
- 只有真正的跨域请求才会发送 Origin 头

当前 CORS 配置已优化支持这种架构，无需额外配置。

## 部署步骤概览
1. 准备服务器环境
2. 后端服务部署
3. 前端应用部署
4. Nginx配置
5. 启动与验证

## 1. 准备服务器环境

### 基础软件安装

```bash
# 更新软件包
apt update
apt upgrade -y

# 安装必要工具
apt install -y curl wget vim git unzip
```

### 安装Node.js

```bash
# 添加Node.js源
curl -fsSL https://deb.nodesource.com/setup_20.x | bash -

# 安装Node.js
apt install -y nodejs

# 验证安装
node -v
npm -v
```

### 安装PM2

```bash
# 全局安装PM2
npm install -g pm2

# 验证PM2安装
pm2 --version
```

### 安装Nginx

```bash
# 安装Nginx
apt install -y nginx

# 启动Nginx并设置开机自启
systemctl start nginx
systemctl enable nginx

# 验证Nginx状态
systemctl status nginx
```

### 创建应用目录

```bash
# 创建应用目录
mkdir -p /var/www/device-management/server
mkdir -p /var/www/device-management/client/dist
mkdir -p /var/www/device-management/logs
```

## 2. 后端服务部署

### 2.1 上传后端代码

选择以下三种方式之一部署后端代码：

**方式1：使用Git克隆整个仓库**

```bash
# 进入应用目录
cd /var/www/device-management

# 克隆代码仓库
git clone <repository-url> .
```

**方式2：SFTP上传代码**

在本地使用FileZilla等SFTP工具，将代码上传至服务器的应用目录。

**方式3：本地构建后上传**

在本地构建后端代码，然后上传到服务器：

```bash
# 本地构建
npm run build:server

# 将构建后的文件打包
tar -czf server-dist.tar.gz -C server/dist .

# 上传到服务器
scp server-dist.tar.gz root@您的服务器IP:/var/www/device-management/server/

# 在服务器上解压到dist目录
cd /var/www/device-management/server/
mkdir -p dist
cd dist
tar -xzf ../server-dist.tar.gz
rm ../server-dist.tar.gz
```

### 2.2 安装后端依赖

```bash
# 进入应用目录
cd /var/www/device-management

# 安装项目依赖
npm install --production --ignore-scripts
```

> 注意：如果遇到husky安装错误，可以使用`--ignore-scripts`参数跳过git hooks安装

### 2.3 配置后端环境变量

在项目根目录创建`.env`文件：

```bash
cat > /var/www/device-management/.env << 'EOL'
# 服务器配置
PORT=3001
NODE_ENV=production

# JWT配置（必须使用强密钥，至少64个字符）
JWT_SECRET="your_64_character_secure_jwt_secret_here_replace_with_actual_strong_key"

# PostgreSQL数据库配置
DB_NAME=web_panel_prod
DB_USER=web_panel_user
DB_PASSWORD="your_strong_database_password_here"
DB_HOST=localhost
DB_PORT=5432

# 阿里云OSS配置
OSS_REGION=oss-cn-hangzhou
OSS_ACCESS_KEY_ID=your_secure_access_key_id
OSS_ACCESS_KEY_SECRET=your_secure_access_key_secret
OSS_BUCKET=your-secure-bucket-name

# 安全配置
# 注意：同源部署时大部分请求无Origin头，这是正常的
CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX=1000
LOGIN_RATE_LIMIT_MAX=5
SESSION_TIMEOUT=900000

# 日志配置
LOG_LEVEL=warn
LOG_FILE_PATH=./logs

# 算法执行安全配置
ALGORITHM_MAX_EXECUTION_TIME=30000
ALGORITHM_MAX_MEMORY=134217728
ALGORITHM_MAX_CODE_SIZE=1048576
EOL
```

**重要安全提醒：**
- 必须替换所有默认值为实际的强密钥
- JWT_SECRET 至少需要64个字符的强随机密钥
- 数据库密码应包含大小写字母、数字和特殊字符
- 生产环境必须使用新的阿里云OSS密钥

### 2.4 创建PM2配置文件

```bash
cat > /var/www/device-management/ecosystem.config.js << 'EOL'
module.exports = {
  apps: [{
    name: "device-management",
    script: "./server/dist/index.js",
    instances: "max",
    exec_mode: "cluster",
    env: {
      NODE_ENV: "production",
      PORT: 3001
    },
    watch: false,
    max_memory_restart: "500M",
    log_date_format: "YYYY-MM-DD HH:mm:ss",
    error_file: "./logs/error.log",
    out_file: "./logs/out.log",
    merge_logs: true
  }]
};
EOL
```

### 2.5 启动后端服务

```bash
# 进入应用目录
cd /var/www/device-management

# 启动服务
pm2 start ecosystem.config.js

# 设置PM2开机自启
pm2 startup
pm2 save
```

### 2.6 验证后端服务状态

```bash
# 查看PM2进程状态
pm2 status

# 检查API是否正常响应
curl http://localhost:3001/api/health

# 查看应用日志
pm2 logs device-management
```

## 3. 前端应用部署

### 3.1 本地构建前端代码

在本地开发环境中构建前端代码：

```bash
# 进入项目目录
cd web-panel

# 构建前端代码
npm run build:client

# 如果遇到TypeScript错误，可以跳过类型检查
cd client && npx vite build
```

构建完成后，会在`client/dist`目录生成静态文件。

### 3.2 上传前端构建文件

将本地生成的dist目录内容上传到服务器：

```bash
# 从本地上传到服务器
scp -r client/dist/* root@您的服务器IP:/var/www/device-management/client/dist/

# 或使用rsync（更高效）
rsync -avz client/dist/ root@您的服务器IP:/var/www/device-management/client/dist/
```

### 3.3 设置前端文件权限

```bash
# 设置适当的文件权限
chmod -R 755 /home/<USER>/web-panel/client/dist
chown -R www-data:www-data /home/<USER>/web-panel/client/dist
```

### 3.4 验证前端文件

```bash
# 检查文件是否正确上传
ls -la /home/<USER>/web-panel/client//dist/

# 确认index.html存在
cat /home/<USER>/web-panel/client//dist/index.html | head -10
```

## 4. Nginx配置

### 4.1 创建Nginx配置文件

```bash
# 创建Nginx配置
cat > /etc/nginx/sites-available/device-management << 'EOL'
server {
    listen 80 default_server;
    server_name _;  # 使用实际域名替换 _

    access_log /var/log/nginx/device-management-access.log;
    error_log /var/log/nginx/device-management-error.log;

    # 安全头配置
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy "strict-origin-when-cross-origin";

    # 隐藏服务器信息
    server_tokens off;

    # 请求大小限制
    client_max_body_size 10M;

    # 前端静态文件
    location / {
        root /var/www/device-management/client/dist;
        try_files $uri $uri/ /index.html;
        index index.html;

        # 静态文件缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # API请求代理
    location /api {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # API请求超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
EOL
```

### 4.2 启用Nginx配置

```bash
# 创建符号链接
ln -sf /etc/nginx/sites-available/device-management /etc/nginx/sites-enabled/default

# 测试Nginx配置
nginx -t

# 如果配置正确，重新加载Nginx
nginx -s reload
```

## 5. 验证完整部署

在浏览器中访问服务器IP地址或域名，应该能看到应用的前端界面。尝试登录或使用其他功能，确认前后端连接正常。

## 6. 故障排查

### 6.1 后端服务故障排查

```bash
# 查看PM2进程状态
pm2 status

# 查看应用日志
pm2 logs device-management

# Part 80 or 3001 already in use
netstat -tulpn | grep 3001

# 检查错误日志
cat /var/www/device-management/logs/error.log
```

### 6.2 前端应用故障排查

```bash
# 检查静态文件是否存在
ls -la /var/www/device-management/client/dist

# 检查文件权限
ls -la /var/www/device-management/client/dist/index.html
```

### 6.3 Nginx故障排查

```bash
# 检查Nginx错误日志
tail -n 50 /var/log/nginx/error.log
tail -n 50 /var/log/nginx/device-management-error.log

# 验证Nginx配置是否正确
nginx -t

# 检查Nginx是否运行
systemctl status nginx
```

## 7. 安全性建议

### 7.1 服务器安全

```bash
# 配置防火墙（Ubuntu/Debian）
ufw enable
ufw default deny incoming
ufw default allow outgoing
ufw allow 22    # SSH
ufw allow 80    # HTTP
ufw allow 443   # HTTPS

# 禁用不必要的服务
systemctl disable apache2  # 如果安装了Apache
systemctl stop apache2
```

### 7.2 SSL证书配置（推荐）

```bash
# 安装Certbot
apt install certbot python3-certbot-nginx

# 获取SSL证书
certbot --nginx -d yourdomain.com

# 自动续期
crontab -e
# 添加以下行：
# 0 12 * * * /usr/bin/certbot renew --quiet
```

### 7.3 安全检查清单

**部署前必须完成：**
- [ ] 更换所有默认密钥和密码
- [ ] 生成强随机JWT密钥（至少64字符）
- [ ] 更新阿里云OSS密钥
- [ ] 配置强数据库密码
- [ ] 设置防火墙规则
- [ ] 配置SSL证书
- [ ] 验证文件权限设置

**定期维护：**
- [ ] 更新系统和软件包
- [ ] 检查安全日志
- [ ] 轮换密钥和密码
- [ ] 备份数据库和配置

## 8. 备份策略

- 定期备份数据库
- 备份应用代码和配置文件
- 考虑使用自动备份工具

## 9. 环境变量管理

不同环境使用不同的配置文件：

- 开发环境：`.env.development`
- 测试环境：`.env.test`
- 生产环境：`.env`

确保这些文件放在项目根目录，并且已在`.gitignore`中排除，避免敏感信息泄露。

## 10. 重要文件位置参考

- 前端代码：`/var/www/device-management/client/dist/`
- 后端代码：`/var/www/device-management/server/`
- Nginx配置：`/etc/nginx/sites-available/device-management`
- PM2配置：`/var/www/device-management/ecosystem.config.js`
- 环境变量：`/var/www/device-management/.env`
- 日志文件：
  - 应用日志：`/var/www/device-management/logs/`
  - Nginx日志：`/var/log/nginx/`

## 11. 服务器目录结构

推荐的生产环境目录结构：

```
/var/www/device-management/
├── client/               # 前端应用
│   └── dist/             # 构建后的静态文件
├── server/               # 后端服务
│   ├── dist/             # 构建后的后端代码
│   │   ├── config/       # 配置文件
│   │   ├── controllers/  # 控制器
│   │   ├── models/       # 数据模型
│   │   ├── routes/       # 路由
│   │   └── index.js      # 入口文件
│   └── src/              # 源代码(可选)
├── logs/                 # 日志文件
├── .env                  # 环境变量
├── package.json          # 项目配置
└── ecosystem.config.js   # PM2配置
```

## 环境变量配置

### 生产环境配置示例

```env
# 服务器配置
PORT=3001
NODE_ENV=production

# JWT配置（必须使用强密钥）
JWT_SECRET="your_64_character_secure_jwt_secret_here_replace_with_actual_strong_key"

# PostgreSQL数据库配置
DB_NAME=web_panel_prod
DB_USER=web_panel_user
DB_PASSWORD="your_strong_database_password_here"
DB_HOST=localhost
DB_PORT=5432

# 阿里云OSS配置
OSS_REGION=oss-cn-hangzhou
OSS_ACCESS_KEY_ID=your_secure_access_key_id
OSS_ACCESS_KEY_SECRET=your_secure_access_key_secret
OSS_BUCKET=your-secure-bucket-name

# 安全配置
CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX=1000
LOGIN_RATE_LIMIT_MAX=5
SESSION_TIMEOUT=900000

# 日志配置
LOG_LEVEL=warn
LOG_FILE_PATH=./logs

# 算法执行安全配置
ALGORITHM_MAX_EXECUTION_TIME=30000
ALGORITHM_MAX_MEMORY=134217728
ALGORITHM_MAX_CODE_SIZE=1048576
```

### 密钥生成工具

```bash
# 生成安全的JWT密钥
node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"

# 运行安全检查
npm run security-check
```

## 目录说明
- [环境配置](./environment.md)
- [Nginx配置](./nginx.md)
- [MongoDB配置](./mongodb.md) 
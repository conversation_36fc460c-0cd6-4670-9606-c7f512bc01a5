# 海聚科技设备管理系统 - 开发指南

## 📋 目录

- [开发环境搭建](#开发环境搭建)
- [项目结构说明](#项目结构说明)
- [开发规范](#开发规范)
- [防抖节流使用指南](#防抖节流使用指南)
- [API开发指南](#api开发指南)
- [组件开发指南](#组件开发指南)
- [测试指南](#测试指南)
- [部署指南](#部署指南)

## 🛠️ 开发环境搭建

### 环境要求

| 工具 | 版本要求 | 说明 |
|------|----------|------|
| Node.js | >= 16.0.0 | JavaScript运行环境 |
| npm | >= 8.0.0 | 包管理工具 |
| PostgreSQL | >= 12.0 | 数据库 |
| Redis | >= 6.0 | 缓存数据库 |
| Git | >= 2.20.0 | 版本控制 |

### 快速开始

```bash
# 1. 克隆项目
git clone <repository-url>
cd web-panel

# 2. 安装前端依赖
cd client
npm install

# 3. 安装后端依赖
cd ../server
npm install

# 4. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，配置数据库连接等信息

# 5. 初始化数据库
npm run db:migrate
npm run db:seed

# 6. 启动开发服务器
# 终端1: 启动后端
npm run dev

# 终端2: 启动前端
cd ../client
npm run dev
```

### 开发工具推荐

```json
{
  "vscode_extensions": [
    "Vue.volar",
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint"
  ],
  "chrome_extensions": [
    "Vue.js devtools",
    "React Developer Tools"
  ]
}
```

## 📁 项目结构说明

### 整体结构

```
web-panel/
├── client/                 # 前端项目
│   ├── src/
│   │   ├── api/           # API接口定义
│   │   ├── components/    # 公共组件
│   │   ├── views/         # 页面组件
│   │   ├── utils/         # 工具函数
│   │   ├── store/         # 状态管理
│   │   └── router/        # 路由配置
│   ├── public/            # 静态资源
│   └── package.json       # 前端依赖
├── server/                # 后端项目
│   ├── src/
│   │   ├── controllers/   # 控制器
│   │   ├── models/        # 数据模型
│   │   ├── routes/        # 路由定义
│   │   ├── middleware/    # 中间件
│   │   ├── utils/         # 工具函数
│   │   └── config/        # 配置文件
│   ├── logs/              # 日志文件
│   └── package.json       # 后端依赖
├── doc/                   # 项目文档
│   ├── performance-optimization.md
│   ├── architecture-design.md
│   └── development-guide.md
└── README.md              # 项目说明
```

### 关键目录说明

#### 前端关键目录

| 目录 | 用途 | 重要文件 |
|------|------|----------|
| `src/api/` | API接口定义 | `device.ts`, `algorithm.ts` |
| `src/utils/` | 工具函数 | `debounce.ts`, `utils.ts` |
| `src/components/` | 公共组件 | `DrillCurveChartCard.vue` |
| `src/views/` | 页面组件 | `device/`, `algorithm/` |

#### 后端关键目录

| 目录 | 用途 | 重要文件 |
|------|------|----------|
| `src/controllers/` | 业务逻辑 | `device.ts`, `user.ts` |
| `src/models/` | 数据模型 | `Device.ts`, `User.ts` |
| `src/utils/` | 工具函数 | `algorithmExecutor.ts` |
| `src/middleware/` | 中间件 | `auth.ts`, `cors.ts` |

## 📝 开发规范

### 代码规范

#### TypeScript规范

```typescript
// ✅ 推荐写法
interface DeviceData {
  id: number
  deviceName: string
  status: 'online' | 'offline'
  createdAt: Date
}

const fetchDeviceData = async (id: number): Promise<DeviceData> => {
  try {
    const response = await api.get(`/devices/${id}`)
    return response.data
  } catch (error) {
    console.error('获取设备数据失败:', error)
    throw error
  }
}

// ❌ 不推荐写法
const fetchDeviceData = async (id) => {
  const response = await api.get(`/devices/${id}`)
  return response.data
}
```

#### Vue组件规范

```vue
<!-- ✅ 推荐写法 -->
<template>
  <div class="device-card">
    <h3 class="device-name">{{ device.name }}</h3>
    <p class="device-status" :class="statusClass">
      {{ device.status }}
    </p>
    <el-button 
      type="primary" 
      @click="debouncedHandleClick"
    >
      操作
    </el-button>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { debouncedApiCall } from '@/utils/debounce'

interface Device {
  id: number
  name: string
  status: string
}

interface Props {
  device: Device
}

const props = defineProps<Props>()

const statusClass = computed(() => ({
  'status-online': props.device.status === 'online',
  'status-offline': props.device.status === 'offline'
}))

const handleClick = () => {
  // 处理点击逻辑
}

const debouncedHandleClick = debouncedApiCall(handleClick)
</script>
```

#### 命名规范

| 类型 | 规范 | 示例 |
|------|------|------|
| 文件名 | kebab-case | `device-list.vue` |
| 组件名 | PascalCase | `DeviceList` |
| 变量名 | camelCase | `deviceData` |
| 常量名 | UPPER_SNAKE_CASE | `API_BASE_URL` |
| 函数名 | camelCase | `fetchDeviceData` |
| CSS类名 | kebab-case | `device-card` |

### Git提交规范

```bash
# 提交格式
<type>(<scope>): <subject>

# 类型说明
feat:     新功能
fix:      修复bug
docs:     文档更新
style:    代码格式调整
refactor: 代码重构
perf:     性能优化
test:     测试相关
chore:    构建工具或辅助工具的变动

# 示例
feat(device): 添加设备搜索防抖功能
fix(chart): 修复图表数据监听问题
docs(api): 更新API文档
perf(debounce): 优化防抖工具性能
```

## ⚡ 防抖节流使用指南

### 基础使用

```typescript
import { 
  debouncedApiCall, 
  debouncedSearch, 
  throttledDrag,
  debouncedChartUpdate 
} from '@/utils/debounce'

// 1. API调用防抖
const handleSearch = debouncedApiCall(() => {
  currentPage.value = 1
  fetchDevices()
})

// 2. 搜索输入防抖
const handleFilter = debouncedSearch(() => {
  filterResults()
})

// 3. 拖拽操作节流
const handleDrag = throttledDrag((event) => {
  updatePosition(event)
})

// 4. 图表更新防抖
const updateChart = debouncedChartUpdate(() => {
  nextTick(initChart)
})
```

### 高级用法

```typescript
import { 
  createDebounced, 
  createThrottled, 
  useDebounce,
  DEBOUNCE_DELAYS 
} from '@/utils/debounce'

// 1. 自定义延迟时间
const customDebounced = createDebounced(myFunction, 800)

// 2. Vue 3 Composition API
export default {
  setup() {
    const { debouncedFn, cancel, flush } = useDebounce(() => {
      // 防抖逻辑
    }, 300)
    
    // 组件卸载时清理
    onUnmounted(() => {
      cancel()
    })
    
    return { debouncedFn }
  }
}

// 3. 使用预定义常量
const chartUpdate = createDebounced(
  updateChart, 
  DEBOUNCE_DELAYS.CHART_UPDATE
)
```

### 最佳实践

#### ✅ 推荐场景

| 场景 | 推荐函数 | 延迟时间 | 说明 |
|------|----------|----------|------|
| 搜索输入 | `debouncedSearch` | 300ms | 用户输入防抖 |
| API调用 | `debouncedApiCall` | 500ms | 避免重复请求 |
| 图表更新 | `debouncedChartUpdate` | 100ms | 数据变化防抖 |
| 拖拽操作 | `throttledDrag` | 16ms | 保持60fps |
| MQTT消息 | `throttledMqtt` | 1000ms | 高频消息节流 |

#### ❌ 避免场景

- 不要对所有函数都使用防抖/节流
- 不要在关键业务逻辑中使用过长的延迟
- 不要忘记在组件卸载时清理防抖函数

## 🔌 API开发指南

### RESTful API规范

```typescript
// 控制器示例
export const getDeviceList = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { page = 1, pageSize = 10, search = '' } = req.query
    
    const whereClause: any = {}
    if (search) {
      whereClause.deviceName = {
        [Op.iLike]: `%${search}%`
      }
    }
    
    const { count, rows } = await Device.findAndCountAll({
      where: whereClause,
      limit: Number(pageSize),
      offset: (Number(page) - 1) * Number(pageSize),
      order: [['createdAt', 'DESC']]
    })
    
    res.json({
      success: true,
      data: {
        list: rows,
        total: count,
        page: Number(page),
        pageSize: Number(pageSize)
      }
    })
  } catch (error) {
    next(error)
  }
}
```

### API响应格式

```typescript
// 成功响应
interface SuccessResponse<T> {
  success: true
  data: T
  message?: string
}

// 错误响应
interface ErrorResponse {
  success: false
  message: string
  code?: number
  details?: any
}

// 分页响应
interface PaginatedResponse<T> {
  success: true
  data: {
    list: T[]
    total: number
    page: number
    pageSize: number
  }
}
```

### 错误处理

```typescript
// 统一错误处理中间件
export const errorHandler = (
  error: any,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  console.error('API错误:', error)
  
  if (error instanceof AppError) {
    res.status(error.statusCode).json({
      success: false,
      message: error.message,
      code: error.code
    })
  } else {
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      code: -1
    })
  }
}
```

## 🧩 组件开发指南

### 组件设计原则

1. **单一职责** - 每个组件只负责一个功能
2. **可复用性** - 通过props和slots提供灵活性
3. **性能优化** - 合理使用防抖节流和缓存
4. **类型安全** - 使用TypeScript定义接口

### 组件模板

```vue
<template>
  <div class="my-component">
    <div class="component-header">
      <h3>{{ title }}</h3>
      <slot name="header-actions" />
    </div>
    
    <div class="component-content">
      <slot />
    </div>
    
    <div class="component-footer" v-if="$slots.footer">
      <slot name="footer" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted } from 'vue'
import { debouncedResize } from '@/utils/debounce'

interface Props {
  title: string
  loading?: boolean
  height?: number
}

interface Emits {
  (e: 'update', data: any): void
  (e: 'error', error: Error): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  height: 400
})

const emit = defineEmits<Emits>()

// 响应式计算属性
const componentStyle = computed(() => ({
  height: `${props.height}px`,
  opacity: props.loading ? 0.6 : 1
}))

// 防抖的resize处理
const handleResize = debouncedResize(() => {
  // 处理窗口大小变化
})

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.my-component {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
}

.component-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

.component-content {
  padding: 16px;
}

.component-footer {
  padding: 16px;
  background-color: #f5f7fa;
  border-top: 1px solid #e4e7ed;
}
</style>
```

### 图表组件开发

```typescript
// 图表组件Hooks
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import { debouncedChartUpdate, debouncedResize } from '@/utils/debounce'

export const useChart = (containerRef: Ref<HTMLElement | null>) => {
  const chartInstance = ref<echarts.ECharts | null>(null)
  const loading = ref(false)
  
  const initChart = () => {
    if (!containerRef.value) return
    
    if (chartInstance.value) {
      chartInstance.value.dispose()
    }
    
    chartInstance.value = echarts.init(containerRef.value)
  }
  
  const updateChart = debouncedChartUpdate((options: echarts.EChartsOption) => {
    if (!chartInstance.value) return
    
    loading.value = true
    chartInstance.value.setOption(options, true)
    loading.value = false
  })
  
  const handleResize = debouncedResize(() => {
    if (chartInstance.value) {
      chartInstance.value.resize()
    }
  })
  
  onMounted(() => {
    nextTick(initChart)
    window.addEventListener('resize', handleResize)
  })
  
  onUnmounted(() => {
    if (chartInstance.value) {
      chartInstance.value.dispose()
    }
    window.removeEventListener('resize', handleResize)
  })
  
  return {
    chartInstance,
    loading,
    updateChart,
    initChart
  }
}
```

## 🧪 测试指南

### 单元测试

```typescript
// 防抖函数测试示例
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { debouncedApiCall, DEBOUNCE_DELAYS } from '@/utils/debounce'

describe('防抖工具函数', () => {
  beforeEach(() => {
    vi.useFakeTimers()
  })
  
  afterEach(() => {
    vi.useRealTimers()
  })
  
  it('应该正确防抖API调用', () => {
    const mockFn = vi.fn()
    const debouncedFn = debouncedApiCall(mockFn)
    
    // 快速调用多次
    debouncedFn()
    debouncedFn()
    debouncedFn()
    
    // 在延迟时间内，函数不应该被调用
    expect(mockFn).not.toHaveBeenCalled()
    
    // 快进时间
    vi.advanceTimersByTime(DEBOUNCE_DELAYS.API_CALL)
    
    // 现在函数应该被调用一次
    expect(mockFn).toHaveBeenCalledTimes(1)
  })
})
```

### 组件测试

```typescript
// 组件测试示例
import { mount } from '@vue/test-utils'
import { describe, it, expect } from 'vitest'
import DeviceCard from '@/components/DeviceCard.vue'

describe('DeviceCard组件', () => {
  it('应该正确渲染设备信息', () => {
    const device = {
      id: 1,
      name: '测试设备',
      status: 'online'
    }
    
    const wrapper = mount(DeviceCard, {
      props: { device }
    })
    
    expect(wrapper.find('.device-name').text()).toBe('测试设备')
    expect(wrapper.find('.status-online').exists()).toBe(true)
  })
  
  it('应该正确处理点击事件', async () => {
    const wrapper = mount(DeviceCard, {
      props: { device: mockDevice }
    })
    
    await wrapper.find('button').trigger('click')
    expect(wrapper.emitted('click')).toBeTruthy()
  })
})
```

### 测试运行

```bash
# 运行所有测试
npm run test

# 运行特定测试文件
npm run test -- debounce.test.ts

# 运行测试并生成覆盖率报告
npm run test:coverage

# 监听模式运行测试
npm run test:watch
```

## 🚀 部署指南

### 开发环境部署

```bash
# 1. 启动数据库服务
sudo systemctl start postgresql
sudo systemctl start redis

# 2. 启动后端服务
cd server
npm run dev

# 3. 启动前端服务
cd client
npm run dev
```

### 生产环境部署

```bash
# 1. 构建前端
cd client
npm run build

# 2. 构建后端
cd server
npm run build

# 3. 使用PM2启动服务
pm2 start ecosystem.config.js

# 4. 配置Nginx
sudo cp nginx.conf /etc/nginx/sites-available/web-panel
sudo ln -s /etc/nginx/sites-available/web-panel /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### Docker部署

```dockerfile
# Dockerfile示例
FROM node:16-alpine

WORKDIR /app

# 复制package文件
COPY package*.json ./
RUN npm ci --only=production

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

EXPOSE 3001

CMD ["npm", "start"]
```

```yaml
# docker-compose.yml
version: '3.8'
services:
  web-panel:
    build: .
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=******************************/webpanel
    depends_on:
      - db
      - redis
  
  db:
    image: postgres:13
    environment:
      POSTGRES_DB: webpanel
      POSTGRES_USER: user
      POSTGRES_PASSWORD: pass
    volumes:
      - postgres_data:/var/lib/postgresql/data
  
  redis:
    image: redis:6-alpine
    
volumes:
  postgres_data:
```

---

**文档维护**: 开发团队
**最后更新**: 2025-06-22
**版本**: v1.0.0

# 数据清洗算法使用说明

## 概述

`example_clean.js` 提供了完善的数据滤波和清洗功能，专门针对钻机数据中的关键字段进行异常值检测和滤波处理。

## 主要功能

### 1. 基础清洗函数 `clean(data)`

最简单的使用方式，使用默认配置对数据进行清洗：

```javascript
// 基础使用
const cleanedData = clean(originalData);
```

**默认处理流程：**
- 异常值检测和处理（IQR方法）
- 5点移动平均滤波
- 3点中值滤波

**处理字段：** `advncSpd`, `rtnSpd`, `rtnTq`, `wtrPrsH`, `frcstKn`

### 2. 高级清洗函数 `advancedClean(data, options)`

提供更多配置选项的清洗函数：

```javascript
// 使用预设配置
const cleanedData = advancedClean(originalData, FILTER_PRESETS.standard);

// 自定义配置
const customConfig = {
  filterFields: ['advncSpd', 'rtnSpd', 'rtnTq'],
  outlierDetection: {
    enabled: true,
    method: 'zscore',  // 'iqr' 或 'zscore'
    threshold: 2.0
  },
  movingAverage: {
    enabled: true,
    windowSize: 7
  },
  medianFilter: {
    enabled: true,
    windowSize: 5
  },
  kalmanFilter: {
    enabled: true,
    processNoise: 0.1,
    measurementNoise: 0.1
  }
};

const cleanedData = advancedClean(originalData, customConfig);
```

## 预设配置

### 1. 轻度清洗 (`FILTER_PRESETS.light`)
- 适用于数据质量较好的场景
- 只进行异常值检测和简单中值滤波
- 处理速度快，对原始数据改动最小

### 2. 标准清洗 (`FILTER_PRESETS.standard`)
- 默认推荐配置
- 平衡处理效果和性能
- 包含异常值检测、移动平均和中值滤波

### 3. 强力清洗 (`FILTER_PRESETS.aggressive`)
- 适用于噪声较大的数据
- 使用所有可用的滤波方法
- 包含卡尔曼滤波，处理效果最好

## 滤波算法详解

### 1. 异常值检测

**IQR方法（四分位数）：**
- 计算Q1（25%分位数）和Q3（75%分位数）
- 异常值边界：[Q1 - 1.5×IQR, Q3 + 1.5×IQR]
- 适用于大多数情况，对数据分布要求较低

**Z-Score方法：**
- 基于均值和标准差
- 异常值判断：|Z-score| > threshold
- 适用于正态分布数据

### 2. 移动平均滤波
- 使用滑动窗口计算平均值
- 有效平滑数据，减少随机噪声
- 窗口大小可配置（推荐3-7）

### 3. 中值滤波
- 使用窗口内数值的中位数
- 对脉冲噪声效果显著
- 保持数据边缘特征

### 4. 卡尔曼滤波
- 基于状态估计的最优滤波
- 适用于连续时间序列数据
- 可配置过程噪声和测量噪声

## 数据质量评估

使用 `assessDataQuality()` 函数评估清洗效果：

```javascript
const qualityReport = assessDataQuality(originalData, cleanedData);
console.log(qualityReport);

// 输出示例：
{
  totalRecords: 1000,
  processedRecords: 1000,
  fieldReports: {
    advncSpd: {
      originalMean: 45.67,
      cleanedMean: 45.23,
      originalStd: 12.34,
      cleanedStd: 8.91,
      noiseReduction: 27.8,  // 噪声降低百分比
      dataIntegrity: 100     // 数据完整性百分比
    }
    // ... 其他字段
  }
}
```

## 使用建议

1. **数据质量较好时**：使用 `clean()` 或 `light` 预设
2. **一般情况**：使用 `standard` 预设
3. **数据噪声较大时**：使用 `aggressive` 预设
4. **特殊需求**：自定义配置参数

## 注意事项

1. 清洗算法会创建数据副本，不会修改原始数据
2. 异常值会被邻近值的平均值替换，而不是删除
3. 滤波窗口大小影响处理效果：窗口越大，平滑效果越强
4. 卡尔曼滤波适用于时间序列数据，需要数据按时间排序
5. 处理大量数据时建议使用较小的窗口大小以提高性能

## 性能优化

- 对于大数据集，建议分批处理
- 可以根据实际需求禁用某些滤波步骤
- 监控控制台输出了解处理进度和效果

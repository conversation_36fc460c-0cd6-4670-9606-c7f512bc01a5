# 控件算法使用说明

## 概述

`example_process.js` 提供了完整的钻机数据分析和处理功能，包含多种图表数据生成、实时计算、地质分析等算法。这些算法专门用于钻机工程数据的智能分析和可视化。

## 主要功能模块

### 1. 数据精度处理 `dataPrecisionProcessing(data)`

对原始钻机数据进行精度校正和单位转换：

```javascript
const processedData = dataPrecisionProcessing(rawData);
```

**处理规则：**
- 旋转速度 `rtnSpd` ÷ 10
- 钻进速度 `advncSpd` ÷ 100  
- 液压压力 `hydrPrs` ÷ 10
- 旋转压力 `rtnPrs` ÷ 10
- 推进压力 `frcstPrs` ÷ 10
- 孔倾角 `hIAng` ÷ 10
- 工作时长低位 `lwWrk` ÷ 10

### 2. 实时计算 `realTimeCalculation(data)`

基于最新数据进行实时工况分析：

```javascript
const realtimeResult = realTimeCalculation(dataArray);

// 返回结果示例：
{
  currentDepth: 1250.5,           // 当前深度
  drillingEfficiency: 12.34,      // 钻进效率
  avgPropulsion: 145.67,          // 平均推进力
  avgTorque: 89.23,              // 平均扭矩
  torqueDelta: 15.6,             // 扭矩波动
  isHollow: false,               // 是否空洞
  rockClass: "Ⅲ类围岩",          // 围岩等级
  rockStrength: "坚固",           // 岩石强度
  timestamp: "2025-01-01T10:30:00.000Z"
}
```

## 图表数据生成算法

### 1. 岩石性质饼图 `generateRockyNaturePieChartData(data)`

根据钻进速度统计岩石强度分布：

```javascript
const pieData = generateRockyNaturePieChartData(data);

// 返回格式：
[
  { value: 35.5, name: "坚固" },
  { value: 28.3, name: "比较坚固" },
  { value: 20.1, name: "中等坚固" },
  // ...
]
```

**岩石强度分级标准：**
- 极坚固：钻进速度 ≤ 25 cm/min
- 很坚固：25 < 钻进速度 ≤ 50 cm/min
- 坚固：50 < 钻进速度 ≤ 70 cm/min
- 比较坚固：70 < 钻进速度 ≤ 90 cm/min
- 中等坚固：90 < 钻进速度 ≤ 100 cm/min
- 较软：100 < 钻进速度 ≤ 130 cm/min
- 空洞：钻进速度 > 130 cm/min

### 2. 围岩统计柱状图 `generatePerimeterRockStatisticsBarChartData(data)`

基于钻进速度和扭矩波动分析围岩等级分布：

```javascript
const barData = generatePerimeterRockStatisticsBarChartData(data);

// 返回格式：
[
  { name: "Ⅰ类围岩", value: 15.2, count: 152 },
  { name: "Ⅱ类围岩", value: 23.8, count: 238 },
  { name: "Ⅲ类围岩", value: 31.5, count: 315 },
  // ...
]
```

### 3. 岩层分布折线图 `generateStrataDistributionLineChartData(data)`

展示钻进深度与钻进速度的关系：

```javascript
const lineData = generateStrataDistributionLineChartData(data);

// 返回格式：
{
  data: [
    { depth: 100, value: 45.6 },
    { depth: 105, value: 52.3 },
    // ...
  ],
  // 岩石强度分界线
  极坚固: 25,
  很坚固: 50,
  坚固: 70,
  比较坚固: 90,
  中等坚固: 100,
  较软: 130,
  空洞: 150
}
```

### 4. 工况矩阵图 `generateConditionMatrixChartData(data)`

综合分析工作模式、围岩等级、预警状态等：

```javascript
const matrixData = generateConditionMatrixChartData(data);

// 返回格式：
[
  {
    workMode: 1,                    // 工作模式
    drillingDepth: 1250,           // 钻进深度
    rockClass: "Ⅲ类围岩",          // 围岩等级
    rockStrength: "坚固",           // 岩石强度
    warningLevel: "低风险预警",      // 预警级别
    rotationTorque: 145.6,         // 旋转扭矩
    propulsion: 189.3,             // 推进力
    rotationalSpeed: 98.5,         // 旋转速度
    drillingRate: 67.8,            // 钻进速度
    timestamp: "2025-01-01T10:30:00.000Z"
  }
  // ...
]
```

### 5. 钻进数据表 `generateDrillingData(data)`

生成包含完整分析结果的钻进数据：

```javascript
const drillingData = generateDrillingData(data);

// 每条记录包含：
{
  // 原始数据字段 + 计算字段
  rockStrengthLevel: 60,          // 岩石强度等级 (0-100)
  rockStrengthDesc: "坚固",       // 岩石强度描述
  rockGradeLevel: 60,            // 围岩等级评分 (0-100)
  rockClass: "Ⅲ类围岩",          // 围岩等级
  geologicalWarningLevel: 1,      // 地质预警等级 (0-3)
  equipmentWarningLevel: 0,       // 设备预警等级 (0-3)
  // ... 其他原始字段
}
```

### 6. 地质分析报告 `generateGeologicAnalysisReport(data)`

生成地质状况分析报告：

```javascript
const report = generateGeologicAnalysisReport(data);

// 返回格式：
[
  {
    name: "地质构造",
    description: "III、VI类围岩占比87%，施工过程中遇到16～25米断层或褶皱...",
    suggestion: "建议：使用大扭矩钻进，钻进过程中加强扭矩监控..."
  },
  {
    name: "地下水状况", 
    description: "30米处，出现大量涌水现象。",
    suggestion: "建议：使用超前钻探结合注浆堵水技术..."
  }
  // ...
]
```

## 核心算法函数

### 1. 围岩等级判断 `determineRockClass(speed, torqueDelta, standardTorque)`

基于钻进速度和扭矩波动判断围岩等级：

```javascript
const rockClass = determineRockClass(45.6, 12.3, 156.7);
// 返回: "Ⅲ类围岩"
```

### 2. 岩石强度判断 `determineRockStrength(drillingRate)`

根据钻进速度判断岩石强度：

```javascript
const strength = determineRockStrength(67.8);
// 返回: "坚固"
```

### 3. 岩石强度信息获取 `getRockStrengthInfo(advncSpd)`

获取详细的岩石强度信息：

```javascript
const info = getRockStrengthInfo(67.8);
// 返回: { level: 60, desc: "坚固" }
```

### 4. 数值格式化 `formatNumber(value, decimal)`

统一的数值格式化处理：

```javascript
const formatted = formatNumber(123.456789, 2);
// 返回: 123.46
```

## 预警等级说明

### 地质预警等级 (0-3)
- **0**: 无预警
- **1**: 低风险 - 单项参数轻微异常
- **2**: 中风险 - 多项参数异常或单项严重异常  
- **3**: 高风险 - 多项参数严重异常

### 设备预警等级 (0-3)
- **0**: 正常 - 所有设备参数正常
- **1**: 轻微 - 设备参数轻微偏离正常范围
- **2**: 中度 - 设备参数明显异常
- **3**: 严重 - 设备参数严重超标，需立即处理

## 数据输入格式

所有算法函数都接受标准的钻机数据格式：

```javascript
const inputData = [{
  "collectionAt": "2025-04-30T09:31:39.000Z",  // 采集时间
  "deviceSn": "PDF-2502-20001",               // 设备序列号
  "dpth": 44360,                              // 钻孔深度(CM)
  "rtnTq": 44360,                             // 旋转扭矩
  "frcstKn": 44360,                           // 推进力
  "wtrPrsH": 72,                              // 高压水压力
  "rtnSpd": 2490,                             // 旋转速度
  "advncSpd": 29,                             // 钻进速度
  "frcstPrs": 710,                            // 推进压力
  // ... 其他字段
}];
```

## 算法执行流程

### 1. 服务端执行流程

控件算法在服务端通过算法执行器运行：

```javascript
// 1. 数据精度处理（如果算法包含此方法）
if (methods.includes('dataPrecisionProcessing')) {
  const processedData = await algorithmExecutor.executeMethod('dataPrecisionProcessing', dataArray);
  dataArray = processedData;
}

// 2. 执行图表数据生成方法
const requiredMethods = [
  'generateRockyNaturePieChartData',
  'generatePerimeterRockStatisticsBarChartData',
  'generateGeologicAnalysisReport',
  'generateStrataDistributionLineChartData',
  'generateConditionMatrixChartData',
  'generateDrillingData'
];

// 3. 逐个执行算法方法
for (const methodName of requiredMethods) {
  const result = await algorithmExecutor.executeMethod(methodName, dataArray);
  // 处理结果...
}
```

### 2. 算法方法优先级

1. **dataPrecisionProcessing** - 最高优先级，数据预处理
2. **generateDrillingData** - 核心数据，必须执行
3. **其他图表方法** - 根据前端组件需求执行

## 使用场景和建议

### 1. 实时监控场景

```javascript
// 获取最新数据进行实时分析
const latestData = await getLatestDeviceData(deviceId);
const realtimeAnalysis = realTimeCalculation([latestData]);

// 监控关键指标
if (realtimeAnalysis.isHollow) {
  console.warn('检测到空洞，当前深度:', realtimeAnalysis.currentDepth);
}

if (realtimeAnalysis.rockClass.includes('Ⅴ') || realtimeAnalysis.rockClass.includes('Ⅵ')) {
  console.warn('围岩等级较差，建议调整钻进参数');
}
```

### 2. 历史数据分析场景

```javascript
// 获取历史数据进行综合分析
const historicalData = await getDeviceDataByDepthRange(deviceId, startDepth, endDepth);

// 生成各类分析图表
const pieChart = generateRockyNaturePieChartData(historicalData);
const barChart = generatePerimeterRockStatisticsBarChartData(historicalData);
const lineChart = generateStrataDistributionLineChartData(historicalData);
const matrixChart = generateConditionMatrixChartData(historicalData);
const report = generateGeologicAnalysisReport(historicalData);
```

### 3. 数据质量检查

```javascript
// 检查数据完整性
function validateInputData(data) {
  if (!Array.isArray(data) || data.length === 0) {
    throw new Error('输入数据格式错误或为空');
  }

  const requiredFields = ['advncSpd', 'rtnSpd', 'rtnTq', 'wtrPrsH', 'frcstKn', 'dpth'];

  for (const item of data) {
    for (const field of requiredFields) {
      if (item[field] === undefined || item[field] === null) {
        console.warn(`数据项缺少必需字段: ${field}`);
      }
    }
  }
}

// 使用前验证数据
validateInputData(inputData);
const result = generateDrillingData(inputData);
```

## 性能优化建议

### 1. 数据量控制
- 单次处理建议不超过 5000 条记录
- 大数据集建议分批处理
- 实时计算只使用最近 10-50 条数据

### 2. 方法选择性执行
```javascript
// 根据前端需求选择性执行算法
const enabledCharts = ['pieChart', 'barChart']; // 前端启用的图表

const methodMap = {
  'pieChart': 'generateRockyNaturePieChartData',
  'barChart': 'generatePerimeterRockStatisticsBarChartData',
  'lineChart': 'generateStrataDistributionLineChartData',
  'matrixChart': 'generateConditionMatrixChartData'
};

const requiredMethods = enabledCharts.map(chart => methodMap[chart]);
```

### 3. 缓存策略
- 对于相同深度范围的数据，可以缓存计算结果
- 实时数据建议设置短期缓存（1-5分钟）
- 历史数据分析结果可以长期缓存

## 错误处理和调试

### 1. 常见错误类型

**数据格式错误：**
```javascript
// 错误：传入非数组数据
generateRockyNaturePieChartData(null); // 返回 null

// 正确：传入数组数据
generateRockyNaturePieChartData([{...}]); // 返回结果
```

**字段缺失错误：**
```javascript
// 错误：缺少必需字段
const incompleteData = [{ dpth: 100 }]; // 缺少 advncSpd 等字段

// 正确：包含完整字段
const completeData = [{
  dpth: 100,
  advncSpd: 45,
  rtnTq: 150,
  // ... 其他字段
}];
```

### 2. 调试技巧

```javascript
// 启用详细日志
console.log('输入数据量:', data.length);
console.log('数据样本:', data.slice(0, 3));

// 检查计算结果
const result = generateRockyNaturePieChartData(data);
console.log('饼图数据:', result);
console.log('数据总和:', result?.reduce((sum, item) => sum + item.value, 0));
```

### 3. 异常处理模式

所有算法函数都遵循统一的异常处理模式：

```javascript
function algorithmFunction(data) {
  try {
    // 1. 输入验证
    if (!Array.isArray(data) || data.length === 0) {
      console.warn('输入数据无效');
      return null; // 或返回默认值
    }

    // 2. 数据处理
    const result = processData(data);

    // 3. 结果验证
    if (!result || result.length === 0) {
      console.warn('处理结果为空');
      return null;
    }

    return result;
  } catch (error) {
    console.error('算法执行失败:', error);
    return null; // 确保不会中断整个流程
  }
}
```

## 扩展和自定义

### 1. 添加新的分析算法

```javascript
/**
 * 自定义分析算法示例
 * @param {Array} data - 输入数据
 * @returns {Object} 分析结果
 */
function customAnalysisAlgorithm(data) {
  try {
    // 实现自定义分析逻辑
    const result = data.map(item => {
      // 自定义计算
      return {
        // 自定义字段
      };
    });

    return result;
  } catch (error) {
    console.error('自定义算法执行失败:', error);
    return null;
  }
}
```

### 2. 修改现有算法参数

```javascript
// 自定义岩石强度分级标准
const customRockStrengthThresholds = {
  极坚固: 20,    // 原值: 25
  很坚固: 45,    // 原值: 50
  坚固: 65,      // 原值: 70
  // ...
};

function customGetRockStrengthInfo(advncSpd) {
  if (advncSpd <= customRockStrengthThresholds.极坚固) return { level: 90, desc: '极坚固' };
  if (advncSpd <= customRockStrengthThresholds.很坚固) return { level: 75, desc: '很坚固' };
  // ...
}
```

## 注意事项

1. **数据单位一致性**：确保输入数据单位与算法期望一致
2. **时间序列顺序**：某些算法（如实时计算）依赖数据的时间顺序
3. **空值处理**：算法会自动处理 null/undefined 值，但建议预先清洗数据
4. **精度处理**：数值结果默认保留2位小数，可根据需要调整
5. **内存管理**：处理大数据集时注意内存使用，建议分批处理

## 版本兼容性

- 支持 ES6+ 语法
- 兼容 Node.js 14+ 环境
- 前端兼容现代浏览器（Chrome 80+, Firefox 75+, Safari 13+）

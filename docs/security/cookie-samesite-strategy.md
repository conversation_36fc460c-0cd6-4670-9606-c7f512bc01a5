# Cookie SameSite策略配置说明

## 概述

本文档说明了为什么在不同环境中使用不同的SameSite策略，以及如何正确配置。

## SameSite策略类型

### 1. strict (严格模式)
```typescript
sameSite: 'strict'
```
- **行为**: 完全阻止跨站请求发送Cookie
- **安全级别**: 最高
- **适用场景**: 生产环境，同域部署
- **CSRF防护**: 最强

### 2. lax (宽松模式)
```typescript
sameSite: 'lax'
```
- **行为**: 允许顶级导航跨站请求，阻止其他跨站请求
- **安全级别**: 中等
- **适用场景**: 开发环境，跨域开发
- **CSRF防护**: 良好

### 3. none (无限制)
```typescript
sameSite: 'none'
```
- **行为**: 允许所有跨站请求发送Cookie
- **安全级别**: 最低
- **要求**: 必须配合`secure: true`使用
- **适用场景**: 特殊的跨域需求

## 环境配置策略

### 开发环境 (lax)

**配置**:
```bash
NODE_ENV=development
COOKIE_SAME_SITE=lax
```

**原因**:
1. **跨端口开发**: 前端(localhost:5173)和后端(localhost:3001)不同端口
2. **开发工具**: API测试工具、浏览器开发工具需要跨站访问
3. **热重载**: 开发服务器可能涉及跨站请求
4. **调试便利**: 避免过于严格的策略影响开发效率

**场景示例**:
```
前端: http://localhost:5173
后端: http://localhost:3001/api
关系: 跨端口 = 跨站
```

### 生产环境 (strict)

**配置**:
```bash
NODE_ENV=production
COOKIE_SAME_SITE=strict
```

**原因**:
1. **最高安全性**: 生产环境需要最强的CSRF防护
2. **同域部署**: 前后端通常部署在同一域名
3. **用户保护**: 保护真实用户数据安全
4. **合规要求**: 满足安全标准和法规要求

**场景示例**:
```
前端: http://**********:8091
后端: http://**********:8091/api
关系: 同域 = 同站
```

## 配置实现

### 1. 环境变量配置

**开发环境** (`.env`):
```bash
COOKIE_SECURE=false
COOKIE_SAME_SITE=lax
```

**生产环境** (`.env.production`):
```bash
COOKIE_SECURE=false  # 当前HTTP环境
COOKIE_SAME_SITE=strict
```

### 2. 代码配置

```typescript
// config/index.ts
security: {
  cookieSecure: process.env.COOKIE_SECURE === 'true',
  sameSite: (process.env.COOKIE_SAME_SITE as 'strict' | 'lax' | 'none') || 
            (process.env.NODE_ENV === 'production' ? 'strict' : 'lax')
}

// controllers/user.ts
res.cookie('token', token, {
  httpOnly: true,
  secure: config.security.cookieSecure,
  sameSite: config.security.sameSite,
  maxAge: 24 * 60 * 60 * 1000,
  path: '/'
})
```

## 安全影响分析

### CSRF攻击防护

#### strict模式
```
恶意网站 -> 用户浏览器 -> 目标网站
❌ Cookie不会被发送，攻击失败
```

#### lax模式
```
恶意网站 -> 用户浏览器 -> 目标网站 (GET请求)
⚠️ 顶级导航可能发送Cookie，需要额外防护

恶意网站 -> 用户浏览器 -> 目标网站 (POST请求)
❌ Cookie不会被发送，攻击失败
```

### 用户体验影响

#### strict模式
- ✅ 最高安全性
- ⚠️ 从外部链接访问可能需要重新登录
- ⚠️ 某些合法的跨站功能可能受影响

#### lax模式
- ✅ 平衡安全性和用户体验
- ✅ 正常的导航链接工作正常
- ⚠️ 相对strict稍微宽松

## 最佳实践

### 1. 环境区分
- 开发环境: 使用lax，便于开发调试
- 生产环境: 使用strict，确保最高安全性

### 2. 配置灵活性
- 通过环境变量控制，便于不同环境部署
- 提供合理的默认值

### 3. 安全监控
- 监控CSRF攻击尝试
- 记录异常的跨站请求

### 4. 文档说明
- 清楚说明配置原因
- 提供环境配置示例

## 故障排除

### 问题: Cookie在开发环境不工作
**原因**: 可能使用了strict模式
**解决**: 改为lax模式
```bash
COOKIE_SAME_SITE=lax
```

### 问题: 生产环境安全扫描报告CSRF风险
**原因**: 可能使用了lax或none模式
**解决**: 改为strict模式
```bash
COOKIE_SAME_SITE=strict
```

### 问题: TypeScript类型错误
**原因**: sameSite需要特定的字面量类型
**解决**: 使用类型断言
```typescript
sameSite: (value as 'strict' | 'lax' | 'none')
```

## 总结

通过环境区分的SameSite策略配置，我们实现了：
1. **开发便利性**: 开发环境使用lax，避免跨端口问题
2. **生产安全性**: 生产环境使用strict，提供最强CSRF防护
3. **配置灵活性**: 通过环境变量控制，便于部署管理
4. **类型安全性**: 正确的TypeScript类型定义

这种策略既保证了开发效率，又确保了生产环境的安全性。

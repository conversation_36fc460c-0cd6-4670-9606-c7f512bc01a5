# JWT Token存储安全升级：从localStorage迁移到HttpOnly Cookie

## 概述

本文档记录了将JWT Token存储方式从不安全的localStorage迁移到安全的HttpOnly Cookie的完整过程。

## 安全问题

### 原有问题
- **XSS攻击风险**: JWT token存储在localStorage中，容易被恶意脚本获取
- **跨站脚本攻击**: 攻击者可通过注入JavaScript代码窃取用户token
- **持久化风险**: localStorage数据持久存储，增加了泄露风险

### 解决方案
- **HttpOnly Cookie**: 浏览器自动管理，JavaScript无法访问
- **Secure标志**: 生产环境仅通过HTTPS传输
- **SameSite保护**: 防止CSRF攻击

## 修改内容

### 1. 后端修改

#### 1.1 安装依赖
```bash
cd server && npm install cookie-parser @types/cookie-parser
```

#### 1.2 配置Cookie解析中间件
**文件**: `server/src/app.ts`
- 导入cookie-parser
- 添加cookieParser()中间件

#### 1.3 修改登录接口
**文件**: `server/src/controllers/user.ts`
- 登录成功后设置HttpOnly Cookie
- 响应中不再返回token
- 配置Cookie安全选项：
  - `httpOnly: true` - 防止JavaScript访问
  - `secure: config.security.cookieSecure` - 通过环境变量控制（当前为false，因为生产环境使用HTTP）
  - `sameSite: 'strict'/'lax'` - 生产环境strict，开发环境lax
  - `maxAge: 24小时` - 设置过期时间

#### 1.4 修改认证中间件
**文件**: `server/src/middleware/auth.ts`
- 优先从Cookie读取token
- 保持Authorization头向后兼容
- 更新错误提示信息

#### 1.5 修改登出接口
**文件**: `server/src/controllers/user.ts`
- 清除HttpOnly Cookie
- 同时支持Cookie和Header方式

### 2. 前端修改

#### 2.1 修改用户状态管理
**文件**: `client/src/store/user.ts`
- 移除token的localStorage存储
- 只保留用户信息的本地存储
- 更新登录、登出、钉钉登录逻辑

#### 2.2 修改HTTP请求配置
**文件**: `client/src/utils/request.ts`
- 移除Authorization头的手动设置
- 添加`withCredentials: true`支持Cookie
- 简化请求拦截器逻辑

#### 2.3 修改路由守卫
**文件**: `client/src/router/index.ts`
- 移除token检查逻辑
- 仅检查用户信息是否存在
- 依赖后端Cookie验证

#### 2.4 更新TypeScript类型
**文件**: `server/src/types/middleware.d.ts`
- 添加cookies类型定义
- 更新注释说明

## 安全提升

### 1. XSS防护
- ✅ JWT token无法被JavaScript访问
- ✅ 即使发生XSS攻击，token也不会泄露
- ✅ 减少了客户端token管理的复杂性

### 2. CSRF防护
- ✅ SameSite=strict防止跨站请求
- ✅ 结合CORS配置提供双重保护
- ✅ 生产环境Secure标志确保HTTPS传输

### 3. 会话管理
- ✅ 浏览器自动管理Cookie生命周期
- ✅ 服务器端可控制Cookie过期
- ✅ 登出时彻底清除认证信息

## 兼容性说明

### 向后兼容
- 认证中间件同时支持Cookie和Authorization头
- 现有API客户端可继续使用Bearer token
- 渐进式迁移，不影响现有功能

### 前端适配
- 所有AJAX请求需要`credentials: 'include'`
- 移除手动token管理逻辑
- 简化认证状态检查

## 测试验证

### 测试文件
创建了`test-cookie-auth.html`用于验证：
- 登录功能是否正常
- Cookie是否正确设置
- 受保护路由是否可访问
- 登出是否清除Cookie

### 测试步骤
1. 打开测试页面
2. 使用admin/123456登录
3. 检查登录是否成功
4. 测试受保护路由访问
5. 验证登出功能
6. 确认Cookie被清除

## 部署注意事项

### 生产环境配置
```bash
# 确保设置正确的环境变量
NODE_ENV=production

# Cookie安全配置
# 当前生产环境使用HTTP，所以设置为false
COOKIE_SECURE=false

# 如果将来启用HTTPS，修改为：
# COOKIE_SECURE=true
```

### CORS配置
确保CORS配置允许credentials：
```javascript
credentials: true
```

### 域名配置
生产环境需要配置正确的域名白名单。

## 监控和日志

### 安全事件监控
- 登录失败尝试
- 无效token访问
- Cookie篡改检测

### 性能监控
- 认证中间件响应时间
- Cookie大小影响
- 数据库查询优化

## 总结

通过将JWT token从localStorage迁移到HttpOnly Cookie，显著提升了应用的安全性：

1. **消除XSS风险** - JavaScript无法访问token
2. **简化前端逻辑** - 浏览器自动管理Cookie
3. **增强CSRF防护** - SameSite和Secure标志
4. **保持兼容性** - 支持多种认证方式
5. **改善用户体验** - 无需手动管理token

这是一个重要的安全升级，建议在所有环境中部署此改进。

# AST代码安全分析器

## 概述

AST代码安全分析器是一个基于抽象语法树(AST)的JavaScript代码安全检查工具，用于检测算法代码中的潜在安全风险和代码注入攻击。

## 功能特性

### 🔍 深度安全检查
- **AST静态分析**: 使用Acorn解析器进行深度代码结构分析
- **多层检测**: 结合正则表达式和AST分析，提供全面的安全检查
- **智能绕过检测**: 能够识别复杂的代码注入绕过技术

### 🛡️ 安全检查项目

1. **禁止的标识符检测**
   - `eval`, `setTimeout`, `setInterval`, `setImmediate`
   - `require`, `import`, `process`, `global`
   - 系统模块: `fs`, `path`, `os`, `child_process`等

2. **危险属性访问检测**
   - `constructor`, `__proto__`, `prototype`
   - `__defineGetter__`, `__defineSetter__`等

3. **动态代码执行检测**
   - 字符串拼接构造危险函数名
   - 动态属性访问绕过检查
   - 构造函数链访问

4. **编码绕过检测**
   - `String.fromCharCode`编码绕过
   - `atob/btoa`编码绕过
   - `unescape/decodeURI`解码绕过

## 风险等级

| 等级 | 描述 | 处理方式 |
|------|------|----------|
| **CRITICAL** | 严重风险，存在直接代码执行威胁 | 拒绝保存/执行 |
| **HIGH** | 高风险，可能导致安全问题 | 拒绝保存/执行 |
| **MEDIUM** | 中等风险，需要注意 | 警告但允许保存/执行 |
| **LOW** | 低风险或安全 | 正常保存/执行 |

## 集成到算法保存流程

### 自动安全检测
算法在创建和更新时会自动进行安全检测：

```typescript
// 创建算法时的安全检测
if (content) {
  const securityAnalyzer = new CodeSecurityAnalyzer()
  const securityResult = securityAnalyzer.analyzeCode(content)
  
  if (securityResult.riskLevel === 'CRITICAL' || securityResult.riskLevel === 'HIGH') {
    throw new AppError(`算法代码包含${securityResult.riskLevel}安全风险，无法保存`, 400)
  }
}
```

### 检测时机
1. **创建算法**: 在保存到数据库之前进行安全检测
2. **更新算法**: 在更新算法内容时进行安全检测
3. **手动检测**: 通过API接口进行独立的安全分析

## API接口

### 1. 分析指定算法代码安全性
```http
POST /api/arithmetic/:id/security-check
Authorization: Bearer <token>
```

### 2. 分析提交的代码安全性
```http
POST /api/arithmetic/security-check
Authorization: Bearer <token>
Content-Type: application/json

{
  "code": "function test() { return 'hello'; }"
}
```

### 响应格式

#### 安全检测API成功响应
```json
{
  "success": true,
  "data": {
    "isSecure": false,
    "riskLevel": "HIGH",
    "score": 72,
    "violations": [
      {
        "type": "FORBIDDEN_IDENTIFIER",
        "message": "使用了禁止的标识符: eval",
        "line": 3,
        "column": 8,
        "severity": "CRITICAL",
        "code": "eval"
      }
    ],
    "summary": {
      "totalViolations": 1,
      "criticalCount": 1,
      "highCount": 0,
      "mediumCount": 0,
      "lowCount": 0
    },
    "recommendations": [
      "移除代码中的禁止标识符，如eval、Function、require等"
    ]
  }
}
```

#### 算法保存失败响应（简洁错误信息）
```json
{
  "status": "fail",
  "message": "算法代码包含严重安全风险，无法保存。请移除代码中的危险操作，如eval、Function等危险函数。",
  "code": -1
}
```

## 检测示例

### ✅ 安全代码
```javascript
function processData(data) {
  return data.map(item => item * 2)
}

function calculateAverage(numbers) {
  return numbers.reduce((sum, num) => sum + num, 0) / numbers.length
}

// 正常的算法函数
function clean(data) {
  if (!Array.isArray(data)) {
    return data
  }
  return data.filter(item => {
    return item && item.clctSts === 1
  })
}

// ES6模块语法也是安全的
export function processData(data) {
  return data.map(item => item * 2)
}

import { someUtility } from './utils'
export default function algorithm(data) {
  return someUtility(data)
}
```
**结果**: 风险等级 LOW，安全分数 100/100

> **注意**:
> - 正常的 `function` 关键字和函数声明是安全的，不会被误报为 `Function` 构造函数
> - ES6模块语法（`import`/`export`）是安全的，分析器会自动识别并正确解析

### ❌ 危险代码示例

#### 1. 直接使用eval
```javascript
function dangerousCode() {
  eval('console.log("hello")')
}
```
**检测结果**: CRITICAL风险，拒绝保存

#### 2. 动态访问绕过
```javascript
function sneakyCode() {
  this['ev' + 'al']('console.log("bypassed")')
}
```
**检测结果**: CRITICAL风险，检测到字符串拼接绕过

#### 3. 构造函数链攻击
```javascript
function constructorAccess() {
  const func = this.constructor.constructor
  return func('return this')()
}
```
**检测结果**: HIGH风险，检测到构造函数访问

#### 4. Function构造函数
```javascript
function functionConstructor() {
  return new Function('return this')()
}
```
**检测结果**: CRITICAL风险，检测到Function构造函数

## 故障排除

### 常见问题

#### 1. "代码包含禁止的API: Function" 错误
**问题**: 正常的 `function` 关键字被误报为 `Function` 构造函数
**解决方案**: 
- 已修复：AST分析器现在能正确区分 `function` 关键字和 `Function` 构造函数
- `function` 关键字用于函数声明是安全的
- 只有 `new Function()` 或 `Function()` 构造函数调用才会被检测为危险

#### 2. 正常算法函数被误报
**问题**: 包含大量 `function` 声明的算法文件被误判为不安全
**解决方案**: 
- 更新到最新版本的安全分析器
- 正常的函数声明如 `function clean(data) {}` 不会触发安全警告

#### 3. 算法保存失败
**问题**: 算法在保存时被安全检测拦截
**解决方案**:
- 检查错误信息中的具体违规项
- 移除或修改代码中的危险操作
- 使用安全分析API预先检测代码

#### 4. ES6模块语法解析错误
**问题**: 包含import/export的算法代码解析失败
**解决方案**:
- 已修复：分析器现在支持ES6模块语法
- 自动识别脚本和模块类型进行解析
- import/export语句是安全的，不会被误报

#### 5. 前端接收不到详细错误信息
**问题**: 控制台显示详细安全信息，但前端只收到简单错误消息
**解决方案**:
- 已修复：Express错误处理中间件函数签名问题
- 详细安全信息现在正确传递到API响应中
- 前端可通过 `error.response.data.details` 获取完整信息

### 版本更新日志

#### v1.6.0 (当前版本)
- ✅ 清洁：移除所有stack信息，避免技术细节泄露
- ✅ 安全：不向前端暴露服务器内部信息
- ✅ 简洁：提供最清洁的错误响应格式
- ✅ 用户友好：专注于用户可理解的错误信息

#### v1.5.0
- ✅ 简化：移除复杂的详细错误信息传递
- ✅ 优化：提供简洁明了的错误消息
- ✅ 改进：更好的用户体验和可读性
- ✅ 精简：减少前端处理复杂度

#### v1.4.0
- ✅ 修复：Express错误处理中间件函数签名
- ✅ 改进：详细安全信息正确传递到前端
- ✅ 优化：完整的错误处理流程测试
- ✅ 验证：API响应包含完整的安全检测详情

#### v1.3.0
- ✅ 修复：支持ES6模块语法（import/export）解析
- ✅ 改进：智能AST解析，自动识别脚本和模块类型
- ✅ 优化：更好的错误处理和用户体验
- ✅ 测试：100%通过率，包括ES6模块语法测试

#### v1.2.0
- ✅ 新增：算法保存时自动安全检测
- ✅ 改进：在创建和更新算法时进行安全验证
- ✅ 优化：提供详细的安全检测日志
- ✅ 文档：移动到正确的docs/security目录

#### v1.1.0
- ✅ 修复：正确区分 `function` 关键字和 `Function` 构造函数
- ✅ 改进：优化正则表达式检查，避免误报
- ✅ 新增：支持真实算法文件的安全性测试
- ✅ 测试：100%通过率，包括真实算法文件测试

#### v1.0.0 (初始版本)
- ✅ 基础AST安全分析功能
- ✅ 多层安全检测机制
- ✅ 风险等级评估

## 前端处理建议

### 简洁的错误信息展示
当算法保存失败时，前端只需要显示简洁的错误消息：

```javascript
// 算法保存失败时的错误处理
try {
  await saveAlgorithm(algorithmData)
  showSuccess('算法保存成功')
} catch (error) {
  // 显示简洁的错误信息
  const errorMessage = error.response?.data?.message || '保存失败'
  showError(errorMessage)
}
```

### 用户界面建议
1. **简洁明了**: 显示清晰的错误消息，包含基本的修复指导
2. **用户友好**: 避免技术术语，使用通俗易懂的语言
3. **操作指导**: 错误消息中包含具体的修复建议
4. **帮助链接**: 可选择性提供安全文档的链接

## 最佳实践

1. **开发阶段**: 使用安全分析API提前检查代码
2. **保存前**: 系统会自动进行安全检测，无需手动操作
3. **监控**: 定期检查日志中的安全警告
4. **更新**: 根据新的安全威胁更新检测规则
5. **用户体验**: 提供友好的错误信息和修复指导

## 技术实现

- **解析器**: Acorn (轻量级、快速的JavaScript解析器)
- **遍历器**: acorn-walk (AST节点遍历)
- **检测方式**: 静态分析 + 模式匹配
- **集成方式**: 嵌入到算法保存和执行流程

## 为什么使用AST解析器

1. **准确性**: 比正则表达式更准确，能理解代码结构
2. **全面性**: 能检测复杂的绕过技术和间接调用
3. **可扩展性**: 易于添加新的检测规则和模式
4. **性能**: 现代AST解析器性能优秀，适合生产环境
5. **标准化**: 基于JavaScript标准语法，检测结果可靠

这个AST安全分析器显著提升了系统的安全性，能够有效防范代码注入攻击，是算法执行环境的重要安全保障。

# 🛡️ 安全配置指南

## 概述

本文档详细说明了项目的安全配置和最佳实践，确保系统在生产环境中的安全性。

## 🔒 已实施的安全措施

### 1. JWT令牌安全
- ✅ 移除URL参数传递支持，仅允许Authorization Header
- ✅ 使用强随机密钥（64字节）
- ✅ 设置合理的过期时间（15分钟）
- ✅ 实施令牌数据库验证
- ✅ 添加令牌格式验证

### 2. CORS配置
- ✅ 配置严格的域名白名单
- ✅ 启用凭证传递
- ✅ 限制允许的HTTP方法
- ✅ 设置预检请求缓存
- ✅ 优化同源部署支持（生产环境推荐）

### 3. 输入验证
- ✅ 实施express-validator验证
- ✅ XSS防护
- ✅ SQL注入防护
- ✅ 文件上传类型限制
- ✅ 请求体大小限制

### 4. 安全中间件
- ✅ Helmet安全头设置
- ✅ 请求频率限制
- ✅ 登录尝试限制
- ✅ 压缩和性能优化

### 5. 错误处理
- ✅ 统一错误响应格式
- ✅ 生产环境敏感信息隐藏
- ✅ 详细的错误日志记录
- ✅ 数据库错误处理

### 6. 环境变量管理
- ✅ 强制环境变量验证
- ✅ JWT密钥强度检查
- ✅ 敏感文件权限控制
- ✅ .gitignore完善配置

### 7. 代码安全检测
- ✅ AST代码安全分析器
- ✅ 算法保存时自动安全检测
- ✅ 代码注入攻击防护
- ✅ 动态代码执行检测

## 🚀 部署前检查清单

### 必须完成的安全配置

#### 1. 环境变量配置
```bash
# 生成新的JWT密钥
node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"

# 设置环境变量
export JWT_SECRET="your_generated_secret"
export DB_PASSWORD="your_strong_password"
export OSS_ACCESS_KEY_ID="your_new_access_key"
export OSS_ACCESS_KEY_SECRET="your_new_secret_key"
```

#### 2. 数据库安全
- [ ] 更改默认数据库密码
- [ ] 创建专用数据库用户
- [ ] 启用SSL连接（生产环境）
- [ ] 配置防火墙规则

#### 3. 阿里云OSS安全
- [ ] 生成新的AccessKey
- [ ] 设置Bucket访问权限
- [ ] 启用访问日志
- [ ] 配置跨域规则

#### 4. 服务器安全
- [ ] 配置防火墙（仅开放必要端口）
- [ ] 启用HTTPS/SSL证书
- [ ] 设置Nginx安全配置
- [ ] 配置日志轮转

#### 5. 应用安全
- [ ] 更新CORS域名白名单
- [ ] 配置生产环境日志级别
- [ ] 设置监控和告警
- [ ] 定期安全扫描

## 🔧 安全检查工具

### 运行安全检查
```bash
# 检查安全配置
npm run security-check

# 生成新的JWT密钥
npm run generate-jwt-secret

# 检查依赖包漏洞
npm audit

# 修复依赖包漏洞
npm audit fix
```

### 定期安全维护
```bash
# 每周执行
npm update
npm audit

# 每月执行
npm outdated
npm run security-check
```

## 🚨 安全事件响应

### 发现安全漏洞时的处理步骤

1. **立即响应**
   - 评估漏洞影响范围
   - 如果是严重漏洞，立即下线服务
   - 通知相关人员

2. **漏洞修复**
   - 应用安全补丁
   - 更新相关密钥
   - 重新部署服务

3. **事后处理**
   - 分析攻击日志
   - 更新安全策略
   - 加强监控措施

## 📋 安全配置参考

### 生产环境.env配置示例
```env
# 服务器配置
PORT=3001
NODE_ENV=production

# JWT配置（使用强随机密钥）
JWT_SECRET=your_64_byte_random_secret_here

# 数据库配置
DB_NAME=web_panel_prod
DB_USER=web_panel_user
DB_PASSWORD=your_strong_password_here
DB_HOST=localhost
DB_PORT=5432
DB_SSL=true

# OSS配置
OSS_REGION=oss-cn-hangzhou
OSS_ACCESS_KEY_ID=your_secure_access_key
OSS_ACCESS_KEY_SECRET=your_secure_secret_key
OSS_BUCKET=your-secure-bucket

# 安全配置
CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX=1000
LOGIN_RATE_LIMIT_MAX=5
SESSION_TIMEOUT=900000

# 日志配置
LOG_LEVEL=warn
LOG_FILE_PATH=./logs
```

### Nginx安全配置示例
```nginx
server {
    listen 443 ssl http2;
    server_name yourdomain.com;
    
    # SSL配置
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
    
    # 隐藏服务器信息
    server_tokens off;
    
    # 请求大小限制
    client_max_body_size 10M;
    
    location / {
        proxy_pass http://localhost:3001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 📞 联系方式

如果发现安全问题，请立即联系：
- 技术负责人：[联系方式]
- 安全团队：[联系方式]
- 紧急联系：[联系方式]

## 📚 相关文档

- [AST代码安全分析器](./AST-Security-Analyzer.md)
- [JWT Cookie安全迁移](./jwt-cookie-migration.md)
- [Cookie SameSite策略](./cookie-samesite-strategy.md)
- [JWT Cookie安全审查](./jwt-cookie-security-review.md)
- [部署指南](../deploy/README.md)
- [API文档](../api/README.md)

# 🔧 日志管理问题修复指南

## 问题描述

在生产环境中发现 `console.log()` 输出到了 PM2 的 `out-0.log` 文件，而不是 Winston 的 `info.log` 文件，导致日志管理不一致。

## 问题原因

1. **PM2日志捕获**：PM2 会捕获所有 `console.log()` 输出到 `out_file`
2. **Winston配置**：只有通过 `logger.info()` 的日志才会输出到 Winston 日志文件
3. **代码混用**：项目中同时使用了 `console.log()` 和 `logger.info()`

## 解决方案

### 方案1：统一日志重定向（已实施）

在现有的日志中间件 `server/src/middleware/logger.ts` 中增加了console重定向功能，在生产环境中自动将 `console.log()` 重定向到 Winston。

#### 核心特性：
- 🔄 自动重定向：生产环境中 `console.log()` → `winston.info()`
- 📝 统一接口：提供便捷的日志记录方法
- 🎯 专用方法：数据处理、算法执行、性能监控专用日志
- 🔧 零侵入：现有代码无需大量修改

#### 使用方法：

```typescript
// 方式1：直接使用console.log（生产环境自动重定向）
console.log('这条日志在生产环境会写入winston info.log')

// 方式2：使用统一日志接口（推荐）
import { logInfo, logDataProcessing } from '../middleware/logger'

logInfo('一般信息日志')
logDataProcessing('数据清洗完成', 1000, 800, 'device123')
```

### 方案2：PM2配置优化

更新了 `ecosystem.config.js`：

```javascript
module.exports = {
  apps: [{
    name: "device-management",
    script: "./server/dist/index.js",
    // 将PM2日志分离到专门目录
    error_file: "./logs/pm2/error.log",
    out_file: "./logs/pm2/out.log",
    log_file: "./logs/pm2/combined.log",
    // 其他配置...
  }]
}
```

## 部署步骤

### 1. 初始化日志目录

```bash
# 在服务器上运行
cd /var/www/device-management
npm run setup-logs
```

### 2. 更新PM2配置

```bash
# 停止现有服务
pm2 stop device-management

# 使用新配置启动
pm2 start ecosystem.config.js

# 保存配置
pm2 save
```

### 3. 验证日志输出

```bash
# 查看Winston日志
tail -f logs/info.log

# 查看PM2日志
tail -f logs/pm2/out.log

# 测试API并观察日志
curl http://localhost:3001/api/health
```

## 日志目录结构

```
logs/
├── winston日志文件/
│   ├── error-2024-01-15.log     # 错误日志（按日轮转）
│   ├── info-2024-01-15.log      # 信息日志（按日轮转）
│   ├── combined-2024-01-15.log  # 综合日志（按日轮转）
│   ├── error.log                # 当前错误日志符号链接
│   ├── info.log                 # 当前信息日志符号链接
│   └── combined.log             # 当前综合日志符号链接
├── pm2/                         # PM2进程日志
│   ├── error.log
│   ├── out.log
│   └── combined.log
└── archive/                     # 归档目录
```

## 日志管理命令

```bash
# 查看实时日志
npm run logs:winston    # Winston综合日志
npm run logs:error      # Winston错误日志
npm run logs:pm2        # PM2日志

# 日志清理
npm run clean-logs-dry  # 试运行（查看将删除的文件）
npm run clean-logs      # 实际清理过期日志
```

## 验证修复效果

### 1. 检查console.log重定向

在代码中添加测试：
```typescript
console.log('测试console.log重定向')
```

生产环境中应该在 `logs/info.log` 中看到这条日志。

### 2. 检查数据处理日志

原来的：
```typescript
console.log(`数据清洗完成，清洗前: ${data.length} 条，清洗后: ${dataArray.length} 条`);
```

现在的：
```typescript
logDataProcessing('数据清洗完成', data.length, dataArray.length);
```

应该在 `logs/info.log` 中看到格式化的日志。

## 监控和维护

### 1. 日志轮转监控

Winston 自动按日轮转，保留30天：
- 单文件最大20MB
- 自动创建符号链接指向当前日志

### 2. 磁盘空间监控

```bash
# 检查日志目录大小
du -sh logs/

# 检查磁盘使用情况
df -h
```

### 3. 定期清理

建议设置 cron 任务：
```bash
# 每周清理一次过期日志
0 2 * * 0 cd /var/www/device-management && npm run clean-logs
```

## 故障排查

### 问题1：日志仍然输出到out.log

**可能原因**：
- 日志重定向未正确初始化
- NODE_ENV 不是 'production'

**解决方法**：
```bash
# 检查环境变量
echo $NODE_ENV

# 检查应用启动日志
pm2 logs device-management | grep "console重定向"
```

### 问题2：Winston日志文件未创建

**可能原因**：
- 日志目录权限问题
- LOG_FILE_PATH 环境变量错误

**解决方法**：
```bash
# 重新初始化日志目录
npm run setup-logs

# 检查目录权限
ls -la logs/
```

### 问题3：日志重复记录

**可能原因**：
- PM2和Winston同时记录
- 多个实例重复初始化

**解决方法**：
- 确保PM2日志和Winston日志分离
- 检查cluster模式配置

## 最佳实践

1. **统一使用新的日志接口**：逐步替换项目中的 `console.log`
2. **结构化日志**：使用专用方法记录特定类型的日志
3. **日志级别**：合理使用 info/warn/error 级别
4. **性能监控**：使用 `logPerformance` 记录关键操作耗时
5. **定期维护**：设置自动清理和监控

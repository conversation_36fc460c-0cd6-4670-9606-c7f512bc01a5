# 海聚科技钻井数据监控系统 - 缓存系统使用文档

## 概述

本系统实现了基于Node-Cache的内存缓存机制，用于优化数据库查询性能，减少重复查询，提升系统响应速度。

## 系统架构

### 核心组件

1. **CacheManager** - 核心缓存管理器
   - 基于Node-Cache实现
   - 支持TTL过期机制
   - 提供统计和监控功能

2. **缓存中间件** - Express中间件
   - 自动缓存键生成
   - 用户隔离机制
   - 条件缓存支持

3. **缓存失效中间件** - 自动缓存清理
   - 数据更新后自动清除
   - 支持模式匹配清除
   - 批量清除功能

## 缓存策略

### TTL配置

- **设备信息类（5分钟）：** 设备列表、设备详情、设备数字岩芯信息
- **算法配置类（30分钟）：** 算法列表、算法详情、算法方法列表
- **模板配置类（1小时）：** 模板列表、字段映射、图表组件配置

### 缓存键格式

```
category:userId:identifier
```

- `category`: 缓存类别（device, algorithm, template等）
- `userId`: 用户ID（实现用户隔离）
- `identifier`: 请求标识符（基于路径、参数的MD5哈希）

## 环境变量配置

```bash
# 缓存配置
CACHE_DEFAULT_TTL=600          # 默认TTL（秒）
CACHE_DEVICE_TTL=300           # 设备缓存TTL（秒）
CACHE_ALGORITHM_TTL=1800       # 算法缓存TTL（秒）
CACHE_TEMPLATE_TTL=3600        # 模板缓存TTL（秒）
CACHE_MAX_KEYS=10000           # 最大缓存键数量
CACHE_LOCK_TIMEOUT=30000       # 缓存锁超时时间（毫秒）
CACHE_ENABLE_LOCK=true         # 是否启用缓存锁机制
```

## API接口

### 缓存状态查询

```http
GET /api/cache/status
```

响应示例：
```json
{
  "success": true,
  "data": {
    "enabled": true,
    "keyCount": 156,
    "hitCount": 1234,
    "missCount": 567,
    "hitRate": "68.52%",
    "memoryUsage": {
      "rss": "45MB",
      "heapUsed": "32MB",
      "heapTotal": "40MB",
      "external": "8MB"
    },
    "cacheMemory": {
      "keySize": 1024,
      "valueSize": 8192,
      "total": 9216
    }
  },
  "message": "缓存系统运行正常"
}
```

### 手动清除缓存

```http
DELETE /api/cache/clear?category=device&userId=123
DELETE /api/cache/clear?userId=123
DELETE /api/cache/clear?category=device
DELETE /api/cache/clear
```

## 使用示例

### 添加缓存中间件

```typescript
import { deviceCacheMiddleware } from '../middleware/cache'

// 设备列表 - 5分钟缓存
router.get('/', 
  asyncHandler(verifyToken), 
  deviceCacheMiddleware,
  asyncHandler(getDeviceList)
)
```

### 添加缓存失效

```typescript
import { deviceCacheInvalidation } from '../middleware/cacheInvalidation'

// 设备更新 - 更新后清除缓存
router.put('/:id', 
  asyncHandler(verifyToken), 
  asyncHandler(updateDevice),
  deviceCacheInvalidation
)
```

### 手动缓存操作

```typescript
import { invalidateCache } from '../middleware/cacheInvalidation'

// 手动清除指定用户的设备缓存
invalidateCache(userId, ['device'])
```

## 监控和调试

### 日志输出

系统会自动输出缓存相关日志：

```
✅ 内存缓存系统初始化成功
📊 缓存系统状态: 最大键数量 10000, 默认TTL 600秒
缓存命中: device:123:a1b2c3d4
缓存未命中: device:123:e5f6g7h8
缓存已设置: device:123:e5f6g7h8, TTL: 300秒
已清除用户 123 的缓存类别: device
```

### 性能指标

- **缓存命中率：** 通过 `/api/cache/status` 查看
- **内存使用：** 监控系统内存和缓存内存使用情况
- **响应时间：** 缓存命中时响应时间显著降低

## 性能优化

### 缓存雪崩防护

系统内置了缓存锁机制来防止缓存雪崩问题：

- **问题描述：** 大量并发请求同时访问未缓存的数据，导致所有请求都去查询数据库
- **解决方案：** 使用缓存锁，确保同一缓存键只有一个请求去查询数据库，其他请求等待
- **配置项：** `CACHE_ENABLE_LOCK` 和 `CACHE_LOCK_TIMEOUT`

### 性能测试

使用提供的测试脚本验证缓存性能：

```bash
# 运行性能测试
node test-cache-performance.js
```

测试结果应该显示：
- 响应时间稳定，没有明显的峰值
- P99响应时间不超过平均响应时间的5倍
- 成功率达到95%以上

## 最佳实践

1. **合理设置TTL：** 根据数据更新频率设置合适的缓存时间
2. **及时清除缓存：** 数据更新后立即清除相关缓存
3. **监控缓存性能：** 定期检查缓存命中率和内存使用
4. **用户隔离：** 确保不同用户的缓存相互独立
5. **降级处理：** 缓存失败时不影响正常业务流程
6. **并发控制：** 启用缓存锁机制防止缓存雪崩
7. **性能测试：** 定期进行并发测试验证系统性能

## 故障排除

### 常见问题

1. **缓存未生效**
   - 检查缓存系统是否正确初始化
   - 确认中间件顺序正确（在verifyToken之后）
   - 检查用户认证是否成功

2. **缓存未清除**
   - 确认缓存失效中间件已添加
   - 检查操作是否返回成功状态
   - 验证缓存键格式是否正确

3. **内存使用过高**
   - 调整CACHE_MAX_KEYS限制
   - 缩短TTL时间
   - 定期清理不必要的缓存

### 调试命令

```bash
# 查看缓存状态
curl http://localhost:3001/api/cache/status

# 清除所有缓存
curl -X DELETE http://localhost:3001/api/cache/clear

# 清除指定用户缓存
curl -X DELETE "http://localhost:3001/api/cache/clear?userId=123"
```

## 版本历史

- **v1.0.0** - 初始版本，基础缓存功能
- **v1.1.0** - 添加缓存失效机制
- **v1.2.0** - 完善监控和统计功能

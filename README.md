# 海聚科技设备管理系统

## 项目介绍
海聚科技设备管理系统是一个前后端一体化的设备管理平台，采用 Vue 3 + Node.js Express 技术栈构建。

## 技术架构
- 前端: Vue 3 + Element Plus + Axios
- 后端: Node.js + Express
- 数据库: PostgreSQL + Sequelize ORM

## 安装教程

1. 克隆项目
```bash
git clone <repository-url>
cd web-panel
```

2. 安装依赖
```bash
# 安装根目录、客户端和服务端所有依赖
npm run install:all

# 或分别安装
npm install           # 根目录依赖
cd client && npm install  # 客户端依赖
cd server && npm install  # 服务端依赖
```

> **注意**: 项目已将依赖分离到不同目录，各自都有独立的package.json
> - 根目录package.json：共享工具和运行命令
> - client/package.json：客户端前端依赖
> - server/package.json：服务端后端依赖

## 开发环境启动

启动前后端开发环境：
```bash
npm run dev
```

单独启动前端开发服务器：
```bash
npm run dev:client
```

单独启动后端开发服务器：
```bash
npm run dev:server
```

## 生产环境部署

本项目提供了完整的部署文档，请参考 [详细部署指南](./docs/deploy/README.md)。

简要部署流程：

1. 准备服务器环境（Node.js, Nginx, PM2）

2. 后端服务部署
   ```bash
   # 构建服务端
   npm run build:server
   
   # 部署时只需要上传server目录及其package.json
   # 在服务器上安装服务端依赖
   cd server && npm install --production
   
   # 环境变量配置（两种选择）
   # 选项1：根目录启动 - 在应用根目录创建.env文件
   # 选项2：server目录启动 - 在server目录创建.env文件
   ```

3. 前端应用部署
   ```bash
   # 构建客户端
   npm run build:client
   
   # 部署时只需要上传构建后的client/dist目录
   ```

4. 配置Nginx和PM2

## 项目结构
```
web-panel/
├── public/            # 静态资源
├── client/            # 前端源码
│   ├── api/           # API请求
│   ├── assets/        # 资源文件
│   ├── components/    # 通用组件
│   ├── router/        # 路由配置
│   ├── styles/        # 全局样式
│   ├── utils/         # 工具函数
│   ├── views/         # 页面
│   ├── App.vue        # 根组件
│   └── main.js        # 入口文件
├── server/            # 后端服务
│   ├── src/           # 源代码
│   │   ├── config/    # 配置文件
│   │   ├── controllers/ # 控制器
│   │   ├── models/    # 数据模型
│   │   ├── routes/    # 路由
│   │   └── index.js   # 服务入口
│   └── dist/          # 构建后的后端代码
├── docs/              # 项目文档
│   ├── deploy/        # 部署文档
│   ├── api/           # API文档
│   └── guide/         # 用户指南
└── package.json       # 项目配置
```

## 文档索引

- [部署文档](./docs/deploy/README.md) - 详细的服务器部署指南
- [API文档](./docs/api/README.md) - 后端API接口文档
- [用户指南](./docs/guide/README.md) - 系统使用说明

## 开发团队

- 前端开发: xxx
- 后端开发: xxx
- 产品设计: xxx

## 版权信息

&copy; 2025 海聚科技
{"name": "web-panel", "version": "1.0.0", "description": "海聚科技设备管理系统", "scripts": {"dev:client": "cd client && npm run dev", "dev:server": "cd server && npm run dev", "dev": "concurrently \"npm run dev:client\" \"npm run dev:server\"", "build:client": "cd client && npm run build", "build:client:dev": "cd client && npm run build:dev", "build:client:prod": "cd client && npm run build:prod", "build:server": "cd server && npm run build", "build:all": "npm run build:client && npm run build:server", "build:all:prod": "npm run build:client:prod && npm run build:server", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write .", "test": "concurrently \"cd client && npm test\" \"cd server && npm test\"", "docs:dev": "vuepress dev docs", "docs:build": "vuepress build docs", "start": "cd server && npm run start", "install:all": "npm install && cd client && npm install && cd ../server && npm install"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^8.31.1", "@vuepress/client": "2.0.0-beta.67", "concurrently": "^9.1.2", "eslint": "^9.25.1", "eslint-plugin-vue": "^10.1.0", "prettier": "^3.2.5", "vuepress": "2.0.0-beta.67"}}